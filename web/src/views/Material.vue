<template>
  <div class="material-container">
    <el-form :model="form" ref="form" label-width="80px" label-position="left" class="demo-ruleForm">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-form-item :label="$t('material.form.name')" prop="name">
            <el-input v-model="form.name" :placeholder="$t('material.dialog.form.namePlaceholder')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item :label="$t('material.table.created_at')" prop="date">
            <el-date-picker v-model="form.date" value-format="timestamp" type="daterange"
              :range-separator="$t('public.to')" :start-placeholder="$t('public.startDate')"
              :end-placeholder="$t('public.endDate')">
            </el-date-picker>
          </el-form-item>
        </el-col>

      </el-row>
      <el-row type="flex" justify="space-between">
        <el-button type="primary" icon="el-icon-plus" @click="add()" size="mini">{{ $t("material.button.add")
        }}</el-button>
        <el-col :span="4">
          <el-button @click="resetForm('form', 'getList')" size="mini">{{
            $t("public.reset")
          }}</el-button>
          <el-button type="primary" @click="searchForm()" size="mini">{{
            $t("public.search")
          }}</el-button>
        </el-col>
      </el-row>
    </el-form>

    <div style="height: 60vh; background-color: #ccc; margin: 10px 0">
      <el-table v-loading="isLoading" :data="dataList" style="width: 100%" border height="100%">
        <el-table-column prop="num" :label="$t('material.table.num')" width="120" align="center">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" :label="$t('material.table.name')" width="180" align="center"></el-table-column>
        <el-table-column prop="type" :label="$t('material.table.type')" align="center">
          <template slot-scope="scope">
            {{ getTypeName(scope.row.type) }}
          </template>
        </el-table-column>
        <el-table-column prop="path" :label="$t('material.table.preview')" align="center">
          <template slot-scope="scope">
            <el-image v-if="scope.row.type == 1 && scope.row.path" :src="imageUrl + scope.row.path" class="img"
              :preview-src-list="[imageUrl + scope.row.path]" fit="cover"></el-image>
            <video class="img" v-else-if="scope.row.type == 2 && scope.row.path" :src="imageUrl + scope.row.path"
              controls></video>
            <div v-else-if="scope.row.type == 3 && scope.row.path" class="file-preview">
              <i :class="getFileIconClass(scope.row.path)"></i>
              <div class="file-name">{{ getFileName(scope.row.path) }}</div>
            </div>
            <div v-else>--</div>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" :label="$t('material.table.created_at')" align="center">
          <template slot-scope="scope">
            {{ $formatTimeStamp(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('material.table.operation')" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="edit(scope.row)">{{
              $t("public.edit")
            }}</el-button>
            <el-popconfirm :title="$t('material.table.sureToDelete')" style="margin-left: 10px"
              @confirm="del(scope.row.id)">
              <el-button type="text" style="color: #ff0000" size="small" slot="reference">{{ $t("public.delete")
              }}</el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-row :gutter="20" type="flex" justify="end">
      <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page.sync="pageNum" :page-sizes="[10, 20, 50]" :page-size="pageSize" layout="total, prev, pager, next"
        :total="total">
      </el-pagination>
    </el-row>

    <el-dialog :title="isEdit
      ? $t('material.dialog.title.editMaterial')
      : $t('material.dialog.title.addMaterial')
      " :visible.sync="isShow" width="600px" @close="close" :close-on-click-modal="false"
      :close-on-press-escape="false" :show-close="false">
      <el-form :model="addForm" :rules="rules" ref="addForm" label-width="80px" label-position="left"
        class="demo-ruleForm">
        <el-row :gutter="20">
          <el-col :span="15">
            <el-form-item :label="$t('material.dialog.form.name')" prop="name">
              <el-input v-model="addForm.name" :placeholder="$t('material.dialog.form.namePlaceholder')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="15">
            <el-form-item :label="$t('material.dialog.form.type')" prop="type" required>
              <el-radio-group v-model="addForm.type" direction="vertical" @change="changeType">
                <el-radio :label="1">{{ $t("material.table.image") }}</el-radio>
                <el-radio :label="2">{{ $t("material.table.video") }}</el-radio>
                <el-radio :label="3">{{ $t("material.table.file") }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('material.dialog.form.upload')" prop="path">
              <fileUpload :uploadNum="1" name="path" :fileType="getFileType(addForm.type)" @uploadStatus="uploadStatus"
                :acceptFileType="acceptFileType" :fileList="fileList" @editUrl="editUrl" @fileData="fileData">
              </fileUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShow = false" :loading="uploading">{{ $t("public.cancel") }}</el-button>
        <el-button type="primary" @click="save()" :loading="uploading">{{
          $t("public.confirm")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getList, add, edit, del } from "@/api/material.js";
import fileUpload from "@/components/file-upload.vue";
export default {
  components: { fileUpload },
  data () {
    return {
      uploading: false,
      acceptFileType: "image/*",
      dataList: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      form: {
        name: "",
        date: [],
        type: ""
      },
      addForm: {
        name: "",
        type: 1,
        path: "",
      },
      fileList: [],
      upfileData: {
        width: 0,
        height: 0,
      },
      id: "",
      imageUrl: process.env.VUE_APP_BASE_API + "assets/media/",
      rules: {
        name: [
          {
            required: true,
            message: this.$i18n.t("material.dialog.form.namePlaceholder"),
            trigger: "blur",
          },
        ],
        path: [
          {
            required: true,
            message: this.$i18n.t("material.dialog.form.pathPlaceholder"),
            trigger: "change",
          },
        ],
      },
      isShow: false,
      isEdit: false,
      isLoading: false,
    };
  },
  created () {
    this.getList();
  },
  methods: {
    fileData (obj) {
      console.log(obj, "upfileData");
      this.upfileData = obj;


    },
    uploadStatus (status) {
      this.uploading = status;
    },
    changeType (type) {
      switch (type) {
        case 1:
          this.acceptFileType = "image/*";
          break;
        case 2:
          this.acceptFileType = "video/mp4,video/ogg,video/mov,video/webm";
          break;
        case 3:
          this.acceptFileType = "application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation";
          break;
        default:
          break;
      }

    },
    getList () {
      this.isLoading = true;
      getList({
        page: this.pageNum,
        pageSize: this.pageSize,
        name: this.form.name,
        type: this.form.type,
        created_at_start:
          this.form.date.length > 0 ? this.form.date[0] / 1000 : "",
        created_at_end:
          this.form.date.length > 0 ? this.form.date[1] / 1000 : "",
      }).then((res) => {
        if (res.code == 0) {
          this.dataList = res.data.data;
          this.total = res.data.total;
          this.isLoading = false;
        }
      });
    },
    //新建模板
    add () {
      this.isShow = true;
      this.isEdit = false;
      this.addForm = {
        name: "",
        type: 1,
        path: ""
      };
      this.fileList = [];
    },
    edit (row) {
      this.fileList = [];
      this.id = row.id;
      this.addForm.name = row.name;
      this.addForm.type = row.type;
      this.addForm.path = row.path;
      this.isEdit = true;
      this.isShow = true;
      this.fileList = [
        {
          url: row.path,
          hover: false,
          name: this.getFileName(row.path)
        },
      ];
    },
    save () {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          // 验证文件类型是否匹配
          if (this.fileList.length === 0) {
            this.$message.error(this.$i18n.t("material.dialog.form.selectFile"));
            return;
          }

          const fileType = this.getFileType(this.addForm.type);
          const fileUrl = this.fileList[0].url;

          if (fileType === 'image' && !this.isImageUrl(fileUrl)) {
            this.$message.error(this.$i18n.t("material.dialog.form.onlyImage"));
            return;
          }

          if (fileType === 'video' && !this.isVideoUrl(fileUrl)) {
            this.$message.error(this.$i18n.t("material.dialog.form.onlyVideo"));
            return;
          }

          if (fileType === 'file' && !this.isAllowedFileUrl(fileUrl)) {
            this.$message.error(this.$i18n.t("material.dialog.form.onlyFile"));
            return;
          }

          const formData = {
            type: this.addForm.type,
            path: this.addForm.path,
            name: this.addForm.name,
            source_width: this.upfileData.width,
            source_height: this.upfileData.height,
          };

          if (this.isEdit) {
            edit(formData, this.id).then((res) => {
              this.$message({
                type: "success",
                message: this.$i18n.t("public.editSuccess"),
              });
              this.$refs.addForm.resetFields();
              this.isShow = false;
              this.getList();
            });
          } else {
            add(formData).then((res) => {
              this.$message({
                type: "success",
                message: this.$i18n.t("public.addSuccess"),
              });
              this.$refs.addForm.resetFields();
              this.isShow = false;
              this.getList();
            });
          }
        }
      });
    },
    isImageUrl (url) {
      if (!url) return false;
      const fileSuffix = url.split(".").pop().toLowerCase();
      return ["jpg", "jpeg", "png", "gif", "bmp"].includes(fileSuffix);
    },
    isVideoUrl (url) {
      if (!url) return false;
      const fileSuffix = url.split(".").pop().toLowerCase();
      return ["mp4", "avi", "mov", "webm"].includes(fileSuffix);
    },
    isAllowedFileUrl (url) {
      if (!url) return false;
      const fileSuffix = url.split(".").pop().toLowerCase();
      return ["pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx"].includes(fileSuffix);
    },

    del (id) {
      del(id).then((res) => {
        this.$message({
          type: "success",
          message: this.$i18n.t("public.deleteSuccess"),
        });
        this.getList();
      });
    },
    editUrl (data) {
      this.fileList = [data];
      this.addForm.path = data.url;
    },
    close () {
      if (this.uploading) return;
      this.addForm = {
        name: "",
        type: 1,
        path: "",
      };
      this.fileList = [];
    },
    //搜索
    searchForm () {
      this.getList();
    },
    //重置
    resetForm () {
      this.pageNum = 1;
      this.$refs.form.resetFields();
      this.getList();
    },
    handleSizeChange (val) {
      this.pageNum = 1;
      this.pageSize = val;
      this.getList();
    },
    handleCurrentChange (val) {
      this.pageNum = val;
      this.getList();
    },
    getTypeName (type) {
      const types = {
        1: this.$t("material.table.image"),
        2: this.$t("material.table.video"),
        3: this.$t("material.table.file")
      };
      return types[type] || "-";
    },
    getFileIconClass (url) {
      if (!url) return "el-icon-document";
      const ext = url.split(".").pop().toLowerCase();
      switch (ext) {
        case "pdf":
          return "el-icon-document-pdf";
        case "doc":
        case "docx":
        case "xls":
        case "xlsx":
        case "ppt":
        case "pptx":
          return "el-icon-document";
        case "jpg":
        case "jpeg":
        case "png":
        case "gif":
        case "bmp":
          return "el-icon-picture";
        case "mp4":
        case "avi":
        case "mov":
        case "webm":
          return "el-icon-video-camera";
        default:
          return "el-icon-document";
      }
    },
    getFileName (url) {
      if (!url) return "";
      return url.split("/").pop();
    },
    getFileType (type) {
      switch (type) {
        case 1: return "image";
        case 2: return "video";
        case 3: return "file";
        default: return "image";
      }
    }
  },
};
</script>

<style scoped>
.flex {
  display: flex;
}

.img {
  width: 60px;
  height: 60px;
}

.file-preview {
  width: 60px;
  height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px dashed #eaeaea;
  border-radius: 5px;

  i {
    font-size: 24px;
    color: #409EFF;
    margin-bottom: 5px;
  }

  .file-name {
    max-width: 50px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 10px;
    color: #606266;
  }
}
</style>