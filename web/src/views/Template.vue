<template>
  <div class="template-container">
    <el-form :model="form" ref="form" label-width="80px" label-position="left" class="demo-ruleForm">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-form-item :label="$t('template.form.name')" prop="name">
            <el-input v-model="form.name" :placeholder="$t('template.form.namePlaceholder')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item :label="$t('template.form.date')">
            <el-date-picker v-model="form.date" type="daterange" :range-separator="$t('public.to')"
              :start-placeholder="$t('public.startDate')" :end-placeholder="$t('public.endDate')">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row type="flex" justify="space-between">
        <el-button type="primary" icon="el-icon-plus" @click="add()" size="mini">{{ $t("template.button.create")
        }}</el-button>


        <el-col :span="4">
          <el-button @click="resetForm()" size="mini">{{
            $t("public.reset")
          }}</el-button>
          <el-button type="primary" @click="searchForm()" size="mini">{{
            $t("public.search")
          }}</el-button>
        </el-col>
      </el-row>
    </el-form>

    <div style="height: 60vh; background-color: #ccc; margin: 10px 0">
      <el-table :data="dataList" style="width: 100%" border height="100%">
        <el-table-column prop="num" :label="$t('template.table.num')" width="120" align="center">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" :label="$t('template.table.name')" width="180" align="center"></el-table-column>
        <el-table-column prop="created_at" :label="$t('template.table.created_at')" align="center">
          <template slot-scope="scope">
            {{ $formatTimeStamp(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('public.operation')" fixed="right" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="zipPackage(scope.row.id, scope.row.type)">{{
              $t("template.dialog.title.pack") }}</el-button>
            <el-button type="text" size="small" @click="edit(scope.row)" v-if="scope.row.type === 1">{{
              $t("public.edit") }}</el-button>
            <el-button type="text" size="small" @click="editWorkTemplate(scope.row)" v-if="scope.row.type === 2">
              {{ $t("public.edit") }}
            </el-button>
            <el-popconfirm :title="$t('template.table.sureToDelete')" style="margin-left: 10px"
              @confirm="del(scope.row.id)">
              <el-button type="text" size="small" slot="reference" style="color: #ff0000">{{ $t("public.delete")
              }}</el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-row :gutter="20" type="flex" justify="end">
      <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page.sync="pageNum" :page-sizes="[10, 20, 50]" :page-size="pageSize" layout="total, prev, pager, next"
        :total="total">
      </el-pagination>
    </el-row>
    <el-dialog :title="isEdit
      ? $t('template.dialog.title.edit')
      : $t('template.dialog.title.add')
      " :visible.sync="isShowTemplate" width="1700px" @close="close" :close-on-click-modal="false"
      :close-on-press-escape="false">
      <!-- 功能区域 -->
      <div class="flex">
        <el-button type="primary" @click="addMaterial(4)" plain size="mini">{{
          $t("template.button.setBackground")
        }}</el-button>
        <el-button type="primary" @click="addMaterial(1)" plain size="mini">{{
          $t("template.button.addImage")
        }}</el-button>
        <el-button type="primary" @click="addMaterial(2)" plain size="mini">{{
          $t("template.button.addVideo")
        }}</el-button>
        <el-button type="primary" @click="addMaterial(3)" plain size="mini">{{
          $t("template.button.dateTime")
        }}</el-button>
        <el-button type="primary" @click="addMaterial(5)" plain size="mini">{{
          $t("template.button.iframe")
        }}</el-button>
        <!-- 新增背景图设置面板 -->
        <el-select v-model="templatePages[currentPage].backgroundSettings.backgroundSize" placeholder="背景图显示方式"
          size="mini" style="margin-left: 10px">
          <el-option :label="$t('template.background.repeat')" value="repeat"></el-option>
          <el-option :label="$t('template.background.cover')" value="cover"></el-option>
          <el-option :label="$t('template.background.contain')" value="contain"></el-option>
          <el-option :label="$t('template.background.auto')" value="auto"></el-option>
        </el-select>

        <el-button v-if="templatePages[currentPage].backgroundUrl" type="info" @click="clearBackground()" plain
          size="mini">{{ $t("template.button.clearBackground") }}</el-button>
        <el-button type="info" @click="clearTemplate" plain size="mini">{{
          $t("public.reset")
        }}</el-button>
        <el-button type="primary" icon="el-icon-plus" @click="addNewPage()" size="mini">{{
          $t("template.button.addNewPage")
        }}</el-button>
        <el-button @click="prevPage()" :disabled="currentPage === 0" size="mini">{{ $t("template.button.prevPage")
        }}</el-button>
        <el-button @click="nextPage()" :disabled="currentPage === templatePages.length - 1" size="mini">{{
          $t("template.button.nextPage") }}</el-button>
        <el-button @click="delPage()" :disabled="templatePages.length === 1" size="mini">{{
          $t("template.button.delPage") }}</el-button>
        <span class="ml-large">{{ $t("template.dialog.pageInfo.currentPage") }}
          {{ currentPage + 1 }} {{ $t("template.dialog.pageInfo.totalPages") }}
          {{ templatePages.length }} {{ $t("template.button.page") }}</span>
      </div>
      <div class="container">
        <!-- 模板区域 -->
        <div class="middle-pane" :style="{
          backgroundImage: templatePages[currentPage].backgroundUrl
            ? `url(${imageUrl + templatePages[currentPage].backgroundUrl})`
            : 'none',
          backgroundSize:
            (templatePages[currentPage].backgroundSettings || {})
              .backgroundSize === 'repeat'
              ? 'auto'
              : (templatePages[currentPage].backgroundSettings || {})
                .backgroundSize || 'cover',
          backgroundRepeat:
            (templatePages[currentPage].backgroundSettings || {})
              .backgroundSize === 'repeat'
              ? 'repeat'
              : 'no-repeat',
          backgroundPosition:
            (templatePages[currentPage].backgroundSettings || {})
              .backgroundPosition || 'center center',
        }" ref="middlePane">

          <draggable-resizable v-for="(item, index) in templatePages[currentPage].materialList" :key="index"
            :snapToGrid="true" :x="item.x_axis < 0 ? 0 : item.x_axis" :y="item.y_axis < 0 ? 0 : item.y_axis"
            :w="item.template_sm_type == 2 ? 240 : item.width ? item.width : (item.source_width / 3)"
            :h="item.template_sm_type == 2 ? 50 : item.height ? item.height : (item.source_height / 3)"
            :min-width="item.template_sm_type == 2 ? 140 : (item.source_width / 3)" :snap="true" :snapTolerance="10"
            :min-height="item.template_sm_type == 2 ? 50 : (item.source_height / 3)" :dragging="true" :resizing="true"
            :lock-aspect-ratio="isLock(item.template_sm_type)" :parent="true"
            @dragging="(left, top) => handleDragging(left, top, index)" class="draggable" @resizing="
              (left, top, width, height) =>
                handleResizing(left, top, width, height, index)
            ">

            <img style="width: 100%; height: 100%" v-if="item.type == 1 && item.template_sm_type == 1"
              :src="imageUrl + item.path" alt="Image" class="media" />
            <video style="width: 100%; height: 100%" v-if="item.type == 2 && item.template_sm_type == 1"
              :src="imageUrl + item.path" class="media"></video>
            <dateTime v-if="item.template_sm_type == 2"></dateTime>
            <div v-if="item.template_sm_type == 5" style="width: 100%; height: 100%; position: relative;">
              <div style="position: absolute; width: 100%; height: 100% ;top: 0px; left: 0px"></div>
              <iframe :src="item.url" width="100%" height="100%" frameborder="0"
                sandbox="allow-same-origin;allow-scripts;allow-forms"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"></iframe>
            </div>

            <img class="del" @click="delMaterial(index)" :src="require('@/assets/delete.png')" alt="" />
          </draggable-resizable>
        </div>

        <!-- 视图列表区域 -->
        <div class="right-pane">
          <el-form :model="sharedAddForm" :rules="rules" ref="addForm" label-width="200px" label-position="left"
            class="demo-ruleForm">
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item :label="$t('template.form.name')" prop="name">
                  <el-input v-model="sharedAddForm.name" :placeholder="$t('template.form.namePlaceholder')"
                    class="uniform-width"></el-input>
                </el-form-item>

                <el-form-item :label="$t('template.form.resolutionRatio')" prop="resolution_ratio" required
                  label-width="200px">
                  <el-select v-model="sharedAddForm.resolution_ratio" :placeholder="$t('template.form.resolutionRatioPlaceholder')
                    " class="uniform-width">
                    <el-option v-for="item in resolutionRatioList" :key="item.id" :label="item.name"
                      :value="item.name"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item :label="$t('template.form.swipterTime')" prop="swipter_time" label-width="200px">
                  <el-input v-model.number="sharedAddForm.swipter_time" :placeholder="$t('template.form.swipterTime')"
                    class="" style="width: 70%;"></el-input>秒
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <!-- <div v-for="(item, index) in templatePages[currentPage].materialList" :key="index" class="view-item">
            <div>
              {{ $t("template.table.name") }}:
              {{
                item.template_sm_type == 1
                  ? item.sm_name
                  : $t("template.dialog.materialType.dateTime")
              }}
            </div>
            <div>
              {{ $t("template.table.type") }}:
              {{
                item.type == 1 && item.template_sm_type == 1
                  ? $t("template.dialog.materialType.image")
                  : item.type == 2 && item.template_sm_type == 1
                    ? $t("template.dialog.materialType.video")
                    : $t("template.dialog.materialType.dateTime")
              }}
            </div>
            <div>W: {{ item.width }}px</div>
            <div>H: {{ item.height }}px</div>
            <div>X: {{ item.x_axis }}px</div>
            <div>Y: {{ item.y_axis }}px</div>
          </div> -->
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowTemplate = false">{{
          $t("public.cancel")
        }}</el-button>
        <el-button type="primary" @click="save()">{{
          $t("public.confirm")
        }}</el-button>
      </span>
    </el-dialog>
    <!-- 选择素材图片弹窗 -->
    <el-dialog title="添加" :append-to-body="true" style="margin-top: 100px" :visible.sync="isShowMaterial" width="80%"
      @close="close1">
      <el-table v-loading="isLoading" ref="singleTable" tooltip-effect="dark" @select="selectChange"
        @row-click="selectChange" @select-all="selectAllChange" :data="materialdDataList" style="width: 100%" border>
        <el-table-column type="selection" width="40"> </el-table-column>
        <el-table-column prop="name" :label="$t('material.table.name')" width="180" align="center"></el-table-column>
        <el-table-column prop="type" :label="$t('material.table.type')" align="center">
          <template slot-scope="scope">
            {{
              scope.row.type === 1
                ? $t("template.dialog.materialType.image")
                : scope.row.type === 2
                  ? $t("template.dialog.materialType.video")
                  : $t("template.dialog.materialType.file")
            }}
          </template>
        </el-table-column>
        <el-table-column prop="path" :label="$t('material.table.preview')" align="center">
          <template slot-scope="scope">
            <el-image v-if="scope.row.path && scope.row.type == 1" :src="imageUrl + scope.row.path" class="img"
              :preview-src-list="[imageUrl + scope.row.path]" fit="cover"></el-image>
            <video class="img" v-else :src="imageUrl + scope.row.path" controls></video>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" :label="$t('template.table.created_at')" align="center">
          <template slot-scope="scope">
            {{ $formatTimeStamp(scope.row.created_at) }}
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowMaterial = false">{{
          $t("public.cancel")
        }}</el-button>
        <el-button type="primary" @click="confirmMaterial">{{
          $t("public.confirm")
        }}</el-button>
      </span>
    </el-dialog>
    <!-- 新增工作模板弹窗 -->
    <el-dialog :title="isEditWorkTemplate
      ? $t('template.button.updateWorkTemplate')
      : $t('template.button.createWorkTemplate')
      " :visible.sync="isShowWorkTemplate" style="margin-top: 300px" width="20%" @close="handleWorkTemplateClose">
      <el-form :model="workTemplateForm" :rules="workTemplateRules" ref="workTemplateFormRef" label-width="100px"
        class="demo-ruleForm">
        <el-form-item :label="$t('template.form.name')" prop="name" required>
          <el-input v-model="workTemplateForm.name" :placeholder="$t('template.form.namePlaceholder')"></el-input>
        </el-form-item>

        <el-form-item :label="$t('template.dialog.title.material')" required>
          <el-button type="primary" @click="selectFile" style="width: 50%">
            {{ $t("template.dialog.title.material") }}
          </el-button>
          <div class="selected-material-count">
            {{ $t("template.dialog.title.material") }}：{{ selectedRows.name || selectedRows.sm_name || " " }}
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowWorkTemplate = false">
          {{ $t("public.cancel") }}
        </el-button>
        <el-button type="primary" @click="handleSaveWorkTemplate">
          {{ $t("public.confirm") }}
        </el-button>
      </span>
    </el-dialog>


    <!-- iframe填写弹窗 -->
    <el-dialog :visible.sync="isShowIframe" style="padding-top:300px" width="40%">
      <el-form :model="iframeeForm" ref="iframeRef" label-width="120px" class="iframe-dialog-form">
        <el-form-item :label="$t('template.form.iframeUrl')" prop="url" required>
          <el-input v-model="iframeeForm.url" :placeholder="$t('template.form.urlPlaceholder')"></el-input>
        </el-form-item>

      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowIframe = false">
          {{ $t("public.cancel") }}
        </el-button>
        <el-button type="primary" @click="handleIframeTemplate">
          {{ $t("public.confirm") }}
        </el-button>
      </span>
    </el-dialog>



    <!-- 打包弹窗 -->
    <el-dialog :title="$t('template.dialog.title.pack')" style="margin-top: 100px" :visible.sync="isShowPack"
      width="30%" @close="
        packForm.resource_pack_name = '';
      packForm.name = '';
      ">
      <el-form :model="packForm" :rules="packRule" style="padding: 20px" ref="packForm" label-width="100px"
        label-position="left" class="demo-ruleForm">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="$t('template.form.resourcePackName')" prop="resource_pack_name">
              <el-input v-model="packForm.resource_pack_name"
                :placeholder="$t('template.form.namePlaceholder')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('template.form.resourcePackAlias')" prop="name">
              <el-input v-model="packForm.name"
                :placeholder="$t('template.form.resourcePackAliasPlaceholder')"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShowPack = false">{{
          $t("public.cancel")
        }}</el-button>
        <el-button type="primary" @click="confirmPack()">{{
          $t("public.confirm")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import DraggableResizable from "vue-draggable-resizable";
import dateTime from "@/components/dateTime.vue";
import "vue-draggable-resizable/dist/VueDraggableResizable.css";
import { zipPack } from "@/utils/zipPack";
import {
  getList,
  getMaterialList,
  getDetail,
  add,
  edit,
  del,
  savePack,
} from "@/api/template.js";

export default {
  components: { DraggableResizable, dateTime },
  data () {
    return {
      isShowIframe: false,

      isEditWorkTemplate: false, // 新增：工作模板编辑状态
      isMultiSelect: false, // 标记是否为多选模式
      selectedRows: [], // 存储多选结果
      isShowWorkTemplate: false,
      workTemplateForm: {
        name: "", // 模板名称
      },
      iframeeForm: {
        name: "", // 模板名称
      },
      workTemplateRules: {
        name: [{ required: true, message: this.$i18n.t("template.form.namePlaceholder"), trigger: "blur" }],
      },
      dataList: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      pageNumM: 1,
      pageSizeM: 10,
      totalM: 0,
      // 在 data 中定义一个变量来跟踪当前页面的背景设置
      currentPageBackgroundSettings: null,
      form: {
        name: "",
        date: [],
      },
      templatePages: [
        {
          backgroundUrl: "",
          materialList: [],
          backgroundList: [],
          backgroundSettings: {
            backgroundSize: "cover",
            backgroundPosition: "center center",
          },
        },
      ],
      currentPage: 0,
      sharedAddForm: {
        name: "",
        resolution_ratio: "1920x1080",
        swipter_time: -1,
      },
      packForm: {
        resource_pack_name: "",
        name: "",
        html_content: "",
      },
      resolutionRatioList: [
        {
          id: 0,
          name: "1920x1080",
        },
        {
          id: 1,
          name: "1440x900",
        },
        {
          id: 2,
          name: "1280x1024",
        },
        {
          id: 3,
          name: "1024x768",
        },
      ],
      rules: {
        name: [
          {
            required: true,
            message: this.$i18n.t("template.form.namePlaceholder"),
            trigger: "blur",
          },
        ],
        resolution_ratio: [
          {
            required: true,
            message: this.$i18n.t("template.form.resolutionRatioPlaceholder"),
            trigger: "change",
          },
        ],
      },
      packRule: {
        name: [
          {
            required: false,
            message: this.$i18n.t("template.form.resourcePackAliasPlaceholder"),
            trigger: "blur",
          },
        ],
        resource_pack_name: [
          {
            required: true,
            message: this.$i18n.t("template.form.resourcePackNamePlaceholder"),
            trigger: "blur",
          },
        ],
      },
      isLoading: false,
      isShowTemplate: false,
      isShowMaterial: false,
      isShowPack: false,
      isEdit: false,
      type: 1,
      selectedRow: null,
      templateId: "",
      imageUrl: process.env.VUE_APP_BASE_API + "assets/media/",
      materialdDataList: [],
    };
  },
  computed: {},
  created () {
    this.getList();
    this.currentPageBackgroundSettings =
      this.templatePages[this.currentPage].backgroundSettings;
  },
  methods: {
    onResize (handle, x, y, width, height) {
      console.log(handle, x, y, width, height, "onResize");

    },

    getMaterialName (materialId) {
      const material = this.materialdDataList.find(
        (item) => item.id === materialId
      );
      return material ? material.name : "未知素材";
    },
    removeSelectedMaterial (materialId) {
      this.workTemplateForm.selectedMaterials =
        this.workTemplateForm.selectedMaterials.filter(
          (id) => id !== materialId
        );
    },
    getList () {
      this.isLoading = true;
      getList({
        page: this.pageNum,
        pageSize: this.pageSize,
        name: this.form.name,
        created_at_start:
          this.form.date.length > 0 ? this.form.date[0] / 1000 : "",
        created_at_end:
          this.form.date.length > 0 ? this.form.date[1] / 1000 : "",
      }).then((res) => {
        if (res.code === 0) {
          this.dataList = res.data.data;
          this.total = res.data.total;
          this.isLoading = false;
        }
      });
    },
    isLock (type) {
      if (type === 5) {
        return false;
      } else {
        return true;
      }
    },
    handleIframeTemplate () {
      this.iframeeForm.url
      this.templatePages[this.currentPage].materialList.push({
        type: 5,
        url: this.iframeeForm.url,
        template_sm_type: 5,
        source_width: 500,
        source_height: 300,

      })
      this.isShowIframe = false;
    },
    // 新增：打开工作模板弹窗
    addwork () {
      //  this.$refs.workTemplateFormRef.resetFields();
      this.isShowWorkTemplate = true;
      this.isMultiSelect = true; // 启用多选模式
      // 重置表单
      // this.workTemplateForm.name = "";
    },

    handleSaveWorkTemplate () {
      this.$refs.workTemplateFormRef.validate((valid) => {
        if (valid) {
          const api = this.isEditWorkTemplate ? edit : add;
          const params = {
            name: this.workTemplateForm.name,
            type: 2,
            template_sm: this.workTemplateForm.template_sm,
          };

          // 编辑时传递 ID，新建时不需要
          const request = this.isEditWorkTemplate
            ? api(params, this.id)
            : api(params);

          request.then(() => {
            this.$message.success(
              this.isEditWorkTemplate
                ? this.$t("public.editSuccess")
                : this.$t("public.addSuccess")
            );
            this.isShowWorkTemplate = false;
            this.getList(); // 刷新列表
          });
        }
      });
    },
    editWorkTemplate (row) {
      this.id = row.id;
      this.isEditWorkTemplate = true; // 标记为编辑模式
      this.isShowWorkTemplate = true; // 打开工作模板窗口

      // 获取工作模板详情
      getDetail({ id: row.id, type: 2 }).then((res) => {
        if (res.code === 0) {
          // 填充表单数据
          this.workTemplateForm.name = res.data.name;

          // 处理已选素材（根据实际数据结构调整）
          if (res.data.template_sm && res.data.template_sm.length > 0) {
            this.workTemplateForm.template_sm = res.data.template_sm;
            this.selectedRows = res.data.template_sm[0]; // 假设第一个为选中素材
          }
          console.log("dddd", this.selectedRows);
        }
      });
    },
    // 新增：关闭弹窗时的重置
    handleWorkTemplateClose () {
      this.isShowWorkTemplate = false;
      this.workTemplateForm.name = "";
      this.workTemplateForm = { name: "" }; // 清空表单
      this.selectedRows = []; // 清空选中素材
    },

    getMaterialList (type) {
      getMaterialList({
        page: this.pageNumM,
        pageSize: this.pageSizeM,
        name: this.form.name,
        type: type,
      }).then((res) => {
        if (res.code === 0) {
          this.materialdDataList = res.data.data;
          this.totalM = res.data.total;
        }
      });
    },
    clearBackground () {
      this.templatePages[this.currentPage].backgroundUrl = "";
      this.templatePages[this.currentPage].backgroundList = [];
    },

    // 在 getDetail 方法中更新 currentPageBackgroundSettings
    async getDetail (id, type) {
      if (type === 1) {
        return new Promise((resolve, reject) => {
          getDetail({
            id,
          }).then((res) => {
            if (res.code === 0) {
              resolve(res.data.template_sm);
            }
          });
        });
      } else {
        getDetail({
          id,
          type: 2,
        }).then((res) => {
          if (res.code === 0) {
            this.sharedAddForm.name = res.data.name;
            this.sharedAddForm.resolution_ratio = res.data.resolution_ratio;
            this.sharedAddForm.swipter_time = res.data.swipter_time || -1;

            // 清空现有页面
            this.templatePages = [];

            res.data.template_sm.forEach((item) => {

              const pageIndex = item.template_page - 1;

              // 确保页面存在并初始化所有属性
              if (!this.templatePages[pageIndex]) {
                this.templatePages[pageIndex] = {
                  backgroundUrl: "",
                  materialList: [],
                  backgroundList: [],
                  backgroundSettings: {
                    backgroundSize: "cover", // 默认值
                    backgroundPosition: "center center",
                  },
                };
              }

              // 处理素材
              if (item.template_sm_type === 3) {
                // 恢复背景图和背景设置
                this.templatePages[pageIndex].backgroundList.push(item);
                this.templatePages[pageIndex].backgroundUrl = item.path;

                // 如果后端有保存背景显示设置，则恢复
                if (item.background_display) {
                  this.templatePages[
                    pageIndex
                  ].backgroundSettings.backgroundSize = item.background_display;
                }
              } else {
                this.templatePages[pageIndex].materialList.push(item);
              }
            });

            // 更新 currentPageBackgroundSettings
            this.currentPageBackgroundSettings =
              this.templatePages[this.currentPage].backgroundSettings;
          }
        });
      }
    },

    // 新建模板
    add () {
      this.close();
      this.isShowTemplate = true;
    },
    edit (row) {
      if (row.type === 1) {
        this.id = row.id;
        this.getDetail(row.id);
        this.isEdit = true;
        this.isShowTemplate = true;

      }
    },
    del (id) {
      del(id).then((res) => {
        this.$message({
          type: "success",
          message: this.$i18n.t("public.deleteSuccess"),
        });
        this.getList();
      });
    },
    close () {
      this.templatePages = [
        {
          backgroundUrl: "",
          materialList: [],
          backgroundList: [],
          backgroundSettings: {
            backgroundSize: "cover",
            backgroundPosition: "center center",
          },
        },
      ];
      this.currentPage = 0;
      this.sharedAddForm.name = "";
      this.sharedAddForm.resolution_ratio = "1920x1080";
      this.sharedAddForm.swipter_time = -1;
      this.isEdit = false;
    },
    close1 () { },
    // 清空重置模板
    clearTemplate () {
      this.templatePages[this.currentPage].backgroundList = [];
      this.templatePages[this.currentPage].materialList = [];
    },
    // 搜索
    searchForm () {
      this.getList();
    },
    // 重置
    resetForm () {
      this.pageNum = 1;
      this.$refs.form.resetFields();
      this.getList();
    },
    handleSizeChange (val) {
      this.pageNum = 1;
      this.pageSize = val;
      this.getList();
    },
    handleCurrentChange (val) {
      this.pageNum = val;
      this.getList();
    },
    // 单选框选中的数据
    // 重写选择事件处理
    selectChange (selection) {
      console.log(selection, "selectionselectionselectionselection");

      // if (this.isMultiSelect) {
      // //   // 多选模式：存储所有选中项
      // //   this.selectedRows = selection;
      // } else {
      // 单选模式：保持原有逻辑
      if (selection.length > 1) {
        const del_row = selection.shift();
        this.$refs.singleTable.toggleRowSelection(del_row, false);
      }


      if (this.isMultiSelect) {
        this.selectedRows = selection[0];
      } else {
        this.selectedRow = selection[0];
      }

      // }
    },

    // 新增：处理全选事件
    selectAllChange (selection) {
      if (this.isMultiSelect) {
        this.selectedRows = selection;
      }
    },
    // 删除素材
    delMaterial (index) {
      try {
        if (this.templatePages[this.currentPage]?.materialList?.[index]) {
          this.templatePages[this.currentPage].materialList.splice(index, 1);
          this.$message({
            type: 'success',
            message: this.$i18n.t('public.deleteSuccess')
          });
          // 强制触发Vue响应式更新
          this.$forceUpdate();
        } else {
          this.$message({
            type: 'warning',
            message: this.$i18n.t('template.error.materialNotFound')
          });
        }
      } catch (error) {
        console.error('删除素材失败:', error);
        this.$message({
          type: 'error',
          message: this.$i18n.t('public.deleteFailed')
        });
      }
    },
    // 修改 addMaterial 方法
    addMaterial (type) {
      this.type = type;


      // 确保templatePages和当前页已初始化
      if (!this.templatePages) {
        this.templatePages = [{
          backgroundUrl: "",
          materialList: [],
          backgroundList: [],
          backgroundSettings: {
            backgroundSize: "cover",
            backgroundPosition: "center center",
          },
        }];
      }
      if (!this.templatePages[this.currentPage]) {
        this.templatePages[this.currentPage] = {
          backgroundUrl: "",
          materialList: [],
          backgroundList: [],
          backgroundSettings: {
            backgroundSize: "cover",
            backgroundPosition: "center center",
          },
        };
      }
      if (!this.templatePages[this.currentPage].materialList) {
        this.templatePages[this.currentPage].materialList = [];
      }

      if (type === 4) {
        // 直接打开素材选择对话框
        this.getMaterialList(1);
        this.isShowMaterial = true;
      }
      else if (type === 3) {
        return this.addDateTime();
      }
      else if (type === 5) {
        this.isShowIframe = true;

      }
      else {
        this.getMaterialList(type);
        this.isShowMaterial = true;
      }
    },
    //获取弹窗
    selectFile () {
      // this.isMultiSelect = true; // 启用多选模式
      this.getMaterialList(3);
      this.isShowMaterial = true;
      console.log(this.isShowMaterial);
    },

    // 修改 confirmMaterial 方法
    // 在 el-select 的 change 事件中更新 currentPageBackgroundSettings
    handleBackgroundSizeChange (newValue) {
      this.templatePages[this.currentPage].backgroundSettings.backgroundSize =
        newValue;
      this.currentPageBackgroundSettings =
        this.templatePages[this.currentPage].backgroundSettings;
    },

    addDateTime () {
      console.log("addDateTime");

      console.log(this.templatePages[this.currentPage], "this.templatePages[this.currentPage]this.templatePages[this.currentPage]");

      const newTemplatePages = JSON.parse(JSON.stringify(this.templatePages));
      newTemplatePages[this.currentPage].materialList.push({
        template_id: 0,
        sm_id: 0,
        width: 0,
        height: 0,
        source_width: 0,
        source_height: 0,
        x_axis: 0,
        y_axis: 0,
        sm_name: "",
        path: "",
        type: 0,
        template_sm_type: 2,
        template_page: this.currentPage + 1,
        background_display: ""
      });
      this.templatePages = newTemplatePages;
    },



    confirmMaterial () {
      console.log(this.type, "this.type");

      // 确保templatePages和当前页已初始化
      if (!this.templatePages) {
        this.templatePages = [{
          backgroundUrl: "",
          materialList: [],
          backgroundList: [],
          backgroundSettings: {
            backgroundSize: "cover",
            backgroundPosition: "center center",
          },
        }];
      }
      if (!this.templatePages[this.currentPage]) {
        this.templatePages[this.currentPage] = {
          backgroundUrl: "",
          materialList: [],
          backgroundList: [],
          backgroundSettings: {
            backgroundSize: "cover",
            backgroundPosition: "center center",
          },
        };
      }
      if (!this.templatePages[this.currentPage].materialList) {
        this.templatePages[this.currentPage].materialList = [];
      }
      if (!this.templatePages[this.currentPage].backgroundList) {
        this.templatePages[this.currentPage].backgroundList = [];
      }

      if (this.isMultiSelect) {
        // 单选选逻辑：将选中的素材转换为指定格式
        if (!this.selectedRows) {
          this.$message.warning(this.$i18n.t("template.form.materialsPlaceholder"));
          return;
        }
        this.workTemplateForm.template_sm = [
          {
            type: 10, // 使用素材实际类型
            template_sm_type: 1, // 固定为1（根据您的示例）
            path: this.selectedRows.path, // 素材路径
            sm_id: this.selectedRows.id, // 素材ID
            sm_name: this.selectedRows.name, // 素材名称
            source_width: this.selectedRows.source_width, // 素材宽度
            source_height: this.selectedRows.source_height, // 素材高度
            x_axis: 0, // 固定值
            y_axis: 0, // 固定值
            width: 0, // 固定值
            height: 0, // 固定值
            template_page: 1, // 固定值
          },
        ];

        // 确保模板名称有值
        this.workTemplateForm.name = this.workTemplateForm.name || "";
        this.isShowMaterial = false;
      } else {
        // 单选逻辑：保持原有代码不变materialsPlaceholder
        if (!this.selectedRows) {
          this.$message.warning(this.$i18n.t("template.form.materialsPlaceholder"));
          return;
        }

        // 原有单选逻辑...
        if (this.type === 4) {
          // 设置背景图
          this.templatePages[this.currentPage].backgroundUrl =
            this.selectedRow.path;
          this.templatePages[this.currentPage].backgroundList = [
            {
              type: this.selectedRow?.type,
              template_sm_type: 3,
              path: this.selectedRow?.path,
              sm_id: this.selectedRow?.id,
              sm_name: this.selectedRow?.name,
              x_axis: 0,
              y_axis: 0,
              width: 0,
              height: 0,
              template_page: this.currentPage + 1,
              background_display:
                this.templatePages[this.currentPage].backgroundSettings
                  .backgroundSize,
            },
          ];
        } else {
          console.log(this.selectedRows, "dsfsdfsdfsdf", this.currentPage);

          // 添加到素材列表（图片、视频、日期时间）
          this.templatePages[this.currentPage].materialList.push({
            type: this.selectedRow?.type,
            template_sm_type: this.type === 3 ? 2 : 1,
            path: this.selectedRow?.path,
            sm_id: this.selectedRow?.id,
            sm_name: this.selectedRow?.name,
            source_width: this.selectedRow?.source_width, // 素材宽度
            source_height: this.selectedRow?.source_height, // 素材高度
            x_axis: 0,
            y_axis: 0,
            width: 0,
            height: this.type === 3 ? 50 : 0,
            template_page: this.currentPage + 1,
          });
        }

        this.isShowMaterial = false;
      }
    },

    save () {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          // 准备所有素材数据，包括背景设置
          let allMaterials = [];
          this.templatePages.forEach((page, pageIndex) => {
            // 添加背景设置到背景素材项
            if (page.backgroundList && page.backgroundList.length > 0) {
              page.backgroundList.forEach((bgItem) => {
                // 为背景素材添加显示设置
                bgItem.background_display =
                  page.backgroundSettings.backgroundSize;
                // 记录所属页面
                bgItem.template_page = pageIndex + 1;
                allMaterials.push(bgItem);
              });
            }


            // 添加普通素材
            page.materialList.forEach((item) => {


              item.template_page = pageIndex + 1;
              allMaterials.push(item);
            });
          });

          if (this.isEdit) {
            // 编辑模板
            edit(
              {
                template_sm: allMaterials,
                resolution_ratio: this.sharedAddForm.resolution_ratio,
                swipter_time: this.sharedAddForm.swipter_time,
                name: this.sharedAddForm.name,
              },
              this.id
            ).then((res) => {
              this.$message({
                type: "success",
                message: this.$i18n.t("public.editSuccess"),
              });
              this.$refs.addForm.resetFields();
              this.isShowTemplate = false;
              this.getList();
            });
          } else {
            // 新增模板
            add({
              template_sm: allMaterials,
              resolution_ratio: this.sharedAddForm.resolution_ratio,
              name: this.sharedAddForm.name,
              swipter_time: this.sharedAddForm.swipter_time,
              type: 1,
            }).then((res) => {
              this.$message({
                type: "success",
                message: this.$i18n.t("public.addSuccess"),
              });
              this.$refs.addForm.resetFields();
              this.isShowTemplate = false;
              this.getList();
            });
          }
        }
      });
    },
    // 打包
    confirmPack () {
      this.$refs.packForm.validate((valid) => {
        if (valid) {
          savePack({
            html_content: this.html_content,
            resource_pack_name: this.packForm.resource_pack_name,
            name: this.packForm.name,
            id: this.templateId,
            type: this.type
          }).then((res) => {
            this.$message({
              type: "success",
              message: this.$i18n.t("template.form.successTips"),
            });
            this.$refs.packForm.resetFields();
            this.isShowPack = false;
            this.getList();
          });
        }
      });
      this.templateId = "",
        this.type = 1
    },
    // 生成打包 html 资源代码

    zipPackage (id, type) {

      let templateHtml = "";
      let templateData = {};
      let templatePages = {};
      let swipterTime = 0
      let templateName = ""
      this.templateId = id
      this.type = type
      let pageSize = ""
      getDetail({ id: id, type: type }).then((res) => {
        if (res.code === 0) {
          templateData = res.data;
        }
        pageSize = templateData.resolution_ratio
        templateName = templateData.name
        swipterTime = templateData.swipter_time

        // 遍历每一页
        templateData.template_sm.map((page) => {

          if (page?.template_page && templatePages) {
            const key = `page-${page.template_page}`
            templatePages[key] = templatePages[key] || []
            templatePages[key].push(page)
          }


        })

        this.html_content = zipPack(templateName, templatePages, swipterTime, pageSize, type)
      })

      this.isShowPack = true;

    },

    handleDragging (x, y, index) {
      if (this.templatePages[this.currentPage]?.materialList?.[index]) {
        this.templatePages[this.currentPage].materialList[index].x_axis = x;
        this.templatePages[this.currentPage].materialList[index].y_axis = y;
      }
    },
    handleResizing (x, y, w, h, index) {
      console.log(x, y, w, h, index, "dfsdfsdfsdfsd");

      if (this.templatePages[this.currentPage]?.materialList?.[index]) {


        this.templatePages[this.currentPage].materialList[index].x_axis = x;
        this.templatePages[this.currentPage].materialList[index].y_axis = y;
        this.templatePages[this.currentPage].materialList[index].width = Math.round(w, 1);
        this.templatePages[this.currentPage].materialList[index].height = Math.round(h, 1);
      }
    },
    addNewPage () {
      this.templatePages.push({
        backgroundUrl: "",
        materialList: [],
        backgroundList: [],
        backgroundSettings: {
          backgroundSize: "cover",
          backgroundPosition: "center center",
        },
      });
      this.currentPage = this.templatePages.length - 1;
      this.currentPageBackgroundSettings =
        this.templatePages[this.currentPage].backgroundSettings; // 添加这行
    },
    prevPage () {
      if (this.currentPage > 0) {
        this.currentPage--;
        this.currentPageBackgroundSettings =
          this.templatePages[this.currentPage].backgroundSettings;
      }
    },
    nextPage () {
      if (this.currentPage < this.templatePages.length - 1) {
        this.currentPage++;
        this.currentPageBackgroundSettings =
          this.templatePages[this.currentPage].backgroundSettings;
      }
    },
    delPage () {
      console.log(this.templatePages, this.currentPage);
      this.prevPage();
      if (this.templatePages.length > 1) {
        this.templatePages.pop(this.currentPage)
      }
    },
  },
};
</script>

<style scoped lang="scss">
.drag-ball {
  cursor: move;
  width: 50px;
  height: 50px;
  background-image: url("~@/assets/login/background.png");
  background-size: contain;
  background-repeat: repeat;
  z-index: 19;
}

.iframe-dialog-form {
  padding-top: 30px;
}

.flex {
  display: flex;
}

.img {
  width: 60px;
  height: 60px;
}

::v-deep .el-dialog {
  margin-top: 0 !important;
}

::v-deep .el-dialog__body {
  padding: 0 10px;
}

.container {
  display: flex;
  height: 100%;
  margin-top: 6px;
}

.left-pane {
  width: 5%;
  background: #f0f0f0;
  padding: 10px;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

.middle-pane {
  width: 1280px;
  height: 720px;
  position: relative;
  background-color: #e2e2e2;

  .del {
    width: 24px;
    height: 24px;
    position: absolute;
    right: 4px;
    top: 4px;
    cursor: pointer;
    display: none;
    border: 1px solid #f0f0f0;
    border-radius: 100px;
    background-color: #8383835c;
  }
}

.draggable:hover .del {
  display: block;
}

.uniform-width {
  width: 80%;
  /* 或者指定具体像素值，如 200px */
}

.right-pane {
  width: 360px;
  background: #f0f0f0;
  padding: 10px;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.view-item {
  margin-bottom: 10px;

  div {
    text-align: left;
  }
}

.thumbnail {
  width: 100%;
  height: auto;
  max-height: 100px;
  object-fit: cover;
}

// 新增：定义更大的左外边距样式
.ml-large {
  margin-left: 20px; // 可根据需要调整间距大小
}
</style>