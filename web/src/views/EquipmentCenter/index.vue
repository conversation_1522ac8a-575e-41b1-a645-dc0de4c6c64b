<template>
  <div class="equipment-center">
    <!-- 主要内容区域 -->
    <div class="equipment-container">
      <!-- 左侧设备树 -->
      <div class="tree-panel" :style="{ width: treeWidth + 'px' }">
        <el-card class="tree-card" shadow="never">
          <div slot="header" class="card-header">
            <span class="card-title">{{ $t('equipmentCenter.tree.title') || '设备列表' }}</span>
            <div class="header-actions">
              <el-button type="primary" size="small" icon="el-icon-plus" @click="addGroupDialog">
                {{ $t('equipmentCenter.button.addGroup') || '新增分组' }}
              </el-button>
            </div>
          </div>

          <el-input :placeholder="$t('equipmentCenter.tree.searchPlaceholder') || '输入关键字进行过滤'" v-model="filterText"
            clearable prefix-icon="el-icon-search" class="search-input" />

          <el-tree class="equipment-tree" :data="equipmentData" :props="defaultProps" :filter-node-method="filterNode"
            ref="tree" node-key="id" @node-click="handleNodeClick" :render-content="renderContent"
            :highlight-current="true" :empty-text="$t('equipmentCenter.tree.noData')" @node-expand="handleNodeExpand"
            :expand-on-click-node="false">
          </el-tree>
        </el-card>
      </div>

      <!-- 拖拽分隔条 -->
      <div class="resize-handle" @mousedown="startResize"></div>

      <!-- 右侧设备详情 -->
      <div class="detail-panel" :style="{ width: 'calc(100% - ' + (treeWidth + 8) + 'px)' }">
        <!-- 未选择设备时的提示 -->
        <div v-if="!selectedDevice" class="empty-state">
          <el-empty :description="$t('equipmentCenter.tree.selectTip') || '请选择左侧设备或分组查看详情'" :image-size="120">
            <template #image>
              <i class="el-icon-s-platform empty-icon"></i>
            </template>
          </el-empty>
        </div>

        <!-- 详情内容 -->
        <div v-else>
          <!-- 单个设备详情视图 -->
          <div v-if="selectedDevice.isClient" class="device-detail">
            <el-card class="info-card" shadow="never">
              <div slot="header" class="card-header">
                <span class="card-title">{{ $t('equipmentCenter.tree.basicInfo') || '基本信息' }}</span>
                <div class="header-actions">
                  <el-button type="primary" size="small" icon="el-icon-edit" @click="editDevice(selectedDevice)">
                    {{ $t('equipmentCenter.button.deviceEdit') || '编辑' }}
                  </el-button>
                  <el-button type="danger" size="small" icon="el-icon-delete" @click="deleteDevice(selectedDevice)">
                    {{ $t('equipmentCenter.button.delete') || '删除' }}
                  </el-button>
                </div>
              </div>
              <div class="device-info">
                <el-row :gutter="20">
                  <!-- <el-col :span="8">
                    <div class="info-item">
                      <span class="info-label">{{ $t('equipmentCenter.table.name') || '设备名称' }}:</span>
                      <span class="info-value">{{ selectedDevice.label || '-' }}</span>
                    </div>
                  </el-col> -->
                  <el-col :span="8">
                    <div class="info-item">
                      <span class="info-label">{{ $t('equipmentCenter.table.alias_name') || '设备别名' }}:</span>
                      <span class="info-value">{{ selectedDevice.alias_name || '-' }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="info-item">
                      <span class="info-label">{{ $t('equipmentCenter.form.mac') || 'MAC地址' }}:</span>
                      <span class="info-value">{{ selectedDevice.mac_address || '-' }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="info-item">
                      <span class="info-label">{{ $t('equipmentCenter.table.group_name') || '分组名称' }}:</span>
                      <span class="info-value">{{ selectedDevice.group_name || '-' }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="info-item">
                      <span class="info-label">{{ $t('equipmentCenter.table.status') || '设备状态' }}:</span>
                      <el-tag :type="selectedDevice.isOnline ? 'success' : 'danger'" size="small">
                        {{ selectedDevice.isOnline ? ($t('equipmentCenter.table.online') || '在线') :
                          ($t('equipmentCenter.table.offline') || '离线') }}
                      </el-tag>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="info-item">
                      <span class="info-label">{{ $t('equipmentCenter.table.ip_addr') }}:</span>
                      <span>{{ selectedDevice.ip_addr || '-' }}</span>

                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="info-item">
                      <span class="info-label">{{ $t('equipmentCenter.table.updated_at') || '最后上线时间' }}:</span>
                      <span class="info-value">{{ formatTime(selectedDevice.updated_at) || '-' }}</span>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-card>

            <!-- 素材与预览 -->
            <el-card class="info-card material-card" shadow="never">
              <div slot="header" class="card-header">
                <span class="card-title">{{ $t('equipmentCenter.material.title') }}</span>
                <div class="header-actions">
                  <el-button type="primary" size="small" icon="el-icon-upload2" @click="uploadMaterial(selectedDevice)">
                    {{ $t('equipmentCenter.button.upload') }}
                  </el-button>
                  <el-button type="success" size="small" icon="el-icon-refresh"
                    @click="replaceMaterial(selectedDevice)">
                    {{ $t('equipmentCenter.button.replace') }}
                  </el-button>
                  <el-button type="warning" size="small" icon="el-icon-view" @click="previewDevice(selectedDevice)">
                    {{ $t('equipmentCenter.button.preview') }}
                  </el-button>
                </div>
              </div>
              <div class="material-info">
                <!-- 假设 selectedDevice.material 存在 -->
                <div v-if="selectedDevice.material" class="material-details">
                  <img :src="selectedDevice.material.thumbnail" alt="素材缩略图" class="material-thumbnail" />
                  <div class="material-text">
                    <p class="material-name">{{ selectedDevice.material.name }}</p>
                    <p class="material-meta">类型: {{ selectedDevice.material.type }} | 大小: {{
                      selectedDevice.material.size }}</p>
                  </div>
                  <div class="material-actions">
                    <el-button type="text" size="small"
                      @click="editMaterial(selectedDevice.material)">编辑当前素材</el-button>
                    <el-button type="text" size="small" class="danger-text"
                      @click="unbindMaterial(selectedDevice)">解除关联</el-button>
                  </div>
                </div>
                <div v-else class="no-material">
                  <el-empty :description="$t('equipmentCenter.material.noMaterial')" :image-size="80"></el-empty>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 分组详情视图 -->
          <div v-else class="group-detail">
            <el-card class="group-card" shadow="never">
              <div slot="header" class="card-header">
                <span class="card-title">分组详情: {{ selectedDevice.label }}</span>
                <div class="header-actions">
                  <el-button type="primary" size="small" icon="el-icon-edit" @click="editGroupDialog(selectedDevice)">
                    {{ $t('equipmentCenter.button.editGroup') || '编辑分组' }}
                  </el-button>
                  <el-button type="danger" size="small" icon="el-icon-delete"
                    @click="deleteGroupDialog(selectedDevice)">
                    {{ $t('equipmentCenter.button.deleteGroup') || '删除分组' }}
                  </el-button>
                  <el-button type="success" size="small" icon="el-icon-upload" @click="handleBatchUpload">
                    批量上传
                  </el-button>
                  <el-button type="success" size="small" icon="el-icon-s-promotion" @click="batchPush"
                    :disabled="multipleSelection.length === 0">
                    批量推送
                  </el-button>
                </div>
              </div>



              <!-- 统计信息 -->
              <div class="stats-info">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <div class="stat-item">
                      <div class="stat-number">{{ selectedDevice.total || 0 }}</div>
                      <div class="stat-label">{{ $t('equipmentCenter.tree.total') || '设备总数' }}</div>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="stat-item online">
                      <div class="stat-number">{{ selectedDevice.online || 0 }}</div>
                      <div class="stat-label">{{ $t('equipmentCenter.tree.online') || '在线设备' }}</div>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="stat-item offline">
                      <div class="stat-number">{{ (selectedDevice.total || 0) - (selectedDevice.online || 0) }}</div>
                      <div class="stat-label">{{ $t('equipmentCenter.tree.offline') || '离线设备' }}</div>
                    </div>
                  </el-col>
                </el-row>
              </div>
              <!-- 设备列表 -->
              <div style="height:calc(100vh - 400px);overflow-y: auto;"">
                <el-table :data="selectedDevice.children" @selection-change="handleSelectionChange"
                style="width: 100%; margin-top: 20px; ">
                <el-table-column type="selection" width="55"></el-table-column>
                <el-table-column prop="alias_name" label="设备别名" width="150px" show-overflow-tooltip></el-table-column>
                <el-table-column prop="mac_address" label="MAC地址" width="150px" show-overflow-tooltip></el-table-column>
                <el-table-column label="连接状态" width="80" align="center">
                  <template slot-scope="scope">
                    <el-tag :type="scope.row.isOnline ? 'success' : 'danger'" size="small">
                      {{ scope.row.isOnline ? '在线' : '离线' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="下发状态" show-overflow-tooltip></el-table-column>
                <el-table-column label="操作" width="150" align="center">
                  <template slot-scope="scope">

                    <el-button size="mini" @click="editDevice(scope.row)">编辑</el-button>
                    <el-button size="mini" type="danger" @click="deleteDevice(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
                </el-table>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑设备弹窗 -->
    <el-dialog :title="$t('equipmentCenter.dialog.title.edit')" :visible.sync="isShow" width="40%" @close="closeDialog"
      :close-on-click-modal="false">
      <el-form :model="deviceInfo" :rules="rules" ref="deviceInfoForm" label-width="120px" label-position="left">
        <el-form-item :label="$t('equipmentCenter.table.group_name')" prop="group_name">
          <el-select v-model="deviceInfo.group_name" :placeholder="$t('equipmentCenter.form.groupPlaceholder')"
            style="width: 100%;" filterable allow-create default-first-option>
            <el-option v-for="item in groupNameList" :key="item.id" :label="item.name == 'null' ? '无分组' : item.name"
              :value="item.name">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('equipmentCenter.table.alias_name')" prop="alias_name" required>
          <el-input v-model="deviceInfo.alias_name"
            :placeholder="$t('equipmentCenter.form.namePlaceholder')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('equipmentCenter.form.mac')" prop="mac_address">
          <el-input v-model="deviceInfo.mac_address" :disabled="true"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShow = false">{{ $t('public.cancel') }}</el-button>
        <el-button type="primary" @click="saveDevice()">{{ $t('public.confirm') }}</el-button>
      </span>
    </el-dialog>

    <!-- 素材选择弹窗 -->
    <el-dialog title="更换素材" :visible.sync="isMaterialDialogVisible" width="60%" append-to-body
      :close-on-click-modal="false">
      <div class="material-select-dialog">
        <el-table v-loading="isMaterialLoading" :data="materials" height="400px" style="width: 100%;" border>
          <el-table-column width="55" align="center">
            <template slot-scope="scope">
              <el-radio :label="scope.row.id" v-model="selectedMaterialId" @change.native="() => { }">&nbsp;</el-radio>
            </template>
          </el-table-column>
          <el-table-column label="缩略图" width="120" align="center">
            <template slot-scope="scope">
              <el-image style="width: 80px; height: 80px; border-radius: 4px;" :src="scope.row.thumbnail"
                :preview-src-list="[scope.row.thumbnail]" fit="cover">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column prop="label" label="素材名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="type" label="类型" width="100" align="center"></el-table-column>
          <el-table-column prop="size" label="大小" width="120" align="center"></el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isMaterialDialogVisible = false">{{ $t('public.cancel') }}</el-button>
        <el-button type="primary" @click="confirmReplaceMaterial">{{ $t('public.confirm') }}</el-button>
      </span>
    </el-dialog>

    <!-- 素材上传弹窗 -->
    <el-dialog title="上传新素材" :visible.sync="isUploadDialogVisible" width="50%" append-to-body
      :close-on-click-modal="false">
      <div v-if="selectedDevice" class="upload-dialog-content">
        <p>上传的素材将自动与当前设备 <el-tag size="small">{{ selectedDevice.label }}</el-tag> 关联。</p>
        <file-upload :file-list="uploadFileList" :uploadNum="1" fileType="image/video" @editUrl="handleUploadSuccess"
          @uploadStatus="handleUploadStatus" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isUploadDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- WebRTC预览弹窗 -->
    <el-dialog title="实时预览" :visible.sync="isPreviewDialogVisible" width="800px" @close="closePreviewDialog"
      append-to-body :close-on-click-modal="false">
      <div class="preview-dialog-content" v-loading="isRtcConnecting" element-loading-text="正在建立视频连接...">
        <video ref="previewVideo" autoplay playsinline style="width: 100%; height: 450px; background: #000;"></video>
        <div v-if="!isRtcConnecting && !previewStream" class="preview-placeholder">
          <i class="el-icon-video-camera-solid"></i>
          <p>无法加载视频流</p>
        </div>
      </div>
    </el-dialog>

    <!-- 素材编辑弹窗 -->
    <el-dialog title="编辑素材信息" :visible.sync="isEditMaterialDialogVisible" width="40%" append-to-body
      :close-on-click-modal="false">
      <el-form v-if="editingMaterial" :model="editingMaterial" :rules="materialRules" ref="materialForm"
        label-width="100px">
        <el-form-item label="素材名称" prop="name">
          <el-input v-model="editingMaterial.name" placeholder="请输入素材名称"></el-input>
        </el-form-item>
        <el-form-item label="素材类型" prop="type">
          <el-input v-model="editingMaterial.type" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="素材大小" prop="size">
          <el-input v-model="editingMaterial.size" :disabled="true"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isEditMaterialDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveMaterial">保存</el-button>
      </span>
    </el-dialog>

    <!-- 分组新增/编辑弹窗 -->
    <el-dialog
      :title="isEditGroup ? $t('equipmentCenter.dialog.title.editGroup') : $t('equipmentCenter.dialog.title.addGroup')"
      :visible.sync="isGroupDialogVisible" width="40%" @close="closeGroupDialog" :close-on-click-modal="false">
      <el-form :model="groupForm" :rules="groupRules" ref="groupForm" label-width="120px" label-position="left">
        <el-form-item :label="$t('equipmentCenter.dialog.form.groupName')" prop="name">
          <el-input v-model="groupForm.name"
            :placeholder="$t('equipmentCenter.dialog.form.groupNamePlaceholder')"></el-input>
        </el-form-item>
        <el-form-item :label="$t('equipmentCenter.dialog.form.groupDescription')" prop="description">
          <el-input v-model="groupForm.description" type="textarea" :rows="3"
            :placeholder="$t('equipmentCenter.dialog.form.groupDescriptionPlaceholder')"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isGroupDialogVisible = false">{{ $t('public.cancel') }}</el-button>
        <el-button type="primary" @click="saveGroup">{{ $t('public.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getTreeList, edit, del, getGroupNameList, addGroup, editGroup, deleteGroup, batchUploadFiles } from '@/api/equipmentCenter.js'
import { getList as getMaterialList, edit as editMaterial } from '@/api/material.js'
import FileUpload from '@/components/file-upload.vue'

export default {
  name: 'EquipmentCenter',
  components: { FileUpload },
  props: ['groupName', 'macAddress'],
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val);
    },
    '$route.params': {
      handler: 'handleRouteChange',
      deep: true
    }
  },
  data () {
    return {
      equipmentData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
        isClient: 'isClient',
        mac_address: 'mac_address',
      },
      filterText: "",
      selectedDevice: null, // 当前选中的设备或分组
      treeWidth: 250,
      isResizing: false,
      deviceInfo: {
        mac_address: '',
        alias_name: '',
        group_name: '',
      },
      groupNameList: [],
      rules: {
        alias_name: [
          { required: true, message: this.$t('equipmentCenter.form.namePlaceholder'), trigger: 'blur' },
        ],
      },
      isShow: false,
      isLoading: false,
      multipleSelection: [], // 用于存储表格中的多选行

      // 素材选择弹窗
      isMaterialDialogVisible: false,
      materials: [],
      isMaterialLoading: false,
      selectedMaterialId: null,

      // 素材上传弹窗
      isUploadDialogVisible: false,
      uploadFileList: [],
      uploading: false,

      // WebRTC 预览弹窗
      isPreviewDialogVisible: false,
      rtcPeerConnection: null,
      previewStream: null,
      isRtcConnecting: false,

      // 素材编辑弹窗
      isEditMaterialDialogVisible: false,
      editingMaterial: null,
      materialRules: {
        name: [
          { required: true, message: '请输入素材名称', trigger: 'blur' },
        ],
      },

      // 分组管理弹窗
      isGroupDialogVisible: false,
      isEditGroup: false,
      groupForm: {
        id: null,
        name: '',
        description: '',
      },
      groupRules: {
        name: [
          { required: true, message: this.$t('equipmentCenter.dialog.message.groupNameRequired'), trigger: 'blur' },
        ],
      }
    }
  },
  created () {
    this.getList()
  },
  methods: {
    getList () {
      this.isLoading = true
      getTreeList().then(res => {
        if (res.code == 0) {
          this.equipmentData = res.data
          this.handleRouteChange();
        }
      }).finally(() => {
        this.isLoading = false
      })
    },
    handleRouteChange () {
      this.$nextTick(() => {
        if (!this.$refs.tree || !this.equipmentData || this.equipmentData.length === 0) return;

        const { groupName, macAddress } = this.$route.params;

        // 清除之前的状态
        if (this.selectedDevice) {
          this.$refs.tree.setCurrentKey(null);
          this.selectedDevice = null;
        }

        if (macAddress) {
          const deviceNode = this.findNodeByMac(this.equipmentData, macAddress);
          if (deviceNode) {
            this.selectedDevice = deviceNode;
            this.$refs.tree.setCurrentKey(deviceNode.id);

            // 查找并展开父节点
            const parentNode = this.findNode(this.equipmentData, n => n.children && n.children.some(c => c.id === deviceNode.id));
            if (parentNode && this.$refs.tree.store.nodesMap[parentNode.id]) {
              this.$refs.tree.store.nodesMap[parentNode.id].expand();
            }
          }
        } else if (groupName) {
          const groupNode = this.findNodeByLabel(this.equipmentData, groupName);
          if (groupNode) {
            this.selectedDevice = groupNode;
            this.$refs.tree.setCurrentKey(groupNode.id);
            if (this.$refs.tree.store.nodesMap[groupNode.id]) {
              this.$refs.tree.store.nodesMap[groupNode.id].expand();
            }
          }
        }
      });
    },
    findNodeById (tree, id) {
      for (const node of tree) {
        if (node.id == id) {
          return node;
        }
        if (node.children) {
          const found = this.findNodeById(node.children, id);
          if (found) {
            return found;
          }
        }
      }
      return null;
    },
    findNode (tree, predicate) {
      for (const node of tree) {
        if (predicate(node)) {
          return node;
        }
        if (node.children) {
          const found = this.findNode(node.children, predicate);
          if (found) {
            return found;
          }
        }
      }
      return null;
    },
    findNodeByLabel (tree, label) {
      return this.findNode(tree, (node) => !node.isClient && node.label === label);
    },
    findNodeByMac (tree, mac) {
      return this.findNode(tree, (node) => node.isClient && node.mac_address === mac);
    },
    handleNodeClick (data, node) {
      let groupName = null;
      if (data.isClient) {
        let parent = node.parent;
        // 循环向上查找，直到找到非客户端的父节点（即分组）
        while (parent && parent.data && parent.data.isClient) {
          parent = parent.parent;
        }
        if (parent && parent.data) {
          groupName = parent.data.label;
        }
      } else {
        groupName = data.label;
      }

      const macAddress = data.isClient ? data.mac_address : undefined;

      const newParams = {};
      if (groupName) {
        newParams.groupName = groupName;
      }
      if (macAddress) {
        newParams.macAddress = macAddress;
      }

      if (this.$route.params.groupName !== newParams.groupName || this.$route.params.macAddress !== newParams.macAddress) {
        this.$router.push({ name: 'EquipmentCenter', params: newParams });
      }

      this.selectedDevice = data;

      // TODO: 此处为前端模拟数据，实际数据应从API获取
      if (this.selectedDevice.isClient && !this.selectedDevice.material) {
        this.selectedDevice = {
          ...this.selectedDevice,
          material: {
            id: 'mat-001',
            name: '默认宣传视频.mp4',
            thumbnail: 'https://shadow.elemecdn.com/app/element/hamburger.9cf7b091-55e9-11e9-a976-7f4d0b07eef6.png',
            type: '视频',
            size: '15.8MB'
          }
        }
      }

      if (!data.isClient) {
        node.expanded ? this.$refs.tree.store?.nodesMap[data.id]?.collapse() : this.$refs.tree.store?.nodesMap[data.id]?.expand();
      } else {
        this.$refs.tree.setCurrentKey(data.id);
      }
    },

    filterNode (value, data) {
      if (!value) return true;
      const lowerCaseValue = value.toLowerCase();
      return (data.label && data.label.toLowerCase().indexOf(lowerCaseValue) !== -1) ||
        (data.alias_name && data.alias_name.toLowerCase().indexOf(lowerCaseValue) !== -1) ||
        (data.mac_address && data.mac_address.toLowerCase().indexOf(lowerCaseValue) !== -1);
    },

    renderContent (h, { node, data }) {
      const isClient = data.isClient;
      return h(
        'span',
        { class: 'custom-tree-node' },
        [
          h(
            'span',
            { class: 'node-content' },
            [
              h('i', {
                class: [
                  isClient ? 'el-icon-monitor' : (!node.expanded ? 'el-icon-folder' : 'el-icon-folder-opened'),
                  'node-icon',
                  { 'online-icon': isClient && data.isOnline, 'offline-icon': isClient && !data.isOnline }
                ]
              }),
              h('span', { class: 'node-label' }, node.label == 'null' ? this.$t('equipmentCenter.tree.null') : node.label),
              isClient
                ? h(
                  'el-tag',
                  {
                    props: {
                      size: 'mini',
                      type: data.isOnline ? 'success' : 'danger',
                    },
                    class: 'status-tag',
                  },
                  data.isOnline ? this.$t('equipmentCenter.tree.online') : this.$t("equipmentCenter.tree.offline")
                )
                : null,
            ]
          ),
          !isClient
            ? h(
              'span',
              { class: 'node-extra' },
              [
                h(
                  'span',
                  { class: 'device-count' },
                  [
                    h('span', { class: 'online-count' }, data.online || 0),
                    ' / ',
                    h('span', { class: 'total-count' }, data.total || 0),
                  ]
                )
              ]
            )
            : null,
        ]

      );
    },

    // =================== 素材与预览相关方法 ===================
    uploadMaterial (device) {
      this.uploadFileList = [];
      this.isUploadDialogVisible = true;
    },

    handleUploadSuccess (fileInfo) {
      // 文件上传成功后，我们认为它已经是一个新的素材了
      // 此时，我们应该将这个新素材与当前设备关联
      // 1. (模拟)调用接口将文件信息保存为新素材
      console.log(`上传文件 ${fileInfo.name} 成功`);

      const newMaterial = {
        id: `mat-${new Date().getTime()}`, // 模拟一个新ID
        name: fileInfo.name,
        thumbnail: fileInfo.url.startsWith('http') ? fileInfo.url : `https://via.placeholder.com/100`, // 模拟缩略图
        url: fileInfo.url,
        type: this.getFileType(fileInfo.name),
        size: 'N/A'
      };

      // 2. (模拟)调用接口将设备与新素材关联
      console.log(`将设备 ${this.selectedDevice.id} 与新上传的素材 ${newMaterial.id} 关联`);
      this.$message.success(`上传成功并已关联到当前设备`);

      // 3. 更新前端UI
      this.selectedDevice.material = newMaterial;

      // 4. 关闭上传弹窗并刷新素材列表
      this.isUploadDialogVisible = false;
      this.fetchMaterials(); // 刷新素材列表，以便在“更换素材”中看到
    },

    getFileType (fileName) {
      const ext = fileName.split('.').pop().toLowerCase();
      if (['jpg', 'jpeg', 'png', 'gif'].includes(ext)) return '图片';
      if (['mp4', 'mov', 'avi'].includes(ext)) return '视频';
      return '文件';
    },

    handleUploadStatus (status) {
      this.uploading = status;
    },

    replaceMaterial (device) {
      this.selectedMaterialId = device.material ? device.material.id : null;
      this.isMaterialDialogVisible = true;
      this.fetchMaterials();
    },

    fetchMaterials () {
      this.isMaterialLoading = true;
      // 假设 getMaterialList 返回素材列表
      getMaterialList({ page: 1, limit: 1000 }).then(res => {
        if (res.code === 0) {
          // 这里假设API返回的数据结构，并为缩略图提供一个占位符
          this.materials = (res.data.list || res.data).map(item => ({
            ...item,
            id: item.id, // 确保id存在
            label: item.label || item.name, // 兼容不同字段
            thumbnail: item.thumbnail || 'https://via.placeholder.com/100' // 使用占位图
          }));
        } else {
          this.$message.error('获取素材列表失败');
        }
      }).catch(() => {
        this.$message.error('获取素材列表失败');
      }).finally(() => {
        this.isMaterialLoading = false;
      });
    },

    confirmReplaceMaterial () {
      if (!this.selectedMaterialId) {
        this.$message.warning('请选择一个素材');
        return;
      }
      // TODO: 调用API将设备与素材关联
      const selectedMat = this.materials.find(m => m.id === this.selectedMaterialId);
      console.log(`将设备 ${this.selectedDevice.id} 与素材 ${this.selectedMaterialId} 关联`);
      this.$message.success(`操作成功，设备已关联素材: ${selectedMat.label}`);

      // 前端模拟更新
      if (!this.selectedDevice.material) this.selectedDevice.material = {};
      this.selectedDevice.material.name = selectedMat.label;
      this.selectedDevice.material.id = selectedMat.id;
      this.selectedDevice.material.thumbnail = selectedMat.thumbnail;
      this.selectedDevice.material.type = selectedMat.type; // 假设有类型
      this.selectedDevice.material.size = selectedMat.size; // 假设有大小

      this.isMaterialDialogVisible = false;
    },

    previewDevice (device) {
      this.isPreviewDialogVisible = true;
      this.isRtcConnecting = true;
      this.$nextTick(() => {
        this.initWebRTC(device);
      });
    },

    initWebRTC (device) {
      console.log(`[WebRTC] 开始为设备 [${device.label}] 初始化连接...`);
      // 模拟信令服务器配置
      const configuration = { iceServers: [{ urls: 'stun:stun.l.google.com:19302' }] };
      this.rtcPeerConnection = new RTCPeerConnection(configuration);

      this.rtcPeerConnection.onicecandidate = event => {
        if (event.candidate) {
          console.log('[WebRTC] 发现新的 ICE 候选:', event.candidate);
          // TODO: 将 candidate 发送到信令服务器
        }
      };

      this.rtcPeerConnection.ontrack = event => {
        console.log('[WebRTC] 接收到远程视频流');
        this.isRtcConnecting = false;
        if (this.$refs.previewVideo) {
          this.$refs.previewVideo.srcObject = event.streams[0];
          this.previewStream = event.streams[0];
        }
      };

      // 模拟从信令服务器接收 Offer 并创建 Answer
      this.createMockOfferAndAnswer();
    },

    createMockOfferAndAnswer () {
      console.log('[WebRTC] 正在创建模拟 Offer...');
      this.rtcPeerConnection.createOffer({
        offerToReceiveVideo: true,
      }).then(offer => {
        console.log('[WebRTC] 模拟 Offer 创建成功:', offer);
        return this.rtcPeerConnection.setLocalDescription(offer);
      }).then(() => {
        console.log('[WebRTC] 模拟: LocalDescription 设置成功，等待远端 Answer...');
        // 在真实场景中，这里会把 offer 发送给信令服务器
        // 然后等待远端（设备端）的 Answer
        // 这里我们模拟一个延迟后收到了 Answer
        setTimeout(() => {
          const mockAnswer = { type: 'answer', sdp: '...' }; // 这是一个无效的SDP，仅作演示
          console.log('[WebRTC] 模拟: 收到远端 Answer:', mockAnswer);
          // this.rtcPeerConnection.setRemoteDescription(new RTCSessionDescription(mockAnswer));
          console.warn('[WebRTC] 模拟: setRemoteDescription 已被注释，因为没有真实的Answer。在真实场景中需要取消注释。');

          // 由于没有真实的流，我们将在几秒后显示连接失败
          setTimeout(() => {
            if (this.isRtcConnecting) {
              this.$message.error('实时预览连接超时（模拟）');
              this.isRtcConnecting = false;
            }
          }, 5000);

        }, 2000);
      }).catch(e => {
        console.error('[WebRTC] Offer 创建失败:', e);
        this.$message.error('WebRTC Offer 创建失败');
        this.isRtcConnecting = false;
      });
    },

    closePreviewDialog () {
      console.log('[WebRTC] 关闭预览窗口，清理资源...');
      if (this.rtcPeerConnection) {
        this.rtcPeerConnection.close();
        this.rtcPeerConnection = null;
      }
      if (this.previewStream) {
        this.previewStream.getTracks().forEach(track => track.stop());
        this.previewStream = null;
      }
      this.isPreviewDialogVisible = false;
      this.isRtcConnecting = false;
    },

    editMaterial (material) {
      // 深拷贝一份，避免直接修改
      this.editingMaterial = JSON.parse(JSON.stringify(material));
      this.isEditMaterialDialogVisible = true;
    },

    saveMaterial () {
      this.$refs.materialForm.validate((valid) => {
        if (valid) {
          // 调用API保存
          editMaterial(this.editingMaterial, this.editingMaterial.id).then(() => {
            this.$message.success('素材信息更新成功');
            // 更新selectedDevice中的素材信息
            this.selectedDevice.material = { ...this.editingMaterial };
            this.isEditMaterialDialogVisible = false;
            // 同时刷新素材列表，以保证数据一致性
            this.fetchMaterials();
          }).catch(() => {
            this.$message.error('素材信息更新失败');
          });
        }
      });
    },

    unbindMaterial (device) {
      this.$confirm(`确定要解除设备 [${device.label}] 与其素材的关联吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // TODO: 调用API解除关联
        console.log(`调用API解除设备 ${device.id} 的素材关联`);
        this.$message.success('解除关联成功');
        // 前端模拟更新
        this.selectedDevice.material = null;
      }).catch(() => { });
    },

    // =================== 分组管理相关方法 ===================
    addGroupDialog () {
      this.isEditGroup = false;
      this.groupForm = {
        id: null,
        name: '',
        description: '',
      };
      this.isGroupDialogVisible = true;
    },

    editGroupDialog (group) {
      this.isEditGroup = true;
      this.groupForm = {
        id: group.id,
        name: group.label,
        description: group.description || '',
      };
      this.isGroupDialogVisible = true;
    },

    saveGroup () {
      this.$refs.groupForm.validate((valid) => {
        if (valid) {
          const api = this.isEditGroup ? editGroup : addGroup;
          const params = {
            name: this.groupForm.name,
            description: this.groupForm.description,
          };

          if (this.isEditGroup) {
            params.id = this.groupForm.id;
          }

          api(params).then(() => {
            this.$message.success(
              this.isEditGroup
                ? this.$t('equipmentCenter.dialog.message.groupEditSuccess')
                : this.$t('equipmentCenter.dialog.message.groupAddSuccess')
            );
            this.isGroupDialogVisible = false;
            this.getList(); // 刷新设备树
          }).catch(() => {
            this.$message.error('操作失败，请稍后重试');
          });
        }
      });
    },

    deleteGroupDialog (group) {
      if (group.group_id == 1) {
        this.$message.error(this.$t('equipmentCenter.dialog.message.defaultGroupDelete'));
        return;
      }
      if (group.children?.length > 0) {
        this.$message.error('请先删除分组下的所有设备');
        return;
      }

      this.$confirm(
        this.$t('equipmentCenter.dialog.message.deleteGroupWarning'),
        this.$t('equipmentCenter.dialog.message.deleteGroupConfirm'),
        {
          confirmButtonText: this.$t('public.confirm'),
          cancelButtonText: this.$t('public.cancel'),
          type: 'warning'
        }
      ).then(() => {
        deleteGroup(group.group_id).then(() => {
          this.$message.success(this.$t('equipmentCenter.dialog.message.groupDeleteSuccess'));
          this.selectedDevice = null; // 清空选中状态
          this.getList(); // 刷新设备树
        }).catch(() => {
          this.$message.error('删除失败，请稍后重试');
        });
      }).catch(() => {
        // 用户取消
      });
    },

    closeGroupDialog () {
      this.$refs.groupForm.resetFields();
      this.groupForm = {
        id: null,
        name: '',
        description: '',
      };
    },

    // =================== 原有方法 ===================
    editDevice (device) {
      if (!device || !device.isClient) {
        this.$message.warning('无效的设备');
        return;
      }
      this.deviceInfo = { ...device };
      this.isShow = true;
      this.fetchGroupNameList();
    },

    fetchGroupNameList () {
      getGroupNameList().then(res => {
        if (res.code == 0) {
          this.groupNameList = res.data
        }
      })
    },

    deleteDevice (device) {
      if (!device || !device.isClient) {
        this.$message.warning('无效的设备');
        return;
      }
      this.$confirm(`确定删除设备: ${device.label}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        del(device.id).then(() => {
          this.$message({ type: 'success', message: '删除成功' });
          if (this.selectedDevice && this.selectedDevice.id === device.id) {
            this.selectedDevice = null;
          }
          this.getList(); // 刷新列表
        }).catch(() => {
          this.$message({ type: 'error', message: '删除失败' });
        });
      }).catch(() => {
        // 用户取消
      });
    },

    saveDevice () {
      this.$refs.deviceInfoForm.validate((valid) => {
        if (valid) {
          edit(this.deviceInfo).then(() => {
            this.$message({ type: 'success', message: '保存成功' });
            this.isShow = false;
            this.getList(); // 刷新整个树以保证数据一致性
          }).catch(() => {
            this.$message({ type: 'error', message: '保存失败' });
          });
        }
      });
    },

    closeDialog () {
      this.$refs.deviceInfoForm.resetFields();
      this.deviceInfo = { mac_address: '', alias_name: '', group_name: '' };
    },

    handleSelectionChange (val) {
      this.multipleSelection = val;
    },

    batchPush () {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请至少选择一个设备');
        return;
      }
      const deviceNames = this.multipleSelection.map(item => item.label).join(', ');
      this.$message.success(`准备向以下设备进行批量推送: ${deviceNames}`);
      console.log('批量推送的设备:', this.multipleSelection);
      // 此处可添加调用API的逻辑
    },

    formatTime (timestamp) {
      if (!timestamp) return '-';
      return this.$formatTimeStamp ? this.$formatTimeStamp(timestamp) : new Date(timestamp).toLocaleString();
    },

    handleNodeExpand (data, node) {
      const expandedGroups = this.$refs.tree.store.nodesMap;
      const expandedGroupNames = [];
      for (const key in expandedGroups) {
        if (expandedGroups[key].expanded) {
          expandedGroupNames.push(expandedGroups[key].data.label);
        }
      }
      // 路由可以设计的更复杂，例如 /EquipmentCenter/group1,group2/deviceMac
      // 这里为了简单，只记录当前操作展开的分组
      if (this.$route.params.groupName !== data.label) {
        this.$router.push({ name: 'EquipmentCenter', params: { groupName: data.label } });
      }
    },

    startResize (e) {
      this.isResizing = true;
      const startX = e.clientX;
      const startWidth = this.treeWidth;
      const handleMouseMove = (ev) => {
        if (!this.isResizing) return;
        const deltaX = ev.clientX - startX;
        const newWidth = startWidth + deltaX;
        if (newWidth >= 150 && newWidth <= 500) {
          this.treeWidth = newWidth;
        }
      };
      const handleMouseUp = () => {
        this.isResizing = false;
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
    },

    handleBatchUpload () {
      if (!this.selectedDevice || this.selectedDevice.isClient) {
        this.$message.warning('请先选择一个设备分组');
        return;
      }
      const input = document.createElement('input');
      input.type = 'file';
      input.webkitdirectory = true;
      input.onchange = e => {
        const files = e.target.files;
        if (files.length === 0) {
          return;
        }
        this.uploadFolder(files);
      };
      input.click();
    },

    async uploadFolder (files) {
      const formData = new FormData();
      formData.append('groupId', this.selectedDevice.group_id);

      for (let i = 0; i < files.length; i++) {
        formData.append('files', files[i]);
      }

      this.isLoading = true;
      try {
        const res = await batchUploadFiles(formData);
        if (res.code === 0) {
          this.$message.success('文件上传成功，正在处理分配...');
          this.getList(); // 刷新列表
        } else {
          this.$message.error(res.msg || '上传失败');
        }
      } catch (error) {
        this.$message.error('上传过程中发生错误');
      } finally {
        this.isLoading = false;
      }
    },
  },
};
</script>

<style>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
  margin: 1px 0;
}


.custom-tree-node:hover {
  background-color: #f0f2f5;
}

.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  background-color: #d9ecff;
}

.node-content {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
}

.node-icon {
  margin-right: 10px;
  font-size: 16px;
  color: #606266;
}

.node-icon.online-icon {
  color: #67c23a;
}

.node-icon.offline-icon {
  color: #f56c6c;
}

.node-label {
  margin-right: 8px;
  color: #303133;
  font-weight: 500;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status-tag {
  margin-left: auto;
  flex-shrink: 0;
}

.node-extra {
  display: flex;
  align-items: center;
  margin-left: 10px;
  flex-shrink: 0;
}

.device-count {
  font-size: 11px;
  color: #909399;
  background-color: #f4f4f5;
  padding: 3px 8px;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  font-weight: 500;
}

.online-count {
  color: #67c23a;
  font-weight: 600;
}

.total-count {
  color: #606266;
}



.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 30px;
}
</style>

<style scoped>
.equipment-center {
  padding: 8px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 100px);
}

.equipment-container {
  height: calc(100vh - 120px);
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  position: relative;
  overflow: hidden;
}

.tree-panel {
  height: 100%;
  flex-shrink: 0;
  position: relative;
  background-color: #fafbfc;
  border-right: 1px solid #e4e7ed;
}

.resize-handle {
  width: 8px;
  height: 100%;
  background-color: transparent;
  cursor: col-resize;
  position: absolute;
  right: -4px;
  top: 0;
  z-index: 10;
}

.tree-card {
  height: 100%;
  border: none;
  background-color: transparent;
}

.tree-card .el-card__header {
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
}

.tree-card .el-card__body {
  padding: 10px;
  height: calc(100% - 65px);
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.search-input {
  margin-bottom: 8px;
  flex-shrink: 0;
}

.equipment-tree {
  background-color: transparent;
  flex-grow: 1;
  height: calc(100vh - 250px);
  overflow-y: auto;
}

.detail-panel {
  height: calc(100vh - 250px);
  flex: 1;
  /* overflow-y: auto; */
  padding: 16px;
  background-color: #fff;
}

.empty-state {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  font-size: 48px;
  color: #c0c4cc;
}

.info-card,
.group-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.material-card {
  margin-top: 16px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.device-info,
.group-info,
.stats-info,
.material-info {
  padding: 20px;
}

.group-info {
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 0;
}

.material-details {
  display: flex;
  align-items: center;
}

.material-thumbnail {
  width: 100px;
  height: 100px;
  border-radius: 6px;
  margin-right: 20px;
  object-fit: cover;
  border: 1px solid #ebeef5;
}

.material-text {
  flex-grow: 1;
}

.material-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 0 0 8px 0;
}

.material-meta {
  font-size: 13px;
  color: #909399;
  margin: 0;
}

.material-actions {
  margin-left: auto;
}

.danger-text {
  color: #f56c6c;
}

.danger-text:hover {
  color: #f78989;
}

.no-material {
  text-align: center;
  color: #909399;
}

.info-item {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  font-size: 14px;
}

.info-label {
  font-weight: 500;
  color: #606266;
  margin-right: 16px;
  min-width: 100px;
}

.info-value {
  color: #303133;
}

.stat-item {
  text-align: center;
  padding: 8px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.stat-item.online {
  border-color: #b3d8ff;
}

.stat-item.offline {
  border-color: #fbc4c4;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 8px;
}

.stat-item.online .stat-number {
  color: #67c23a;
}

.stat-item.offline .stat-number {
  color: #f56c6c;
}

.stat-label {
  font-size: 13px;
  color: #606266;
}

.preview-dialog-content {
  position: relative;
  min-height: 450px;
}

.preview-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
}

.preview-placeholder i {
  font-size: 60px;
  margin-bottom: 20px;
}
</style>