import request, { postBlob, getBlob, handleImport } from '@/utils/request'

export function getList (params) {
  return request({
    url: `/admin/equipment/getList`,
    method: 'get',
    params
  })
}

export function getTreeList (params) {
  return request({
    url: `/admin/equipment/getTreeList`,
    method: 'get',
    params
  })
}

export function add (data) {
  return request({
    url: `/manage/equipment/addEquipment`,
    method: 'post',
    data
  })
}

export function edit (data) {
  return request({
    url: `/admin/equipment/editEquipment`,
    method: 'put',
    data
  })
}

export function del (id) {
  return request({
    url: `/admin/equipment/delete/` + id,
    method: 'delete'
  })
}

export function getGroupNameList (params) {
  return request({
    url: `/admin/equipment/groupNameList`,
    method: 'get',
    params
  })
}

// 分组管理相关API
export function addGroup (data) {
  return request({
    url: `/admin/equipment/addGroup`,
    method: 'post',
    data
  })
}

export function editGroup (data) {
  return request({
    url: `/admin/equipment/editGroup`,
    method: 'put',
    data
  })
}

export function deleteGroup (id) {
  return request({
    url: `/admin/equipment/deleteGroup/${id}`,
    method: 'delete'
  })
}

export function getGroupDetail (id) {
  return request({
    url: `/admin/equipment/getGroupDetail/${id}`,
    method: 'get'
  })
}

// 批量上传文件并关联设备
export function batchUploadFiles (data) {
  return request({
    url: `/admin/sourcematerial/batchUpload`,
    method: 'post',
    data, // data 应该是 FormData
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
