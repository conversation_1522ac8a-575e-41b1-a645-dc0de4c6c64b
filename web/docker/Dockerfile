FROM node:20.11-alpine3.19  AS base



WORKDIR /app/web/
COPY . .
RUN rm -rf .yarn
RUN rm -rf node_modules

RUN yarn config set registry https://registry.npmmirror.com


RUN yarn install
RUN yarn build


FROM nginx

RUN echo "Asia/Shanghai" > /etc/timezone

WORKDIR /usr/share/nginx/html/

COPY --from=base /app/web/dist/ /usr/share/nginx/html/
RUN rm /etc/nginx/conf.d/default.conf

COPY --from=base /app/web/docker/default.conf /etc/nginx/conf.d/



EXPOSE 8080