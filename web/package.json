{"name": "esop-dashboard", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^1.7.2", "core-js": "^3.8.3", "element-ui": "^2.15.14", "hash-sum": "^2.0.0", "jquery": "^3.7.1", "js-cookie": "^3.0.5", "swiper": "^11.2.8", "vue": "^2.6.14", "vue-draggable-resizable": "^2.3.0", "vue-i18n": "^8.28.2", "vue-router": "^3.6.5", "vuex": "^3.6.2"}, "devDependencies": {"@babel/cli": "^7.24.8", "@babel/core": "^7.25.2", "@babel/eslint-parser": "^7.12.16", "@babel/preset-env": "^7.25.3", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sass": "^1.77.8", "sass-loader": "^16.0.0", "vue-template-compiler": "^2.6.14", "webpack-chain": "^6.5.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}