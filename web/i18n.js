import { login } from "@/api/user";
import Vue from "vue";
import VueI18n from "vue-i18n";
import locale from "element-ui/lib/locale";
import enLocale from "element-ui/lib/locale/lang/en";
import zhLocale from "element-ui/lib/locale/lang/zh-CN";
Vue.use(VueI18n);

const messages = {
  en: {
    public: {
      reset: "Reset",
      search: "Search",
      cancel: "Cancel",
      confirm: "Confirm",
      delete: "Delete",
      edit: "Edit",
      add: "Add",
      operation: "Operation",
      startDate: "Start Date",
      endDate: "End Date",
      addSuccess: "Added successfully!",
      editSuccess: "Edited successfully!",
      deleteSuccess: "Deleted successfully!",
      totalItems: "A total of {count} items",
      to: "to",
    },
    login: {
      esopBackend: "ESOP Admin",
      rememberPassword: "Remember Password",
      login: "Login",
      enterUsername: "Please enter the username",
      enterPassword: "Please enter the password",
      switchLang: "Switch to Chinese",
      loginOut: "Login Out",
      loginSuccess: "Login Success",
    },
    equipmentCenter: {
      tree: {
        null: "No Group",
        title: "Device List",
        searchPlaceholder: "Please enter the device name or MAC address",
        selectTip: "Please select the device",
        basicInfo: "Basic Information",
        total: "Total",
        online: "Online",
        offline: "Offline",
        noData: "No device data",
      },
      material: {
        title: "Material & Preview",
        noMaterial: "No material associated with this device"
      },
      form: {
        name: "Name",
        aliasName: "Alias Name",
        number: "Number",
        mac: "MAC Address",
        date: "Time",
        status: "Status",
        namePlaceholder: "Please enter the device name",
        numberPlaceholder: "Please enter the device number",
        groupPlaceholder: "Please select the device group",
        macberPlaceholder: "Please enter the MAC address",
      },
      button: {
        deviceEdit: "Edit Device",
        allSend: "Send All",
        create: "Create",
        cancel: "Cancel",
        edit: "Edit",
        editTemplate: "Edit Template",
        delete: "Delete",
        singleSend: "Send",
        upload: "Upload Material",
        replace: "Replace Material",
        preview: "Live Preview",
        addGroup: "Add Group",
        editGroup: "Edit Group",
        deleteGroup: "Delete Group"
      },
      table: {
        num: "Serial Number",
        name: "Device Name",
        number: "Device Number",
        alias_name: "Device Alias",
        status: "Status",
        online: "Online",
        offline: "Offline",
        group_name: "Group Name",
        created_at: "Creation Time",
        updated_at: "Last Online Time",
        ip_addr: "IP Address",
        operation: "Operation",
        sureToDelete: "Are you sure to delete this device?",
      },
      dialog: {
        title: {
          add: "Add",
          edit: "Edit",
          addGroup: "Add Group",
          editGroup: "Edit Group",
        },
        form: {
          accountName: "Account Name",
          account: "Account",
          password: "Password",
          accountNamePlaceholder: "Please enter the account name",
          accountPlaceholder: "Please enter the account",
          passwordPlaceholder: "Please enter the password",
          groupName: "Group Name",
          groupNamePlaceholder: "Please enter group name",
          groupDescription: "Group Description",
          groupDescriptionPlaceholder: "Please enter group description",
        },
        message: {
          deleteGroupConfirm: "Are you sure to delete this group?",
          deleteGroupWarning: "Deleting a group will move all devices in it to 'No Group'. Continue?",
          groupNameRequired: "Group name is required",
          groupAddSuccess: "Group added successfully",
          groupEditSuccess: "Group updated successfully",
          groupDeleteSuccess: "Group deleted successfully",
        },
      },
    },
    operationLog: {
      form: {
        operatorName: "Name",
        operatorAccount: "Account",
        operationTime: "Time",
        operatorNamePlaceholder: "Please enter the operator name",
        operatorAccountPlaceholder: "Please enter the operator account",
      },
      table: {
        num: "Serial Number",
        operatorName: "Operator Name",
        operatorAccount: "Operator Account",
        action: "Type",
        module: "Module",
        operationTime: "Operation Time",
      },
    },
    material: {
      form: {
        name: "Name",
        date: "Time",
        namePlaceholder: "Please enter the material name",
      },
      button: {
        add: "Add Material",
      },
      table: {
        num: "Number",
        name: "Name",
        type: " Type",
        preview: "Preview",
        created_at: "Time",
        operation: "Operation",
        sureToDelete: "Are you sure to delete this material?",
        image: "Image",
        video: "Video",
        file: "File"
      },
      dialog: {
        title: {
          addMaterial: "Add Material",
          editMaterial: "Edit Material",
        },
        form: {
          name: "Name",
          type: "Type",
          path: "Path",
          upload: "Material",
          namePlaceholder: "Please enter the material name",
          typePlaceholder: "Please select the material type",
          pathPlaceholder: "Please upload the material",
          selectFile: 'Please upload a file',
          onlyImage: 'Please upload image type material',
          onlyVideo: 'Please upload video type material',
          onlyPDF: 'Please upload PDF type file',
          onlyFile: 'Please upload a supported file type (PDF, Word, Excel, PowerPoint)',
        },
      },
    },
    account: {
      form: {
        name: " Name",
        createTime: " Time",
        account: "Account",
        password: "Password",
        namePlaceholder: "Please enter the account name",
        accountPlaceholder: "Please enter the account",
        passwordPlaceholder: "Please enter the password",
      },
      button: {
        addAccount: "Add Account",
      },
      table: {
        num: "Serial Number",
        name: "Account Name",
        account: "Account",
        created_at: "Creation Time",
        confirmDelete: "Are you sure to delete this account?",
      },
      dialog: {
        title: {
          add: "Add Account",
          edit: "Edit Account",
        },
      },
    },
    template: {
      background: {
        repeat: "Tile",
        cover: "Stretch",
        contain: "Fit",
        auto: "Original Size",
      },
      form: {
        name: " Name",
        date: " Time",
        namePlaceholder: "Please enter the template name",
        urlPlaceholder: "Please enter the  URL",
        resolutionRatio: "Resolution",
        resolutionRatioPlaceholder: "Please select resolution",
        swipterTime: "Switching time",
        resourcePackName: "Resource Pack Name",
        resourcePackAlias: "Resource Pack Alias",
        resourcePackAliasPlaceholder: "Please enter resource pack alias",
        successTips: "Packaging successful",
        resourcePackNamePlaceholder: "Please enter the resource pack name",
        materialsPlaceholder: "Please give me the materials",
        iframeUrl: "Iframe URL",
      },
      button: {
        updateWorkTemplate: "Update Work Template",
        createWorkTemplate: "Create Work Template",
        create: "Create Template",
        addMaterial: "Add Material",
        clearBackground: "Clear Background",
        clearTemplate: "Reset Template",
        prevPage: "Previous Page",
        nextPage: "Next Page",
        addNewPage: "Add New Page",
        setBackground: "Set Background Image",
        addImage: "Add Image",
        addVideo: "Add Video",
        dateTime: "Date & Time",
        iframe: "Iframe",
        page: "Page",
        delPage: "Delete Page"
      },
      table: {
        num: "Serial Number",
        name: "Template Name",
        type: "Template Type",
        created_at: "Creation Time",
        operation: "Operation",
        sureToDelete: "Are you sure to delete this template?",
      },

      dialog: {
        title: {
          add: "Add Template",
          edit: "Edit Template",
          pack: "Packaging",
          material: "Select Material",
        },

        pageInfo: {
          currentPage: "Current Page",
          totalPages: "Total Pages",
        },
        materialType: {
          image: "Image",
          video: "Video",
          dateTime: "Date & Time",
        },
      },
    },
    menu: {
      accountManagement: "Account",
      templateManagement: "Template",
      publicTemplateManagement: "Public Template",
      materialManagement: "Public Material",
      deviceCenter: "Device",
      resourceManagement: "Resource",
      operationLog: "Operation",
    },

    upload: {
      onlyVideo: "This option only supports uploading files in video format!",
      onlyImage: "This option only supports uploading files in image format!",
      onlyVideoOrImageAgain: "This option only supports uploading files in video or image format!",
      maxFileCount: "Maximum {count} files can be uploaded!", // 最多上传 {count} 个文件！
    },
    resource: {
      form: {
        name: "Name",
        namePlaceholder: "Please enter resource name",
        packName: "Name",
        packNamePlaceholder: "Please enter package name",
        date: "Time",
        mac_address: "MAC Address",
        deviceName: "Device Name",
        deviceNamePlaceholder: "Please enter device name",
        deviceId: "Device ID",
        deviceIdPlaceholder: "Please enter device ID",
        deviceAliasName: "Device Alias",
      },
      button: {
        sendByRule: "Send by Rule",
        cancel: "Cancel",
        confirm: "Confirm",
        nextStep: "Next Step",
        sendAll: "Send All",
        sendPart: "Specified send",
      },
      table: {
        num: "Serial No",
        name: "Resource Name",
        pack_name: "Package Name",
        created_at: "Creation Time",
        deleteResource: "Are you sure you want to delete this resource?",
      },
      dialog: {
        title: {
          selectDevice: "Select Device",
          selectResource: "Select Resource",
          inputDevice: "Input Devices for Resource",
          selectGroup: "Select Group",
          group_name: "Group Name",
        },
        tip: {
          noSelectedResources: "No resources selected",
          deviceAliasHint:
            "Enter device aliases, separate multiple devices with commas",
          confirmSend: "Confirm sending to selected devices. Proceed?",
          selectAtLeastOneDevice: "Please select at least one device!",
          selectAtLeastOneGroup: "Please select at least one group!",
          selectAtLeastOneResource: "Please select at least one resource",
        },
        message: {
          // 20250509 补充
          selectedResources: "{count} resources selected",
          selectedGroups: "{count} groups selected",
          sendSuccess: "Sent successfully!",
          sendByRuleSuccess: "Rule sent successfully",
          sendFailed: "Sending failed, please try again later",
          requestError: "Request error, please check your network!",
        },
      },
      confirm: {
        // 20250509 补充
        title: "Confirmation",
        sendToDevices: "Confirm sending to selected devices. Proceed?",
      },
    },
    ...enLocale, // 合并ElementUI的英文语言包
  },
  zh: {
    public: {
      reset: "重置",
      search: "搜索",
      cancel: "取消",
      confirm: "确定",
      delete: "删除",
      edit: "编辑",
      add: "新增",
      operation: "操作",
      startDate: "开始日期",
      endDate: "结束日期",
      addSuccess: "新增成功!",
      editSuccess: "编辑成功!",
      deleteSuccess: "删除成功!",
      totalItems: "共 {count} 条2222",
      to: "至",
    },
    login: {
      esopBackend: "ESOP 后台",
      rememberPassword: "记住密码",
      login: "登录",
      enterUsername: "请输入用户名",
      enterPassword: "请输入密码",
      // 原英文翻译内容
      switchLang: "切换到英文",
      loginSuccess: "登录成功",
      loginOut: "退出登录",
    },
    equipmentCenter: {
      tree: {
        null: "无分组",
        title: "设备列表",
        searchPlaceholder: "输入关键字进行过滤",
        selectTip: "请选择左侧设备查看详情",
        basicInfo: "设备信息",
        total: "设备总数",
        online: "在线",
        offline: "离线",
        noData: "暂无设备数据",
      },
      material: {
        title: "素材与预览",
        noMaterial: "当前设备未关联素材"
      },
      form: {
        name: "设备名称",
        aliasName: "设备别名",
        number: "设备编号",
        mac: "MAC地址",
        date: "创建时间",
        status: "设备状态",
        namePlaceholder: "请输入设备名称",
        numberPlaceholder: "请输入设备编号",
        groupPlaceholder: "请选择设备分组",
        macPlaceholder: "请输入设备MAC地址",
      },
      button: {
        deviceEdit: "设备编辑",
        allSend: "全部",
        create: "创建",
        cancel: "取消",
        edit: "编辑",
        delete: "删除",
        editTemplate: "模板编辑",
        singleSend: "单个发送",
        upload: "上传素材",
        replace: "更换素材",
        preview: "实时预览",
        addGroup: "新增分组",
        editGroup: "编辑分组",
        deleteGroup: "删除分组"
      },
      table: {
        num: "序号",
        name: "设备名称",
        number: "设备编号",
        status: "设备状态",
        online: "在线",
        offline: "离线",
        alias_name: "设备别名",
        group_name: "分组名称",
        created_at: "创建时间",
        updated_at: "最后上线时间",
        ip_addr: "设备IP地址",
        operation: "操作",
        sureToDelete: "确定删除该设备?",
      },
      dialog: {
        title: {
          add: "新增",
          edit: "编辑",
          addGroup: "新增分组",
          editGroup: "编辑分组",
        },
        form: {
          accountName: "账号名称",
          account: "账号",
          password: "密码",
          accountNamePlaceholder: "请输入账号名称",
          accountPlaceholder: "请输入账号",
          passwordPlaceholder: "请输入密码",
          groupName: "分组名称",
          groupNamePlaceholder: "请输入分组名称",
          groupDescription: "分组描述",
          groupDescriptionPlaceholder: "请输入分组描述",
        },
        message: {
          deleteGroupConfirm: "确定删除该分组吗？",
          defaultGroupDelete: "无法删除默认分组",
          deleteGroupWarning: "删除分组将把该分组下的所有设备移动到'无分组'，是否继续？",
          groupNameRequired: "分组名称不能为空",
          groupAddSuccess: "分组新增成功",
          groupEditSuccess: "分组编辑成功",
          groupDeleteSuccess: "分组删除成功",
        },
      },
    },
    operationLog: {
      form: {
        operatorName: "操作人名称",
        operatorAccount: "操作人账号",
        operationTime: "操作时间",
        operatorNamePlaceholder: "请输入操作人名称",
        operatorAccountPlaceholder: "请输入操作人账号",
      },
      table: {
        num: "序号",
        operatorName: "操作人名称",
        operatorAccount: "操作人账号",
        action: "类型",
        module: "模块",
        operationTime: "操作时间",
      },
    },
    material: {
      form: {
        name: "素材名称",
        date: "创建时间",
        namePlaceholder: "请输入素材名称",
      },
      button: {
        add: "新增素材",
      },
      table: {
        num: "序号",
        name: "素材名称",
        type: "素材类型",
        preview: "素材预览",
        created_at: "创建时间",
        operation: "操作",
        sureToDelete: "确定删除该素材?",
        image: "图片",
        video: "视频",
        file: "文件"
      },
      dialog: {
        title: {
          addMaterial: "新增素材",
          editMaterial: "编辑素材",
        },
        form: {
          name: "素材名称",
          type: "素材类型",
          path: "素材路径",
          upload: "上传素材",
          namePlaceholder: "请输入素材名称",
          typePlaceholder: "请选择素材类型",
          pathPlaceholder: "请上传素材",
          selectFile: '请上传文件',
          onlyImage: '请上传图片类型的素材',
          onlyVideo: '请上传视频类型的素材',
          onlyPDF: '请上传PDF类型的文件',
          onlyFile: '请上传支持的文件类型（PDF, Word, Excel, PowerPoint）',
        },
      },
    },
    account: {
      form: {
        name: "账号名称",
        account: "账号",
        password: "密码",
        createTime: "创建时间",
        namePlaceholder: "请输入账号名称",
        accountPlaceholder: "请输入账号",
        passwordPlaceholder: "请输入密码",
      },
      button: {
        addAccount: "新建账号",
      },
      table: {
        num: "序号",
        name: "账号名称",
        account: "账号",
        created_at: "创建时间",
        confirmDelete: "这是一段内容确定删除吗？",
      },
      dialog: {
        title: {
          add: "新增账号",
          edit: "编辑账号",
        },
      },
    },
    upload: {
      onlyVideo: "该选项只支持视频格式的文件",
      onlyImage: "该选项只支持上传图片格式的文件！",
      onlyVideoOrImageAgain: "该选项只支持上传视频或图片格式的文件！",
      maxFileCount: "最多上传 {count} 个文件！", // 保持原中文提示

    },

    template: {
      background: {
        repeat: "平铺",
        cover: "拉伸",
        contain: "适应",
        auto: "原始大小",
      },

      form: {
        name: "模板名称",
        date: "创建时间",
        namePlaceholder: "请输入模板名称",
        urlPlaceholder: "请输入url路径",
        swipterTime: "定时切换时间",
        resolutionRatio: "分辨率",
        resolutionRatioPlaceholder: "请选择分辨率",
        resourcePackName: "资源名称",
        resourcePackAlias: "资源别名",
        resourcePackAliasPlaceholder: "请输入资源包别名",
        successTips: "打包成功",
        resourcePackNamePlaceholder: "请输入资源包名称",
        materialsPlaceholder: "请选择素材",
        iframeUrl: "嵌入网页地址",
      },
      button: {
        create: "新建模板",
        updateWorkTemplate: "修改作业模板",
        createWorkTemplate: "新建作业模板",
        addMaterial: "添加素材",
        clearBackground: "清空背景图",
        clearTemplate: "重置",
        prevPage: "上一页",
        nextPage: "下一页",
        addNewPage: "新增一页",
        setBackground: "设置背景图",
        addImage: "添加图片",
        addVideo: "添加视频",
        dateTime: "日期时间",
        iframe: "嵌入网页",
        page: "页",
        delPage: "删除页面"
      },
      table: {
        num: "序号",
        name: "模板名称",
        type: "模板类型",
        created_at: "创建时间",
        operation: "操作",
        sureToDelete: "确定删除该模板?",
      },

      dialog: {
        title: {
          add: "新增模板",
          edit: "编辑模板",
          pack: "打包",
          material: "选择素材",
        },

        pageInfo: {
          currentPage: "当前第",
          totalPages: "页，共",
        },
        materialType: {
          image: "图片",
          video: "视频",
          file: "文件",
          dateTime: "日期时间",
        },
      },
    },
    menu: {
      accountManagement: "账号管理",
      templateManagement: "模板管理",
      publicTemplateManagement: "公共模板管理",
      materialManagement: "公共素材管理",
      deviceCenter: "设备中心",
      resourceManagement: "资源管理",
      operationLog: "操作日志",
    },
    resource: {
      form: {
        name: "资源名称",
        namePlaceholder: "请输入资源名称",
        packName: "包名称",
        packNamePlaceholder: "请输入包名称",
        date: "创建时间",
        mac_address: "MAC地址",
        deviceName: "设备名称",
        deviceNamePlaceholder: "请输入设备名称",
        deviceId: "设备编号",
        deviceIdPlaceholder: "请输入设备编号",
        deviceAliasName: "设备别名",
      },
      button: {
        sendByRule: "按规则发送",
        sendAll: "全部发送",
        sendPart: "指定发送",
        cancel: "取消",
        confirm: "确定",
        nextStep: "下一步",
      },
      table: {
        num: "序号",
        name: "资源名称",
        pack_name: "包名称",
        created_at: "创建时间",
        deleteResource: "确定删除该资源？",
      },
      dialog: {
        title: {
          selectDevice: "选择设备",
          selectResource: "选择资源",
          inputDevice: "输入资源对应的设备",
          selectGroup: "选择分组",
          group_name: "分组名称",
        },
        tip: {
          noSelectedResources: "暂无选中资源",
          deviceAliasHint: "请输入设备别名，多个设备请使用逗号分隔",

          confirmSend: "请确定是否发送到已选中的设备, 是否继续?",
          selectAtLeastOneDevice: "请至少选择一个设备！",
          selectAtLeastOneGroup: "请至少选择一个分组！",
          selectAtLeastOneResource: "请至少选择一个资源!",
        },
        message: {
          // 20250509 补充
          selectedResources: "已选中 {count} 条资源数据",
          selectedGroups: "已选中 {count} 条分组数据",
          sendSuccess: "发送成功!",
          sendByRuleSuccess: "规则发送成功！",
          sendFailed: "发送失败，请稍后重试！",
          requestError: "请求出错，请检查网络！",
        },
      },
      confirm: {
        // 20250509 补充
        title: "提示",
        sendToDevices: "请确定是否发送到已选中的设备, 是否继续?",
      },
    },
    ...zhLocale, // 合并ElementUI的中文语言包
  },
};
// const i18n = new VueI18n({
//   locale: "zh",
//   messages,
// });
// // 设置ElementUI的语言更新函数
// locale.i18n((key, value) => i18n.t(key, value));
// export default i18n;
// ✅ 新增：读取本地存储的语言设置（优先使用用户选择的语言，否则用中文）
const savedLanguage = localStorage.getItem('appLanguage') || 'zh';

const i18n = new VueI18n({
  locale: savedLanguage, // ✅ 修改：初始化语言为本地存储的值或默认中文
  messages,
});

locale.i18n((key, value) => i18n.t(key, value)); // 保持 ElementUI 语言同步
export default i18n;
