(function(){var e={9613:function(e,t,a){"use strict";a.d(t,{Vp:function(){return n},cI:function(){return i},iD:function(){return o},ri:function(){return s}});var r=a(7120);function o(e){return(0,r.Ay)({url:"/v1/login",method:"post",params:e})}function n(e){return(0,r.Ay)({url:"/admin/user/detail",method:"get",params:{adminUserId:e}})}function i(e){return(0,r.Ay)({url:"/admin/user/module/list",method:"get",params:e})}function s(){return(0,r.Ay)({url:"/admin/user/logout",method:"post"})}},7618:function(e,t,a){"use strict";var r=a(1706),o=function(){var e=this,t=e._self._c;return t("div",{attrs:{id:"app"}},[t("router-view")],1)},n=[],i={name:"App",data(){return{}}},s=i,c=a(1656),l=(0,c.A)(s,o,n,!1,null,null,null),d=l.exports,u=a(8139),m=a(1974),p=(a(9613),a(6586)),h=a(766),g=a(8072),f=a(2059);r["default"].use(p.A);const S={en:{public:{reset:"Reset",search:"Search",cancel:"Cancel",confirm:"Confirm",delete:"Delete",edit:"Edit",add:"Add",operation:"Operation",startDate:"Start Date",endDate:"End Date",addSuccess:"Added successfully!",editSuccess:"Edited successfully!",deleteSuccess:"Deleted successfully!",totalItems:"A total of {count} items",to:"to"},login:{esopBackend:"ESOP Admin",rememberPassword:"Remember Password",login:"Login",enterUsername:"Please enter the username",enterPassword:"Please enter the password",switchLang:"Switch to Chinese",loginOut:"Login Out",loginSuccess:"Login Success"},equipmentCenter:{tree:{null:"No Group",title:"Device List",searchPlaceholder:"Please enter the device name or MAC address",selectTip:"Please select the device",basicInfo:"Basic Information",total:"Total",online:"Online",offline:"Offline",noData:"No device data"},material:{title:"Material & Preview",noMaterial:"No material associated with this device"},form:{name:"Name",aliasName:"Alias Name",number:"Number",mac:"MAC Address",date:"Time",status:"Status",namePlaceholder:"Please enter the device name",numberPlaceholder:"Please enter the device number",groupPlaceholder:"Please select the device group",macberPlaceholder:"Please enter the MAC address"},button:{deviceEdit:"Edit Device",allSend:"Send All",create:"Create",cancel:"Cancel",edit:"Edit",editTemplate:"Edit Template",delete:"Delete",singleSend:"Send",upload:"Upload Material",replace:"Replace Material",preview:"Live Preview",addGroup:"Add Group",editGroup:"Edit Group",deleteGroup:"Delete Group"},table:{num:"Serial Number",name:"Device Name",number:"Device Number",alias_name:"Device Alias",status:"Status",online:"Online",offline:"Offline",group_name:"Group Name",created_at:"Creation Time",updated_at:"Last Online Time",ip_addr:"IP Address",operation:"Operation",sureToDelete:"Are you sure to delete this device?"},dialog:{title:{add:"Add",edit:"Edit",addGroup:"Add Group",editGroup:"Edit Group"},form:{accountName:"Account Name",account:"Account",password:"Password",accountNamePlaceholder:"Please enter the account name",accountPlaceholder:"Please enter the account",passwordPlaceholder:"Please enter the password",groupName:"Group Name",groupNamePlaceholder:"Please enter group name",groupDescription:"Group Description",groupDescriptionPlaceholder:"Please enter group description"},message:{deleteGroupConfirm:"Are you sure to delete this group?",deleteGroupWarning:"Deleting a group will move all devices in it to 'No Group'. Continue?",groupNameRequired:"Group name is required",groupAddSuccess:"Group added successfully",groupEditSuccess:"Group updated successfully",groupDeleteSuccess:"Group deleted successfully"}}},operationLog:{form:{operatorName:"Name",operatorAccount:"Account",operationTime:"Time",operatorNamePlaceholder:"Please enter the operator name",operatorAccountPlaceholder:"Please enter the operator account"},table:{num:"Serial Number",operatorName:"Operator Name",operatorAccount:"Operator Account",action:"Type",module:"Module",operationTime:"Operation Time"}},material:{form:{name:"Name",date:"Time",namePlaceholder:"Please enter the material name"},button:{add:"Add Material"},table:{num:"Number",name:"Name",type:" Type",preview:"Preview",created_at:"Time",operation:"Operation",sureToDelete:"Are you sure to delete this material?",image:"Image",video:"Video",file:"File"},dialog:{title:{addMaterial:"Add Material",editMaterial:"Edit Material"},form:{name:"Name",type:"Type",path:"Path",upload:"Material",namePlaceholder:"Please enter the material name",typePlaceholder:"Please select the material type",pathPlaceholder:"Please upload the material",selectFile:"Please upload a file",onlyImage:"Please upload image type material",onlyVideo:"Please upload video type material",onlyPDF:"Please upload PDF type file",onlyFile:"Please upload a supported file type (PDF, Word, Excel, PowerPoint)"}}},account:{form:{name:" Name",createTime:" Time",account:"Account",password:"Password",namePlaceholder:"Please enter the account name",accountPlaceholder:"Please enter the account",passwordPlaceholder:"Please enter the password"},button:{addAccount:"Add Account"},table:{num:"Serial Number",name:"Account Name",account:"Account",created_at:"Creation Time",confirmDelete:"Are you sure to delete this account?"},dialog:{title:{add:"Add Account",edit:"Edit Account"}}},template:{background:{repeat:"Tile",cover:"Stretch",contain:"Fit",auto:"Original Size"},form:{name:" Name",date:" Time",namePlaceholder:"Please enter the template name",urlPlaceholder:"Please enter the  URL",resolutionRatio:"Resolution",resolutionRatioPlaceholder:"Please select resolution",swipterTime:"Switching time",resourcePackName:"Resource Pack Name",resourcePackAlias:"Resource Pack Alias",resourcePackAliasPlaceholder:"Please enter resource pack alias",successTips:"Packaging successful",resourcePackNamePlaceholder:"Please enter the resource pack name",materialsPlaceholder:"Please give me the materials",iframeUrl:"Iframe URL"},button:{updateWorkTemplate:"Update Work Template",createWorkTemplate:"Create Work Template",create:"Create Template",addMaterial:"Add Material",clearBackground:"Clear Background",clearTemplate:"Reset Template",prevPage:"Previous Page",nextPage:"Next Page",addNewPage:"Add New Page",setBackground:"Set Background Image",addImage:"Add Image",addVideo:"Add Video",dateTime:"Date & Time",iframe:"Iframe",page:"Page",delPage:"Delete Page"},table:{num:"Serial Number",name:"Template Name",type:"Template Type",created_at:"Creation Time",operation:"Operation",sureToDelete:"Are you sure to delete this template?"},dialog:{title:{add:"Add Template",edit:"Edit Template",pack:"Packaging",material:"Select Material"},pageInfo:{currentPage:"Current Page",totalPages:"Total Pages"},materialType:{image:"Image",video:"Video",dateTime:"Date & Time"}}},menu:{accountManagement:"Account",templateManagement:"Template",publicTemplateManagement:"Public Template",materialManagement:"Material",deviceCenter:"Device",resourceManagement:"Resource",operationLog:"Operation"},upload:{onlyVideo:"This option only supports uploading files in video format!",onlyImage:"This option only supports uploading files in image format!",onlyVideoOrImageAgain:"This option only supports uploading files in video or image format!",maxFileCount:"Maximum {count} files can be uploaded!"},resource:{form:{name:"Name",namePlaceholder:"Please enter resource name",packName:"Name",packNamePlaceholder:"Please enter package name",date:"Time",mac_address:"MAC Address",deviceName:"Device Name",deviceNamePlaceholder:"Please enter device name",deviceId:"Device ID",deviceIdPlaceholder:"Please enter device ID",deviceAliasName:"Device Alias"},button:{sendByRule:"Send by Rule",cancel:"Cancel",confirm:"Confirm",nextStep:"Next Step",sendAll:"Send All",sendPart:"Specified send"},table:{num:"Serial No",name:"Resource Name",pack_name:"Package Name",created_at:"Creation Time",deleteResource:"Are you sure you want to delete this resource?"},dialog:{title:{selectDevice:"Select Device",selectResource:"Select Resource",inputDevice:"Input Devices for Resource",selectGroup:"Select Group",group_name:"Group Name"},tip:{noSelectedResources:"No resources selected",deviceAliasHint:"Enter device aliases, separate multiple devices with commas",confirmSend:"Confirm sending to selected devices. Proceed?",selectAtLeastOneDevice:"Please select at least one device!",selectAtLeastOneGroup:"Please select at least one group!",selectAtLeastOneResource:"Please select at least one resource"},message:{selectedResources:"{count} resources selected",selectedGroups:"{count} groups selected",sendSuccess:"Sent successfully!",sendByRuleSuccess:"Rule sent successfully",sendFailed:"Sending failed, please try again later",requestError:"Request error, please check your network!"}},confirm:{title:"Confirmation",sendToDevices:"Confirm sending to selected devices. Proceed?"}},...g.A},zh:{public:{reset:"重置",search:"搜索",cancel:"取消",confirm:"确定",delete:"删除",edit:"编辑",add:"新增",operation:"操作",startDate:"开始日期",endDate:"结束日期",addSuccess:"新增成功!",editSuccess:"编辑成功!",deleteSuccess:"删除成功!",totalItems:"共 {count} 条2222",to:"至"},login:{esopBackend:"ESOP 后台",rememberPassword:"记住密码",login:"登录",enterUsername:"请输入用户名",enterPassword:"请输入密码",switchLang:"切换到英文",loginSuccess:"登录成功",loginOut:"退出登录"},equipmentCenter:{tree:{null:"无分组",title:"设备列表",searchPlaceholder:"输入关键字进行过滤",selectTip:"请选择左侧设备查看详情",basicInfo:"设备信息",total:"设备总数",online:"在线",offline:"离线",noData:"暂无设备数据"},material:{title:"素材与预览",noMaterial:"当前设备未关联素材"},form:{name:"设备名称",aliasName:"设备别名",number:"设备编号",mac:"MAC地址",date:"创建时间",status:"设备状态",namePlaceholder:"请输入设备名称",numberPlaceholder:"请输入设备编号",groupPlaceholder:"请选择设备分组",macPlaceholder:"请输入设备MAC地址"},button:{deviceEdit:"设备编辑",allSend:"全部",create:"创建",cancel:"取消",edit:"编辑",delete:"删除",editTemplate:"模板编辑",singleSend:"单个发送",upload:"上传素材",replace:"更换素材",preview:"实时预览",addGroup:"新增分组",editGroup:"编辑分组",deleteGroup:"删除分组"},table:{num:"序号",name:"设备名称",number:"设备编号",status:"设备状态",online:"在线",offline:"离线",alias_name:"设备别名",group_name:"分组名称",created_at:"创建时间",updated_at:"最后上线时间",ip_addr:"设备IP地址",operation:"操作",sureToDelete:"确定删除该设备?"},dialog:{title:{add:"新增",edit:"编辑",addGroup:"新增分组",editGroup:"编辑分组"},form:{accountName:"账号名称",account:"账号",password:"密码",accountNamePlaceholder:"请输入账号名称",accountPlaceholder:"请输入账号",passwordPlaceholder:"请输入密码",groupName:"分组名称",groupNamePlaceholder:"请输入分组名称",groupDescription:"分组描述",groupDescriptionPlaceholder:"请输入分组描述"},message:{deleteGroupConfirm:"确定删除该分组吗？",defaultGroupDelete:"无法删除默认分组",deleteGroupWarning:"删除分组将把该分组下的所有设备移动到'无分组'，是否继续？",groupNameRequired:"分组名称不能为空",groupAddSuccess:"分组新增成功",groupEditSuccess:"分组编辑成功",groupDeleteSuccess:"分组删除成功"}}},operationLog:{form:{operatorName:"操作人名称",operatorAccount:"操作人账号",operationTime:"操作时间",operatorNamePlaceholder:"请输入操作人名称",operatorAccountPlaceholder:"请输入操作人账号"},table:{num:"序号",operatorName:"操作人名称",operatorAccount:"操作人账号",action:"类型",module:"模块",operationTime:"操作时间"}},material:{form:{name:"素材名称",date:"创建时间",namePlaceholder:"请输入素材名称"},button:{add:"新增素材"},table:{num:"序号",name:"素材名称",type:"素材类型",preview:"素材预览",created_at:"创建时间",operation:"操作",sureToDelete:"确定删除该素材?",image:"图片",video:"视频",file:"文件"},dialog:{title:{addMaterial:"新增素材",editMaterial:"编辑素材"},form:{name:"素材名称",type:"素材类型",path:"素材路径",upload:"上传素材",namePlaceholder:"请输入素材名称",typePlaceholder:"请选择素材类型",pathPlaceholder:"请上传素材",selectFile:"请上传文件",onlyImage:"请上传图片类型的素材",onlyVideo:"请上传视频类型的素材",onlyPDF:"请上传PDF类型的文件",onlyFile:"请上传支持的文件类型（PDF, Word, Excel, PowerPoint）"}}},account:{form:{name:"账号名称",account:"账号",password:"密码",createTime:"创建时间",namePlaceholder:"请输入账号名称",accountPlaceholder:"请输入账号",passwordPlaceholder:"请输入密码"},button:{addAccount:"新建账号"},table:{num:"序号",name:"账号名称",account:"账号",created_at:"创建时间",confirmDelete:"这是一段内容确定删除吗？"},dialog:{title:{add:"新增账号",edit:"编辑账号"}}},upload:{onlyVideo:"该选项只支持视频格式的文件",onlyImage:"该选项只支持上传图片格式的文件！",onlyVideoOrImageAgain:"该选项只支持上传视频或图片格式的文件！",maxFileCount:"最多上传 {count} 个文件！"},template:{background:{repeat:"平铺",cover:"拉伸",contain:"适应",auto:"原始大小"},form:{name:"模板名称",date:"创建时间",namePlaceholder:"请输入模板名称",urlPlaceholder:"请输入url路径",swipterTime:"定时切换时间",resolutionRatio:"分辨率",resolutionRatioPlaceholder:"请选择分辨率",resourcePackName:"资源名称",resourcePackAlias:"资源别名",resourcePackAliasPlaceholder:"请输入资源包别名",successTips:"打包成功",resourcePackNamePlaceholder:"请输入资源包名称",materialsPlaceholder:"请选择素材",iframeUrl:"嵌入网页地址"},button:{create:"新建模板",updateWorkTemplate:"修改作业模板",createWorkTemplate:"新建作业模板",addMaterial:"添加素材",clearBackground:"清空背景图",clearTemplate:"重置",prevPage:"上一页",nextPage:"下一页",addNewPage:"新增一页",setBackground:"设置背景图",addImage:"添加图片",addVideo:"添加视频",dateTime:"日期时间",iframe:"嵌入网页",page:"页",delPage:"删除页面"},table:{num:"序号",name:"模板名称",type:"模板类型",created_at:"创建时间",operation:"操作",sureToDelete:"确定删除该模板?"},dialog:{title:{add:"新增模板",edit:"编辑模板",pack:"打包",material:"选择素材"},pageInfo:{currentPage:"当前第",totalPages:"页，共"},materialType:{image:"图片",video:"视频",file:"文件",dateTime:"日期时间"}}},menu:{accountManagement:"账号管理",templateManagement:"模板管理",publicTemplateManagement:"公共模板管理",materialManagement:"素材管理",deviceCenter:"设备中心",resourceManagement:"资源管理",operationLog:"操作日志"},resource:{form:{name:"资源名称",namePlaceholder:"请输入资源名称",packName:"包名称",packNamePlaceholder:"请输入包名称",date:"创建时间",mac_address:"MAC地址",deviceName:"设备名称",deviceNamePlaceholder:"请输入设备名称",deviceId:"设备编号",deviceIdPlaceholder:"请输入设备编号",deviceAliasName:"设备别名"},button:{sendByRule:"按规则发送",sendAll:"全部发送",sendPart:"指定发送",cancel:"取消",confirm:"确定",nextStep:"下一步"},table:{num:"序号",name:"资源名称",pack_name:"包名称",created_at:"创建时间",deleteResource:"确定删除该资源？"},dialog:{title:{selectDevice:"选择设备",selectResource:"选择资源",inputDevice:"输入资源对应的设备",selectGroup:"选择分组",group_name:"分组名称"},tip:{noSelectedResources:"暂无选中资源",deviceAliasHint:"请输入设备别名，多个设备请使用逗号分隔",confirmSend:"请确定是否发送到已选中的设备, 是否继续?",selectAtLeastOneDevice:"请至少选择一个设备！",selectAtLeastOneGroup:"请至少选择一个分组！",selectAtLeastOneResource:"请至少选择一个资源!"},message:{selectedResources:"已选中 {count} 条资源数据",selectedGroups:"已选中 {count} 条分组数据",sendSuccess:"发送成功!",sendByRuleSuccess:"规则发送成功！",sendFailed:"发送失败，请稍后重试！",requestError:"请求出错，请检查网络！"}},confirm:{title:"提示",sendToDevices:"请确定是否发送到已选中的设备, 是否继续?"}},...f["default"]}},v=localStorage.getItem("appLanguage")||"zh",P=new p.A({locale:v,messages:S});h["default"].i18n(((e,t)=>P.t(e,t)));var w=P,E=a(4927),T=a.n(E),A=a(7282);r["default"].prototype.$formatTimeStamp=A.YQ,r["default"].use(T()),r["default"].prototype.$confirm=T().MessageBox.confirm,r["default"].config.productionTip=!1,new r["default"]({el:"#app",router:u.Ay,store:m.A,i18n:w,render:e=>e(d)})},8139:function(e,t,a){"use strict";a.d(t,{bw:function(){return p},Ay:function(){return S},dg:function(){return f}});var r=a(1706),o=a(1594),n=function(){var e=this,t=e._self._c;return t("div",{attrs:{id:"app"}},[t("el-container",{staticStyle:{height:"100vh",border:"1px solid #eee"}},[t("el-aside",{staticStyle:{"background-color":"#001529"},attrs:{width:"200px"}},[t("div",{staticClass:"projrct-name"},[e._v(e._s(e.$t("login.esopBackend")))]),t("el-menu",{attrs:{"default-active":e.activeMenuIndex,"background-color":"#001529","text-color":"#fff","active-text-color":"#409eff"}},e._l(e.menuItems,(function(a,r){return t("router-link",{key:r,attrs:{to:a.path}},[t("el-menu-item",{attrs:{index:a.index}},[t("i",{class:a.icon}),t("span",{attrs:{slot:"title"},slot:"title"},[e._v(e._s(a.title))])])],1)})),1)],1),t("el-container",[t("el-header",{staticStyle:{"text-align":"right","font-size":"12px"}},[t("el-dropdown",{staticClass:"user-container",staticStyle:{cursor:"pointer"},attrs:{trigger:"hover"}},[t("div",{staticClass:"user right-menu-item hover-effect"},[t("div",{staticClass:"flex"},[t("i",{staticClass:"el-icon-user-solid"}),t("span",[e._v(e._s(e.name))])])]),t("el-dropdown-menu",{staticClass:"user-dropdown",attrs:{slot:"dropdown"},slot:"dropdown"},[t("el-dropdown-item",{nativeOn:{click:function(t){return e.logout.apply(null,arguments)}}},[t("span",{staticStyle:{display:"block"}},[e._v(e._s(e.$t("login.loginOut")))])])],1)],1)],1),t("el-main",[t("router-view")],1)],1)],1)],1)},i=[],s=(a(4114),{name:"App",data(){return{isLogin:!1,menuItems:[{title:this.$i18n.t("menu.publicTemplateManagement"),icon:"el-icon-s-order",index:"1",path:"/Template"},{title:this.$i18n.t("menu.materialManagement"),icon:"el-icon-picture",index:"2",path:"/Material"},{title:this.$i18n.t("menu.accountManagement"),icon:"el-icon-s-custom",index:"3",path:"/Account"},{title:this.$i18n.t("menu.deviceCenter"),icon:"el-icon-s-platform",index:"4",path:"/EquipmentCenter"},{title:this.$i18n.t("menu.resourceManagement"),icon:"el-icon-s-cooperation",index:"5",path:"/Resource"},{title:this.$i18n.t("menu.operationLog"),icon:"el-icon-s-check",index:"6",path:"/OperationLog"}],name:JSON.parse(localStorage.getItem("greemall_login")).name}},computed:{activeMenuIndex(){return this.menuItems.find((e=>e.path===this.$route.path))?.index||"1"}},methods:{async logout(){await this.$store.dispatch("user/logout"),this.$store.commit("tagsView/SET_RESET_VIES"),this.$router.push("/login")}}}),c=s,l=a(1656),d=(0,l.A)(c,n,i,!1,null,null,null),u=d.exports,m=a(2171);r["default"].use(o.Ay);const p=[{path:"/",redirect:"Login",component:()=>a.e(82).then(a.bind(a,9082))},{path:"/Login",name:"Login",component:()=>a.e(82).then(a.bind(a,9082))},{path:"/main",name:"Main",component:u,children:[{path:"/Template",name:"Template",component:()=>a.e(143).then(a.bind(a,1143))},{path:"/Material",name:"Material",component:()=>a.e(726).then(a.bind(a,8726))},{path:"/Account",name:"Account",component:()=>a.e(310).then(a.bind(a,9310))},{path:"/EquipmentCenter/:groupName?/:macAddress?",name:"EquipmentCenter",component:()=>a.e(254).then(a.bind(a,2254)),props:!0},{path:"/Resource",name:"Resource",component:()=>a.e(232).then(a.bind(a,4232))},{path:"/OperationLog",name:"OperationLog",component:()=>a.e(119).then(a.bind(a,2119))}]}],h=()=>new o.Ay({mode:"hash",base:"/",scrollBehavior:()=>({y:0}),routes:p}),g=h();function f(){const e=h();g.matcher=e.matcher}g.beforeEach(((e,t,a)=>{if("/login"===e.path)a();else{const e=(0,m.gf)();console.log(e),null!==e&&e?a():a("/login")}}));var S=g},7614:function(e){e.exports={title:"esop后台",tagsView:!0,fixedHeader:!0,sidebarLogo:!0,breadcrumb:!1,pages:[]}},1974:function(e,t,a){"use strict";a.d(t,{A:function(){return F}});var r=a(1706),o=a(1910);const n={sidebar:e=>e.app.sidebar,device:e=>e.app.device,l1Path:e=>e.app.l1Path,allUnreadNum:e=>e.app.allUnreadNum,show:e=>e.app.show,visitedViews:e=>e.tagsView.visitedViews,cachedViews:e=>e.tagsView.cachedViews,token:e=>e.user.token,fanruanToken:e=>e.user.fanruanToken,userid:e=>e.user.userid,phone:e=>e.user.phone,name:e=>e.user.name,isNotice:e=>e.user.isNotice,menus:e=>e.user.menus,websitNumber:e=>e.user.websitNumber,customerId:e=>e.user.customerId,customerName:e=>e.user.customerName,customerNumber:e=>e.user.customerNumber,showMessages:e=>e.user.showMessages,code:e=>e.sales.code,isCustomer:e=>e.user.customerId&&e.user.customerName&&e.user.customerNumber,isZbService:e=>e.user.isZbService,isService:e=>e.user.isService,isWebsite:e=>e.user.isWebsite,isAdmin:e=>e.user.isAdmin,isCenter:e=>e.user.isCenter,isMessageGroup:e=>e.user.isMessageGroup,isSettlementGroup:e=>e.user.isSettlementGroup,roleList:e=>e.user.roleList,websitId:e=>e.user.websitId,greemall_user:e=>e.user.greemall_user};var i=n,s=a(5462);const c={sidebar:{opened:!s.A.get("sidebarStatus")||!!+s.A.get("sidebarStatus"),withoutAnimation:!1},device:"desktop",l1Path:"",allUnreadNum:0,show:null},l={SET_L1_PATH:(e,t)=>{e.l1Path=t},TOGGLE_SIDEBAR:e=>{e.sidebar.opened=!e.sidebar.opened,e.sidebar.withoutAnimation=!1,e.sidebar.opened?s.A.set("sidebarStatus",1):s.A.set("sidebarStatus",0)},CLOSE_SIDEBAR:(e,t)=>{s.A.set("sidebarStatus",0),e.sidebar.opened=!1,e.sidebar.withoutAnimation=t},TOGGLE_DEVICE:(e,t)=>{e.device=t},SET_UNREAD_NOTICE:(e,t)=>{e.allUnreadNum=t},SET_SHOW:(e,t)=>{e.show=t}},d={toggleSideBar({commit:e}){e("TOGGLE_SIDEBAR")},closeSideBar({commit:e},{withoutAnimation:t}){e("CLOSE_SIDEBAR",t)},toggleDevice({commit:e},t){e("TOGGLE_DEVICE",t)},getUnreadNum({commit:e}){}};var u={namespaced:!0,state:c,mutations:l,actions:d},m=a(7614),p=a.n(m);const{showSettings:h,tagsView:g,fixedHeader:f,sidebarLogo:S,breadcrumb:v}=p(),P={showSettings:h,tagsView:g,fixedHeader:f,sidebarLogo:S,breadcrumb:v},w={CHANGE_SETTING:(e,{key:t,value:a})=>{e.hasOwnProperty(t)&&(e[t]=a)}},E={changeSetting({commit:e},t){e("CHANGE_SETTING",t)}};var T={namespaced:!0,state:P,mutations:w,actions:E},A=(a(4114),a(8139));function b(e,t){return!t.meta||!t.meta.roles||e.some((e=>t.meta.roles.includes(e)))}function _(e,t){const a=[];return e.forEach((e=>{const r={...e};b(t,r)&&(r.children&&(r.children=_(r.children,t)),a.push(r))})),a}const N={routes:[],addRoutes:[]},V={SET_ROUTES:(e,t)=>{e.addRoutes=t,e.routes=A.bw.concat(t)}},y={generateRoutes({commit:e},t){return console.log(t),new Promise((a=>{let r;r=t.includes("admin")?A.asyncRoutes||[]:_(A.asyncRoutes,t),e("SET_ROUTES",r),a(r)}))}};var I={namespaced:!0,state:N,mutations:V,actions:y};const D={visitedViews:[],cachedViews:[]},C={ADD_VISITED_VIEW:(e,t)=>{e.visitedViews.some((e=>e.path===t.path))||e.visitedViews.push(Object.assign({},t,{title:t?.query?.pageName||t.meta.title||"no-name"}))},ADD_CACHED_VIEW:(e,t)=>{e.cachedViews.includes(t.name)||(t.meta.noCache||e.cachedViews.push(t.name),e.visitedViews.length>15&&(e.visitedViews.splice(0,1),e.cachedViews.splice(0,1)))},SET_RESET_VIES:(e,t)=>{e.visitedViews=[]},DEL_VISITED_VIEW:(e,t)=>{for(const[a,r]of e.visitedViews.entries())if(r.path===t.path){e.visitedViews.splice(a,1);break}},DEL_CACHED_VIEW:(e,t)=>{const a=e.cachedViews.indexOf(t.name);a>-1&&e.cachedViews.splice(a,1)},DEL_OTHERS_VISITED_VIEWS:(e,t)=>{e.visitedViews=e.visitedViews.filter((e=>e.meta.affix||e.path===t.path))},DEL_OTHERS_CACHED_VIEWS:(e,t)=>{const a=e.cachedViews.indexOf(t.name);e.cachedViews=a>-1?e.cachedViews.slice(a,a+1):[]},DEL_ALL_VISITED_VIEWS:e=>{const t=e.visitedViews.filter((e=>e.meta.affix));e.visitedViews=t},DEL_ALL_CACHED_VIEWS:e=>{e.cachedViews=[]},UPDATE_VISITED_VIEW:(e,t)=>{for(let a of e.visitedViews)if(a.path===t.path){a=Object.assign(a,t);break}}},O={addView({dispatch:e},t){e("addVisitedView",t),e("addCachedView",t)},addVisitedView({commit:e},t){e("ADD_VISITED_VIEW",t)},addCachedView({commit:e},t){e("ADD_CACHED_VIEW",t)},delView({dispatch:e,state:t},a){return new Promise((r=>{e("delVisitedView",a),e("delCachedView",a),r({visitedViews:[...t.visitedViews],cachedViews:[...t.cachedViews]})}))},delVisitedView({commit:e,state:t},a){return new Promise((r=>{e("DEL_VISITED_VIEW",a),r([...t.visitedViews])}))},delCachedView({commit:e,state:t},a){return new Promise((r=>{e("DEL_CACHED_VIEW",a),r([...t.cachedViews])}))},delOthersViews({dispatch:e,state:t},a){return new Promise((r=>{e("delOthersVisitedViews",a),e("delOthersCachedViews",a),r({visitedViews:[...t.visitedViews],cachedViews:[...t.cachedViews]})}))},delOthersVisitedViews({commit:e,state:t},a){return new Promise((r=>{e("DEL_OTHERS_VISITED_VIEWS",a),r([...t.visitedViews])}))},delOthersCachedViews({commit:e,state:t},a){return new Promise((r=>{e("DEL_OTHERS_CACHED_VIEWS",a),r([...t.cachedViews])}))},delAllViews({dispatch:e,state:t},a){return new Promise((r=>{e("delAllVisitedViews",a),e("delAllCachedViews",a),r({visitedViews:[...t.visitedViews],cachedViews:[...t.cachedViews]})}))},delAllVisitedViews({commit:e,state:t}){return new Promise((a=>{e("DEL_ALL_VISITED_VIEWS"),a([...t.visitedViews])}))},delAllCachedViews({commit:e,state:t}){return new Promise((a=>{e("DEL_ALL_CACHED_VIEWS"),a([...t.cachedViews])}))},updateVisitedView({commit:e},t){e("UPDATE_VISITED_VIEW",t)}};var k={namespaced:!0,state:D,mutations:C,actions:O},L=a(9613),M=a(2171);const R=()=>({token:(0,M.gf)(),name:""}),G=R(),x={RESET_STATE:e=>{Object.assign(e,R())},SET_STATUS(e,t){e.isNotice=t},SET_USERID:(e,t)=>{e.userid=t},SET_TOKEN:(e,t)=>{e.token=t},SET_FANRUAN_TOKEN:(e,t)=>{e.fanruanToken=t},SET_NAME:(e,t)=>{e.name=t},SET_PHONE:(e,t)=>{e.phone=t},SET_IS_ZB_SERVICE:(e,t)=>{e.isZbService=t},SET_IS_SERVICE:(e,t)=>{e.isService=t},SET_IS_ADMIN:(e,t)=>{e.isAdmin=t},SET_MENUS:(e,t)=>{e.menus=t},SET_CUSTOMERID:(e,t)=>{e.customerId=t},SET_CUSTOMERNAME:(e,t)=>{e.customerName=t},SET_CUSTOMERNUMBER:(e,t)=>{e.customerNumber=t},SET_IS_COLLAPSE(e,t){e.isCollapse=!t.isCollapse},SET_WEBSIT_NUMBER:(e,t)=>{e.websitNumber=t},showMessage:(e,t)=>{"yes"==t?e.showMessages=!0:(e.showMessages=!1,e.isNotice=!0)},set_greemall_user:(e,t)=>{e.greemall_user=t},SET_ROLELIST:(e,t)=>{t.find((t=>{"serviceProviderSite"===t.code?e.isWebsite=!0:e.isWebsite=!1,"topMessenger"===t.code?e.isMessageGroup=!0:e.isMessageGroup=!1,"topSettlement"==t.code?e.isSettlementGroup=!0:e.isSettlementGroup=!1}))},SET_ROLELIST:(e,t)=>{t.find((t=>{"serviceProviderSite"===t.code&&(e.isWebsite=!0),"topMessenger"===t.code&&(e.isMessageGroup=!0),"topSettlement"==t.code&&(e.isSettlementGroup=!0)}))},SET_ROLE_LIST:(e,t)=>{e.roleList=t},SET_WEBSIT_ID:(e,t)=>{e.websitId=t}},W={login({commit:e},t){const{username:a,password:r}=t;return new Promise(((t,o)=>{(0,L.iD)({account:a.trim(),password:r}).then((a=>{const{data:r}=a;e("SET_TOKEN",r.Token),(0,M.WG)(r.Token),t(r)})).catch((e=>{o(e)}))}))},getInfo({commit:e,state:t}){return new Promise(((a,r)=>{(0,L.Vp)(t.userid).then((t=>{const{data:o}=t;if(!o)return r("Verification failed, please Login again.");console.log(o);const{nickName:n,userName:i}=o;e("SET_NAME",n),e("SET_PHONE",i),localStorage.setItem("greemall_user",JSON.stringify(o)),a(o)})).catch((e=>{r(e)}))}))},getRouter({commit:e,state:t}){return new Promise(((a,r)=>{(0,L.cI)({adminUserId:t.userid}).then((t=>{const r=t.data.filter((e=>1!==e.flag));e("SET_MENUS",r),a()})).catch((e=>{r(e)}))}))},logout({commit:e,state:t}){return(0,M.eF)(),(0,A.dg)(),window.history.replaceState(null,"",""),window.history.go(0),!1},resetToken({commit:e}){return new Promise((t=>{(0,M.eF)(),e("RESET_STATE"),t()}))},setStatus({commit:e,state:t}){e("SET_IS_COLLAPSE",t)}};var B={namespaced:!0,state:G,mutations:x,actions:W};r["default"].use(o.Ay);const U=new o.Ay.Store({modules:{app:u,settings:T,permission:I,tagsView:k,user:B},getters:i});var F=U},2171:function(e,t,a){"use strict";a.d(t,{WG:function(){return i},eF:function(){return s},gf:function(){return n}});var r=a(5462);const o="esop_token";function n(){return r.A.get(o)}function i(e){return r.A.set(o,e)}function s(){return r.A.remove(o)}},7120:function(e,t,a){"use strict";a(4603),a(7566),a(8721);var r=a(417),o=a(1974),n=a(7282),i=a(4927);a(2171);const s=r.A.create({baseURL:"/",timeout:3e5});s.interceptors.request.use((e=>(o.A.getters.token&&(e.headers["Authorization"]=o.A.getters.token),e)),(e=>(console.log(e),Promise.reject(e)))),s.interceptors.response.use((e=>{const t=e.data;return 401===e.status&&(0,i.confirm)("登录失效，您可以取消停留在此页面，或重新登录","登录失效",{confirmButtonText:"重新登录",cancelButtonText:"取消",type:"warning"}).then((()=>{o.A.dispatch("user/resetToken").then((()=>{location.reload()}))})),0!=t.code?JSON.parse(e.config.data||"{}")?.returnErr||e.config.params?.returnErr?Promise.reject(t):((0,n.sI)({message:t.message||"Error",type:"error",duration:5e3}),Promise.reject(new Error(t.message||"Error"))):t}),(e=>(console.log(e),(0,n.sI)({message:e.response.data.message,type:"error",duration:5e3}),401===e.response.status&&(void 0).$confirm("登录失效，您可以取消停留在此页面，或重新登录","登录失效",{confirmButtonText:"重新登录",cancelButtonText:"取消",type:"warning"}).then((()=>{o.A.dispatch("user/resetToken").then((()=>{location.reload()}))})),Promise.reject(e)))),t.Ay=s},7282:function(e,t,a){"use strict";a.d(t,{NO:function(){return n},YQ:function(){return i},sI:function(){return o}});a(4114),a(4603),a(7566),a(8721),a(2171);var r=a(4927);const o=e=>(0,r.Message)({...e,offset:80});function n(e,t,a){if(!e||!e.length)return-1;for(var r=0;r<e.length;r++)if(e[r][t]==a)return r;return-1}function i(e){const t=e.toString().length<13?1e3*e:e;let a=new Date(t),r=a.getFullYear(),o=a.getMonth()+1,n=a.getDate(),i=a.getHours(),s=a.getMinutes(),c=a.getSeconds();return o=o<10?"0"+o:o,n=n<10?"0"+n:n,i=i<10?"0"+i:i,s=s<10?"0"+s:s,c=c<10?"0"+c:c,`${r}-${o}-${n} ${i}:${s}:${c}`}["success","warning","info","error"].forEach((e=>{o[e]=t=>("string"===typeof t&&(t={message:t,offset:80}),t.type=e,(0,r.Message)(t))}))}},t={};function a(r){var o=t[r];if(void 0!==o)return o.exports;var n=t[r]={id:r,loaded:!1,exports:{}};return e[r].call(n.exports,n,n.exports,a),n.loaded=!0,n.exports}a.m=e,function(){a.amdO={}}(),function(){var e=[];a.O=function(t,r,o,n){if(!r){var i=1/0;for(d=0;d<e.length;d++){r=e[d][0],o=e[d][1],n=e[d][2];for(var s=!0,c=0;c<r.length;c++)(!1&n||i>=n)&&Object.keys(a.O).every((function(e){return a.O[e](r[c])}))?r.splice(c--,1):(s=!1,n<i&&(i=n));if(s){e.splice(d--,1);var l=o();void 0!==l&&(t=l)}}return t}n=n||0;for(var d=e.length;d>0&&e[d-1][2]>n;d--)e[d]=e[d-1];e[d]=[r,o,n]}}(),function(){a.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return a.d(t,{a:t}),t}}(),function(){a.d=function(e,t){for(var r in t)a.o(t,r)&&!a.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}}(),function(){a.f={},a.e=function(e){return Promise.all(Object.keys(a.f).reduce((function(t,r){return a.f[r](e,t),t}),[]))}}(),function(){a.u=function(e){return"static/js/"+e+"."+{82:"43205d3c",119:"ba77b662",143:"66847604",232:"1f7b88f4",254:"94d14f03",310:"1f179b32",726:"777044ff"}[e]+".js"}}(),function(){a.miniCssF=function(e){return"static/css/"+e+"."+{82:"58818e24",119:"0d6aab14",143:"ec31310a",232:"ece943c6",254:"3cb0cfea",310:"3058886b",726:"ed542b31"}[e]+".css"}}(),function(){a.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){var e={},t="esop-dashboard:";a.l=function(r,o,n,i){if(e[r])e[r].push(o);else{var s,c;if(void 0!==n)for(var l=document.getElementsByTagName("script"),d=0;d<l.length;d++){var u=l[d];if(u.getAttribute("src")==r||u.getAttribute("data-webpack")==t+n){s=u;break}}s||(c=!0,s=document.createElement("script"),s.charset="utf-8",s.timeout=120,a.nc&&s.setAttribute("nonce",a.nc),s.setAttribute("data-webpack",t+n),s.src=r),e[r]=[o];var m=function(t,a){s.onerror=s.onload=null,clearTimeout(p);var o=e[r];if(delete e[r],s.parentNode&&s.parentNode.removeChild(s),o&&o.forEach((function(e){return e(a)})),t)return t(a)},p=setTimeout(m.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=m.bind(null,s.onerror),s.onload=m.bind(null,s.onload),c&&document.head.appendChild(s)}}}(),function(){a.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){a.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e}}(),function(){a.p="/dashboard/"}(),function(){if("undefined"!==typeof document){var e=function(e,t,r,o,n){var i=document.createElement("link");i.rel="stylesheet",i.type="text/css",a.nc&&(i.nonce=a.nc);var s=function(a){if(i.onerror=i.onload=null,"load"===a.type)o();else{var r=a&&a.type,s=a&&a.target&&a.target.href||t,c=new Error("Loading CSS chunk "+e+" failed.\n("+r+": "+s+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=r,c.request=s,i.parentNode&&i.parentNode.removeChild(i),n(c)}};return i.onerror=i.onload=s,i.href=t,r?r.parentNode.insertBefore(i,r.nextSibling):document.head.appendChild(i),i},t=function(e,t){for(var a=document.getElementsByTagName("link"),r=0;r<a.length;r++){var o=a[r],n=o.getAttribute("data-href")||o.getAttribute("href");if("stylesheet"===o.rel&&(n===e||n===t))return o}var i=document.getElementsByTagName("style");for(r=0;r<i.length;r++){o=i[r],n=o.getAttribute("data-href");if(n===e||n===t)return o}},r=function(r){return new Promise((function(o,n){var i=a.miniCssF(r),s=a.p+i;if(t(i,s))return o();e(r,s,null,o,n)}))},o={524:0};a.f.miniCss=function(e,t){var a={82:1,119:1,143:1,232:1,254:1,310:1,726:1};o[e]?t.push(o[e]):0!==o[e]&&a[e]&&t.push(o[e]=r(e).then((function(){o[e]=0}),(function(t){throw delete o[e],t})))}}}(),function(){var e={524:0};a.f.j=function(t,r){var o=a.o(e,t)?e[t]:void 0;if(0!==o)if(o)r.push(o[2]);else{var n=new Promise((function(a,r){o=e[t]=[a,r]}));r.push(o[2]=n);var i=a.p+a.u(t),s=new Error,c=function(r){if(a.o(e,t)&&(o=e[t],0!==o&&(e[t]=void 0),o)){var n=r&&("load"===r.type?"missing":r.type),i=r&&r.target&&r.target.src;s.message="Loading chunk "+t+" failed.\n("+n+": "+i+")",s.name="ChunkLoadError",s.type=n,s.request=i,o[1](s)}};a.l(i,c,"chunk-"+t,t)}},a.O.j=function(t){return 0===e[t]};var t=function(t,r){var o,n,i=r[0],s=r[1],c=r[2],l=0;if(i.some((function(t){return 0!==e[t]}))){for(o in s)a.o(s,o)&&(a.m[o]=s[o]);if(c)var d=c(a)}for(t&&t(r);l<i.length;l++)n=i[l],a.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return a.O(d)},r=self["webpackChunkesop_dashboard"]=self["webpackChunkesop_dashboard"]||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))}();var r=a.O(void 0,[504],(function(){return a(7618)}));r=a.O(r)})();
//# sourceMappingURL=app.515e3d11.js.map