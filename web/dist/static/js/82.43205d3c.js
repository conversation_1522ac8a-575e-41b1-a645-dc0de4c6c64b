"use strict";(self["webpackChunkesop_dashboard"]=self["webpackChunkesop_dashboard"]||[]).push([[82],{9082:function(e,t,s){s.r(t),s.d(t,{default:function(){return g}});var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"login-container"},[t("div",{staticClass:"language-switcher"},[t("el-button",{on:{click:e.switchLanguage}},[e._v(e._s(e.$t("login.switchLang")))])],1),t("div",{staticClass:"flexBox"},[t("div",[t("el-form",{ref:"loginForm",staticClass:"login-form",attrs:{model:e.loginForm,rules:e.loginRules,"auto-complete":"on","label-position":"left"},nativeOn:{submit:function(t){return t.preventDefault(),e.handleLogin.apply(null,arguments)}}},[t("div",{staticClass:"flex"},[t("div",{staticClass:"image-container"},[t("el-carousel",{staticClass:"carousel",attrs:{interval:5e3,arrow:"always",height:"400px"}},[t("el-carousel-item",[t("el-image",{attrs:{"z-index":1,src:s(1365),fit:"cover"}})],1)],1)],1),t("div",{staticClass:"right-container"},[t("div",{staticClass:"form-container"},[t("div",{staticClass:"title"},[e._v(e._s(e.$t("login.esopBackend")))]),t("el-form-item",{attrs:{prop:"username"}},[t("span",{staticClass:"svg-container"},[t("el-image",{staticStyle:{width:"16px"},attrs:{src:s(9843)}})],1),t("el-input",{ref:"username",attrs:{placeholder:e.$t("login.enterUsername"),name:"username",type:"text",tabindex:"1","auto-complete":"on"},model:{value:e.loginForm.username,callback:function(t){e.$set(e.loginForm,"username",t)},expression:"loginForm.username"}})],1),t("el-form-item",{attrs:{prop:"password"}},[t("span",{staticClass:"svg-container"},[t("el-image",{staticStyle:{width:"16px"},attrs:{src:s(521)}})],1),t("el-input",{key:e.passwordType,ref:"password",attrs:{type:e.passwordType,placeholder:e.$t("login.enterPassword"),name:"password",tabindex:"2","auto-complete":"off","show-password":""},model:{value:e.loginForm.password,callback:function(t){e.$set(e.loginForm,"password",t)},expression:"loginForm.password"}})],1),t("div",{staticStyle:{display:"flex"}},[t("el-checkbox",{model:{value:e.isRemenberPw,callback:function(t){e.isRemenberPw=t},expression:"isRemenberPw"}},[e._v(e._s(e.$t("login.rememberPassword")))])],1),t("div",{staticClass:"button-container"},[t("el-button",{attrs:{loading:e.loading,type:"primary"},nativeOn:{click:function(t){return t.preventDefault(),e.handleLogin.apply(null,arguments)}}},[e._v(e._s(e.$t("login.login")))])],1)],1)])])])],1)])])},o=[],i=(s(4114),{data(){const e=(e,t,s)=>{t.length<=0?s(new Error(this.$i18n.t("login.enterUsername"))):s()},t=(e,t,s)=>{t.length<=0?s(new Error(this.$i18n.t("login.enterPassword"))):s()};return{imageUrl:this.$imageUrl,loginForm:{username:"",password:"",code:"",codeValue:""},loginRules:{username:[{required:!0,trigger:"change",validator:e}],password:[{required:!0,trigger:"change",validator:t}]},loading:!1,passwordType:"password",redirect:void 0,isRemenberPw:!1,codeImage:"",banner:[],companyList:[],dictList:[]}},methods:{switchLanguage(){this.$i18n.locale="en"===this.$i18n.locale?"zh":"en",localStorage.setItem("appLanguage",this.$i18n.locale)},handleLogin(e){this.$refs.loginForm.validate((e=>{if(!e)return console.log("error submit!!"),!1;this.loading=!0,this.$store.dispatch("user/login",this.loginForm).then((async e=>{this.$message.success(this.$t("login.loginSuccess")),this.$router.push({path:"/Template"}),this.saveUnAndPw(e),this.loading=!1})).catch((()=>{this.loading=!1}))}))},saveUnAndPw(e){localStorage.setItem("greemall_login",JSON.stringify(e.Admin)),this.isRemenberPw&&this.setCookie(this.loginForm.username,this.loginForm.password,7)},setCookie(e,t,s){var a=new Date;a.setTime(a.getTime()+864e5*s),window.document.cookie="greemall_username="+e+";path=/;expires="+a.toGMTString(),window.document.cookie="greemall_password="+t+";path=/;expires="+a.toGMTString()},getCookie:function(){if(document.cookie.length>0)for(var e=document.cookie.split("; "),t=0;t<e.length;t++){var s=e[t].split("=");"greemall_username"==s[0]?this.loginForm.username=s[1]:"greemall_password"==s[0]&&(this.loginForm.password=s[1])}},clearCookie:function(){this.setCookie("","",-1)},toEnterApply(){this.$router.push({name:"enterApply"})},toRetrievePassword(){this.$router.push({name:"retrievePassword"})}}}),n=i,r=s(1656),l=(0,r.A)(n,a,o,!1,null,"6738e9fa",null),g=l.exports},1365:function(e,t,s){e.exports=s.p+"static/img/bg2.b0e7e3a3.png"},521:function(e){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAApNJREFUWEftlstrE1EUxr8zSenKRyYVXyCKuagbN4KoVWhd+PgD7MaVD5BMzMpKoS5sFxZ8rdJMEHxsXNm9qJsWtIqCGzcqNyIIWsXkxsdChGaOTEzqmMfMTTJZCN7NwNxzvvO795x77iW0OcysPADQdpCzA6DB3+48DzaeA/xCpcSDdiSpHeO4LccZuODnQ8C5oiWmdHW1AUxb3gRwTFP4lrLEcR1bLQAzlz8D5it1gj8AkgD3AdjWEIxoVCUTV4MgAgFWZF5tikSi8wCvXRJjZ9KI9l8rnNq44P5bNv0y3mdE9wO48ycgLZTLi4Nf01vfBqTMnzFmy2kCUt7gKrVlopmXacsjXggGsiVLnO4KIJ6TeWZsroo8VZbY5Sdo2vIugMOuDRHeFJMi0THAQObdOify831NgInGSsnEJT/BWE4eJcbtmo1R7l9fSG/40MrHtwbMnDwIxr2as1PG8Je0mPMDWJmRQ0YEs0s2hEMqKe53BpDNj4L4clcATGdVKlF/gjx8Pssxs68nQMb57gCcyVZFW6mToHPa6/mWAPFc/qLDvDMMAIPoWTGZGGum1RTAtOVnAANhBPdoFJQlVtVrNgCYtnS7mdtQejFmlCVGvMKNAFn5EYTVvYgOxieVEmv8AWzJPQleFVWW+GvRzVLwTwHMVBemXTOh7YC3LTe0X58chgLAwFzJEsPeODFbzhIwFFQ/oQAAaDhOusc3LAB3oSPKEpUaqH+I+O1CaADeNOhuvwv2HyC0Hajk2cGeytfA46Dqr82HC6Ab1WOnA/DNfep3oK3j8l1ZYnnAZZR/CPBeHbX2beiRshL7gq7jEyBcb19cw4NxUqXEDV8Ad7LS26OYAmO3hmywCeGJs4jxZk/6X9L5ETAL1SqOAAAAAElFTkSuQmCC"},9843:function(e){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAplJREFUWEe1ljFME2EYht+vRYImJvYaYzTgIL1WaRSj0ejm4qAjRDYTjRLgThaii4t1cdG4wF0haDRxE2HUwcVNo9EIWrC94iAEY7RXJyXI3WeugEqF3v9fy429932+p3/y5z6C5LOt3zpBYW4jUAKM1lKdMM7gLDs09r1XfSaDJJlwJG31EMOs1GGCVuxR06JcYYGokTvHRPdEwMR8vqDH7wtlRULbjXzMIbZEsiuZMJP6VY/l/TpCJ6CY1mMAp/xgZe+f2Jp62q/jK6AMWi1wkfEDrfk+hKTdrU5W6voLmNYZAA8DCQAdtqaOVCdgZFOg0LVAAuxet/VEqiqBqJFrY6LRIALE3F7Q42NVCUTMDwcI4fEgAgyntajtnahKoPH2zOYfDfNZAE2SEjNb5hsSs31NP6sS8MpRI3+JiftlBIipt6DHBvw6vrdgBaCYlncTvBsh8ozYmtohEhQW8GCKaV0EMOwD7rQ19Y7IcC8jJbAscYwZZ0FoASNZGkTIgDFJhAe2pr4QHR5IQAYukpU+ARGoTKaigDKYOwmXkgwcBrCPgJ0AdvkMmGPgM4ApAl4jxBm7O/50vc6aApGh6d206FwFUZfMv1k3yzzEdeEbxa7mT+WZ/wS8lSsUxl0Ae2oy/C/ko+vgQvnKtkogakwfYXJf1njwKhxx6GhBb3618uMfgaq++7LG/+wJSwKpTH1kR/1zYhySZQXJM+FN8cvCcaSSCyUBxchfBvHNILDAHaYrth67RVsHpqKbwnVvwWgMDAtSJMz+chYPksy6HWROpY63vpNiWo8AtNcaLsgbJSWdnwDzfsFCbWNE77wT+ObtHLUlC9MKngALxzcg6Am8B5a/6xswwAeZIWVp7+8U+MrVWm8O7A7/Bohq1Fxl3ySBAAAAAElFTkSuQmCC"}}]);
//# sourceMappingURL=82.43205d3c.js.map