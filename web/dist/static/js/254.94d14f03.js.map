{"version": 3, "file": "static/js/254.94d14f03.js", "mappings": "2JAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,YAAY,CAACE,IAAI,YAAYC,YAAY,mBAAmBC,MAAMN,EAAIO,KAAKC,MAAM,CAAC,OAASR,EAAIS,YAAY,KAAO,aAAa,QAAUT,EAAIU,QAAQ,SAAWV,EAAIW,UAAY,EAAiB,kBAAiB,EAAM,gBAAgBX,EAAIY,aAAa,YAAYZ,EAAIa,aAAa,cAAcb,EAAIc,qBAAqB,aAAad,EAAIe,oBAAoB,UAAYf,EAAIgB,WAAW,OAAShB,EAAIiB,kBAAkBf,EAAG,MAAM,CAACG,YAAY,UAAU,CAACL,EAAIkB,GAAIlB,EAAImB,UAAU,SAASC,EAAKC,GAAO,OAAOnB,EAAG,MAAM,CAACoB,IAAID,EAAMhB,YAAY,QAAQ,CAACH,EAAG,MAAM,CAACG,YAAY,MAAMG,MAAM,CAAC,GAAK,gBAAgBe,GAAG,CAAC,UAAY,SAASC,GAAQJ,EAAKK,OAAQ,CAAI,EAAE,SAAW,SAASD,GAAQJ,EAAKK,OAAQ,CAAK,IAAI,CAAEzB,EAAI0B,WAAWN,EAAKO,KAAMzB,EAAG,WAAW,CAACE,IAAI,MAAMwB,UAAS,EAAKC,YAAY,CAAC,MAAQ,QAAQ,OAAS,SAASrB,MAAM,CAAC,IAAMR,EAAI8B,SAAWV,EAAKO,IAAI,mBAAmB3B,EAAI+B,gBAAgB,IAAM,SAASR,GAAG,CAAC,MAAQ,SAASC,GAAiC,OAAzBA,EAAOQ,kBAAyBhC,EAAIiC,gBAAgBC,MAAM,KAAMC,UAAU,KAAMnC,EAAIoC,WAAWhB,EAAKO,KAAMzB,EAAG,QAAQ,CAAC2B,YAAY,CAAC,MAAQ,QAAQ,OAAS,QAAQ,QAAU,SAASrB,MAAM,CAAC,SAAW,KAAK,CAACN,EAAG,SAAS,CAACM,MAAM,CAAC,IAAMR,EAAI8B,SAAWV,EAAKO,IAAI,KAAO,iBAAiBzB,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,IAAI,CAACI,MAAMN,EAAIqC,iBAAiBjB,EAAKO,OAAOzB,EAAG,MAAM,CAACG,YAAY,aAAa,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGnB,EAAKb,MAAQP,EAAIwC,mBAAmBpB,EAAKO,WAAWzB,EAAG,MAAM,CAACuC,WAAW,CAAC,CAAClC,KAAK,OAAOmC,QAAQ,SAASC,MAAOvB,EAAKK,MAAOmB,WAAW,eAAevC,YAAY,QAAQ,CAAEL,EAAI6C,WAAWzB,EAAKO,KAAMzB,EAAG,IAAI,CAACG,YAAY,kBAAkBkB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAI8C,YAAY1B,EAAK,KAAKlB,EAAG,IAAI,CAACG,YAAY,mBAAmBkB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAI+C,aAAa3B,EAAKO,IAAI,KAAO3B,EAAIgB,WAAoHhB,EAAIgD,KAA5G9C,EAAG,IAAI,CAACG,YAAY,kBAAkBkB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAIiD,YAAY7B,EAAKO,IAAI,KAAgB3B,EAAIgB,WAAmHhB,EAAIgD,KAA3G9C,EAAG,IAAI,CAACG,YAAY,iBAAiBkB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAIkD,YAAY9B,EAAKO,IAAI,QAAiB,IAAI,IAAI3B,EAAImB,SAASgC,OAASnD,EAAIW,UAAWT,EAAG,MAAM,CAACG,YAAY,MAAMkB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAIiD,aAAa,IAAI,CAAC/C,EAAG,IAAI,CAACG,YAAY,mBAAmBL,EAAIgD,MAAM,IAAI,EACpuE,EACII,EAAkB,G,8DCqCtB,GACAC,MAAA,CACArC,WAAAsC,QACAC,SAAAC,OACAjD,KAAAiD,OACAvC,eAAA,CACAwC,KAAAD,OACAE,QAAA,IAEAvC,SAAAwC,MACAhD,UAAA,CACA8C,KAAAG,OACAF,QAAA,GAEArC,MAAA,CACAoC,KAAAG,OACAF,QAAA,IAGAG,IAAAA,GACA,OAIApD,YAAAqD,+BACAhC,SAAAgC,iBACApD,QAAA,CACAqD,cAAAC,EAAAA,EAAAC,QAAAC,OAGAC,UAAA,GACAC,eAAA,EACAC,cAAA,GACAC,KAAA,GAEA,EACAC,SAAA,CACAC,aAAA,CACAC,IAAA,WACA,YAAAtD,UAAA,KAAAA,SAAAgC,OAAA,CACA,EACAuB,IAAA,SAAAC,GAAA,IAGAC,QAAA,CACAC,SAAAA,CAAAC,GACA,KAAAC,MAAA,QAAAD,EACA,EACA7C,eAAAA,GACA+C,YAAA,KACA,IAAAC,EAAAC,SAAAC,cAAA,6BACAF,GAEAA,EAAAG,iBAAA,SAAAC,IACA,mCAAAA,EAAAC,OAAAC,WAAAC,WAGAN,SAAAC,cAAA,2BAAAM,OAAA,GACA,GACA,IACA,EACA5E,YAAAA,CAAA6E,EAAAvE,GACA,IAAAA,EAAAgC,OACA,KAAAhC,SAAA,GAEA,KAAAA,SAAAA,CAEA,EACAwE,OAAAA,GACA,6CAAAC,QAAA,SAAAC,IACA,MAAAA,EAAA,GAAAC,KAAAC,SAAA,KAAAC,SAAA,KAEA,EACApF,YAAAA,CAAA8E,GAEA,MAAAO,EAAA,KAAAC,cAAAR,GAEA,IAAAO,EAEA,OADA,KAAAE,kBAAAT,IACA,EAIA,GADA,KAAApB,KAAA8B,KAAAV,EAAAnF,MACA,KAAA+D,KAAAnB,OAAA,KAAAxC,UAGA,OAFA,KAAA0F,SAAAC,QAAA,KAAAC,MAAAC,EAAA,uBAAAC,MAAA,KAAA9F,aACA,KAAA+F,oBACA,EAEA,GAAAhB,EAAAjC,KAAAkD,SAAA,UAEA,IAAAC,EAAA,IAAAC,WACAD,EAAAE,OAAAzB,IACA,IAAA0B,EAAA1B,EAAAC,OAAA0B,OACAC,EAAA/B,SAAAgC,cAAA,OACAD,EAAAE,IAAAJ,EACAE,EAAAH,OAAA,KACA,KAAA/B,MACA,WACA,CACAqC,MAAAH,EAAAG,MACAC,OAAAJ,EAAAI,SAKAC,QAAAC,IAAA,MAAAN,EAAAG,OACAE,QAAAC,IAAA,MAAAN,EAAAI,OAAA,CACA,EAEAT,EAAAY,cAAA9B,EAEA,MAEA,GAAAA,EAAAjC,KAAAkD,SAAA,UACAW,QAAAC,IAAA7B,EAAA,QACA,MAAA+B,EAAAvC,SAAAgC,cAAA,SACAO,EAAAN,IAAAO,IAAAC,gBAAAjC,GACA+B,EAAArC,iBAAA,uBACA,KAAAL,MACA,WACA,CACAqC,MAAAK,EAAAG,WACAP,OAAAI,EAAAI,cAIAP,QAAAC,IAAA,iBAAAE,EAAAG,WACAH,EAAAI,YAAA,GAGA,CAGA,QACA,EACA3B,aAAAA,CAAAR,GAEA,GADA4B,QAAAC,IAAA,KAAAhE,UACA,eAAAA,SACA,OAAAmC,EAAAjC,KAAAkD,SAAA,SACA,kBAAApD,SACA,OAAAmC,EAAAjC,KAAAkD,SAAA,SACA,wBAAApD,SACA,OAAAmC,EAAAjC,KAAAkD,SAAA,UAAAjB,EAAAjC,KAAAkD,SAAA,SACA,iBAAApD,SAAA,CAEA,MAAAuE,EAAA,KAAA7G,eAAA8G,MAAA,KAAAC,KAAA5G,GAAAA,EAAA6G,SACA,OAAAH,EAAAnB,SAAAjB,EAAAjC,KACA,CACA,QACA,EACA0C,iBAAAA,CAAAT,GACA,MAAAwC,EAAA,KAAAC,oBACA,KAAA9B,SAAAC,QAAA4B,GACA,KAAAxB,kBACA,EACAyB,iBAAAA,GACA,YAAA5E,UACA,YACA,YAAAgD,MAAAC,EAAA,oBACA,YACA,YAAAD,MAAAC,EAAA,oBACA,kBACA,YAAAD,MAAAC,EAAA,gCACA,UACA,YAAAD,MAAAC,EAAA,gCACA,WACA,YAAAD,MAAAC,EAAA,iCACA,QACA,SAEA,EACAE,gBAAAA,GACA,KAAA0B,MAAAC,UAAAC,aACA,KAAAhE,KAAA,GACA,KAAAnD,SAAA,SAAAA,SACA,EACAL,oBAAAA,GACA,KAAAiE,MAAA,kBACA,EACAhE,mBAAAA,CAAAwH,EAAA7C,EAAAvE,GACA,KAAA4D,MAAA,mBACA,KAAA5D,SAAAiF,KAAA,CACA7F,KAAAmF,EAAAnF,KACAkB,OAAA,EACAE,IAAA+D,EAAA8C,SAAA3E,KAAA4E,YAGA,KAAA1D,MACA,UACA,CACAxE,KAAAmF,EAAAnF,KACAkB,OAAA,EACAE,IAAA+D,EAAA8C,SAAA3E,KAAA4E,WAEA,KAAAlI,KACA,KAAAc,MAEA,EACAU,aAAAA,GACA,IAAA2G,EAAA,GASA,OARA,KAAAvH,UAAA,KAAAA,SAAAgC,OAAA,GACA,KAAAhC,SAAAwH,SAAAvH,IACA,QAAAM,WAAAN,EAAAO,KAAA,CACA,MAAAiH,EAAA,KAAA9G,SAAAV,EAAAO,IACA+G,EAAAtC,KAAAwC,EACA,KAGAF,CACA,EACAG,YAAAA,CAAAlH,GACA,IAAA+G,EAAA,GACA,KAAAvH,SAAAwH,SAAAvH,IACA,KAAAM,WAAAN,EAAAO,MACA+G,EAAAtC,KAAAhF,EACA,IAEA,IAAAC,GAAAyH,EAAAA,EAAAA,IAAAJ,EAAA,MAAA/G,IACA,IAAAN,GAAA,KAAA+G,MAAAnB,KAAA,KAAAmB,MAAAnB,IAAA5F,KACA,KAAA+G,MAAAnB,IAAA5F,GAAA0H,YAAA,EAEA,EACAC,YAAAA,CAAArH,GACA,EAEAmB,WAAAA,CAAA1B,GACA,KAAAM,WAAAN,EAAAO,KACA,KAAAkH,aAAAzH,EAAAO,KACA,KAAAS,WAAAhB,EAAAO,KACA,KAAAqH,aAAA5H,EAAAO,KAGAsH,OAAAC,KAAA,KAAApH,SAAAV,EAAAO,IAAA,SAEA,EACAD,UAAAA,CAAAC,GACA,IAAAA,EAAA,SACA,MAAAwH,EAAAxH,EAAAyH,UAAAzH,EAAA0H,YAAA,QAAAC,cACAC,EAAA,kCACA,OAAAA,EAAA5C,SAAAwC,EACA,EACA/G,UAAAA,CAAAT,GACA,IAAAA,EAAA,SACA,MAAA6H,EAAA,2BACAC,EAAA9H,EAAAyH,UAAAzH,EAAA0H,YAAA,QAAAC,cACA,OAAAE,EAAA7C,SAAA8C,EACA,EACApH,gBAAAA,CAAAV,GACA,IAAAA,EAAA,yBACA,MAAA+H,EAAA/H,EAAAyH,UAAAzH,EAAA0H,YAAA,QAAAC,cACA,OAAAI,GACA,UACA,6BACA,UACA,WACA,yBACA,UACA,WACA,yBACA,UACA,WACA,yBACA,QACA,yBAEA,EACAlH,kBAAAA,CAAAb,GACA,OAAAA,EACAA,EAAAoG,MAAA,KAAA4B,MADA,EAEA,EACA9G,UAAAA,CAAAlB,GACA,YAAAD,WAAAC,IAAA,KAAAS,WAAAT,EACA,EACAoB,YAAAA,CAAApB,GACAsH,OAAAC,KAAA,KAAApH,SAAAH,EAAA,SACA,EACAsB,WAAAA,CAAA2G,GACA,KAAAtF,KAAA,GACA,KAAAD,cAAAuF,EAEA,KAAAxB,MAAAC,WACA,KAAAD,MAAAC,UAAAwB,IAAA1E,cAAA,SAAAM,OAEA,EACAvC,WAAAA,CAAAvB,GACA,MAAAN,GAAAyH,EAAAA,EAAAA,IAAA,KAAA3H,SAAA,MAAAQ,IACA,IAAAN,GACA,KAAAF,SAAA2I,OAAAzI,EAAA,EAEA,EACA0I,YAAAA,GACA,KAAA3B,MAAAC,WACA,KAAAD,MAAAC,UAAA2B,QAEA,IC7U0P,I,UCQtPC,GAAY,OACd,EACAlK,EACAqD,GACA,EACA,KACA,WACA,MAIF,EAAe6G,EAAiB,O,oECnBhC,IAAIlK,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,oBAAoB,CAACH,EAAG,MAAM,CAACG,YAAY,uBAAuB,CAACH,EAAG,MAAM,CAACG,YAAY,aAAa6J,MAAO,CAAE9C,MAAOpH,EAAImK,UAAY,OAAS,CAACjK,EAAG,UAAU,CAACG,YAAY,YAAYG,MAAM,CAAC,OAAS,UAAU,CAACN,EAAG,MAAM,CAACG,YAAY,cAAcG,MAAM,CAAC,KAAO,UAAU4J,KAAK,UAAU,CAAClK,EAAG,OAAO,CAACG,YAAY,cAAc,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIqK,GAAG,+BAAiC,WAAWnK,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACH,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,gBAAgBe,GAAG,CAAC,MAAQvB,EAAIsK,iBAAiB,CAACtK,EAAIsC,GAAG,IAAItC,EAAIuC,GAAGvC,EAAIqK,GAAG,oCAAsC,QAAQ,QAAQ,KAAKnK,EAAG,WAAW,CAACG,YAAY,eAAeG,MAAM,CAAC,YAAcR,EAAIqK,GAAG,2CAA6C,YAAY,UAAY,GAAG,cAAc,kBAAkBE,MAAM,CAAC5H,MAAO3C,EAAIwK,WAAYC,SAAS,SAAUC,GAAM1K,EAAIwK,WAAWE,CAAG,EAAE9H,WAAW,gBAAgB1C,EAAG,UAAU,CAACE,IAAI,OAAOC,YAAY,iBAAiBG,MAAM,CAAC,KAAOR,EAAI2K,cAAc,MAAQ3K,EAAI4K,aAAa,qBAAqB5K,EAAI6K,WAAW,WAAW,KAAK,iBAAiB7K,EAAI8K,cAAc,qBAAoB,EAAK,aAAa9K,EAAIqK,GAAG,+BAA+B,wBAAuB,GAAO9I,GAAG,CAAC,aAAavB,EAAI+K,gBAAgB,cAAc/K,EAAIgL,qBAAqB,IAAI,GAAG9K,EAAG,MAAM,CAACG,YAAY,gBAAgBkB,GAAG,CAAC,UAAYvB,EAAIiL,eAAe/K,EAAG,MAAM,CAACG,YAAY,eAAe6J,MAAO,CAAE9C,MAAO,gBAAkBpH,EAAImK,UAAY,GAAK,QAAU,CAAGnK,EAAIkL,eAAiUhL,EAAG,MAAM,CAAEF,EAAIkL,eAAeC,SAAUjL,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACH,EAAG,UAAU,CAACG,YAAY,YAAYG,MAAM,CAAC,OAAS,UAAU,CAACN,EAAG,MAAM,CAACG,YAAY,cAAcG,MAAM,CAAC,KAAO,UAAU4J,KAAK,UAAU,CAAClK,EAAG,OAAO,CAACG,YAAY,cAAc,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIqK,GAAG,mCAAqC,WAAWnK,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACH,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,gBAAgBe,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAIoL,WAAWpL,EAAIkL,eAAe,IAAI,CAAClL,EAAIsC,GAAG,IAAItC,EAAIuC,GAAGvC,EAAIqK,GAAG,sCAAwC,MAAM,OAAOnK,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,SAAS,KAAO,QAAQ,KAAO,kBAAkBe,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAIqL,aAAarL,EAAIkL,eAAe,IAAI,CAAClL,EAAIsC,GAAG,IAAItC,EAAIuC,GAAGvC,EAAIqK,GAAG,kCAAoC,MAAM,QAAQ,KAAKnK,EAAG,MAAM,CAACG,YAAY,eAAe,CAACH,EAAG,SAAS,CAACM,MAAM,CAAC,OAAS,KAAK,CAACN,EAAG,SAAS,CAACM,MAAM,CAAC,KAAO,IAAI,CAACN,EAAG,MAAM,CAACG,YAAY,aAAa,CAACH,EAAG,OAAO,CAACG,YAAY,cAAc,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIqK,GAAG,qCAAuC,QAAQ,OAAOnK,EAAG,OAAO,CAACG,YAAY,cAAc,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIkL,eAAeI,YAAc,YAAYpL,EAAG,SAAS,CAACM,MAAM,CAAC,KAAO,IAAI,CAACN,EAAG,MAAM,CAACG,YAAY,aAAa,CAACH,EAAG,OAAO,CAACG,YAAY,cAAc,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIqK,GAAG,6BAA+B,SAAS,OAAOnK,EAAG,OAAO,CAACG,YAAY,cAAc,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIkL,eAAeK,aAAe,YAAYrL,EAAG,SAAS,CAACM,MAAM,CAAC,KAAO,IAAI,CAACN,EAAG,MAAM,CAACG,YAAY,aAAa,CAACH,EAAG,OAAO,CAACG,YAAY,cAAc,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIqK,GAAG,qCAAuC,QAAQ,OAAOnK,EAAG,OAAO,CAACG,YAAY,cAAc,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIkL,eAAeM,YAAc,YAAYtL,EAAG,SAAS,CAACM,MAAM,CAAC,KAAO,IAAI,CAACN,EAAG,MAAM,CAACG,YAAY,aAAa,CAACH,EAAG,OAAO,CAACG,YAAY,cAAc,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIqK,GAAG,iCAAmC,QAAQ,OAAOnK,EAAG,SAAS,CAACM,MAAM,CAAC,KAAOR,EAAIkL,eAAeO,SAAW,UAAY,SAAS,KAAO,UAAU,CAACzL,EAAIsC,GAAG,IAAItC,EAAIuC,GAAGvC,EAAIkL,eAAeO,SAAYzL,EAAIqK,GAAG,iCAAmC,KAASrK,EAAIqK,GAAG,kCAAoC,MAAO,QAAQ,KAAKnK,EAAG,SAAS,CAACM,MAAM,CAAC,KAAO,IAAI,CAACN,EAAG,MAAM,CAACG,YAAY,aAAa,CAACH,EAAG,OAAO,CAACG,YAAY,cAAc,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIqK,GAAG,kCAAkC,OAAOnK,EAAG,OAAO,CAACF,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIkL,eAAeQ,SAAW,YAAYxL,EAAG,SAAS,CAACM,MAAM,CAAC,KAAO,IAAI,CAACN,EAAG,MAAM,CAACG,YAAY,aAAa,CAACH,EAAG,OAAO,CAACG,YAAY,cAAc,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIqK,GAAG,qCAAuC,UAAU,OAAOnK,EAAG,OAAO,CAACG,YAAY,cAAc,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAI2L,WAAW3L,EAAIkL,eAAeU,aAAe,aAAa,IAAI,KAAK1L,EAAG,UAAU,CAACG,YAAY,0BAA0BG,MAAM,CAAC,OAAS,UAAU,CAACN,EAAG,MAAM,CAACG,YAAY,cAAcG,MAAM,CAAC,KAAO,UAAU4J,KAAK,UAAU,CAAClK,EAAG,OAAO,CAACG,YAAY,cAAc,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIqK,GAAG,sCAAsCnK,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACH,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,mBAAmBe,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAI6L,eAAe7L,EAAIkL,eAAe,IAAI,CAAClL,EAAIsC,GAAG,IAAItC,EAAIuC,GAAGvC,EAAIqK,GAAG,kCAAkC,OAAOnK,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,mBAAmBe,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAI8L,gBAAgB9L,EAAIkL,eAAe,IAAI,CAAClL,EAAIsC,GAAG,IAAItC,EAAIuC,GAAGvC,EAAIqK,GAAG,mCAAmC,OAAOnK,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,gBAAgBe,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAI+L,cAAc/L,EAAIkL,eAAe,IAAI,CAAClL,EAAIsC,GAAG,IAAItC,EAAIuC,GAAGvC,EAAIqK,GAAG,mCAAmC,QAAQ,KAAKnK,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAAEL,EAAIkL,eAAec,SAAU9L,EAAG,MAAM,CAACG,YAAY,oBAAoB,CAACH,EAAG,MAAM,CAACG,YAAY,qBAAqBG,MAAM,CAAC,IAAMR,EAAIkL,eAAec,SAASC,UAAU,IAAM,WAAW/L,EAAG,MAAM,CAACG,YAAY,iBAAiB,CAACH,EAAG,IAAI,CAACG,YAAY,iBAAiB,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIkL,eAAec,SAASzL,SAASL,EAAG,IAAI,CAACG,YAAY,iBAAiB,CAACL,EAAIsC,GAAG,OAAOtC,EAAIuC,GAAGvC,EAAIkL,eAAec,SAASvI,MAAM,UAAUzD,EAAIuC,GAAGvC,EAAIkL,eAAec,SAASE,WAAWhM,EAAG,MAAM,CAACG,YAAY,oBAAoB,CAACH,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,OAAO,KAAO,SAASe,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAImM,aAAanM,EAAIkL,eAAec,SAAS,IAAI,CAAChM,EAAIsC,GAAG,YAAYpC,EAAG,YAAY,CAACG,YAAY,cAAcG,MAAM,CAAC,KAAO,OAAO,KAAO,SAASe,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAIoM,eAAepM,EAAIkL,eAAe,IAAI,CAAClL,EAAIsC,GAAG,WAAW,KAAKpC,EAAG,MAAM,CAACG,YAAY,eAAe,CAACH,EAAG,WAAW,CAACM,MAAM,CAAC,YAAcR,EAAIqK,GAAG,uCAAuC,aAAa,OAAO,QAAQ,GAAGnK,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,UAAU,CAACG,YAAY,aAAaG,MAAM,CAAC,OAAS,UAAU,CAACN,EAAG,MAAM,CAACG,YAAY,cAAcG,MAAM,CAAC,KAAO,UAAU4J,KAAK,UAAU,CAAClK,EAAG,OAAO,CAACG,YAAY,cAAc,CAACL,EAAIsC,GAAG,SAAStC,EAAIuC,GAAGvC,EAAIkL,eAAemB,UAAUnM,EAAG,MAAM,CAACG,YAAY,kBAAkB,CAACH,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,gBAAgBe,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAIsM,gBAAgBtM,EAAIkL,eAAe,IAAI,CAAClL,EAAIsC,GAAG,IAAItC,EAAIuC,GAAGvC,EAAIqK,GAAG,qCAAuC,QAAQ,OAAOnK,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,SAAS,KAAO,QAAQ,KAAO,kBAAkBe,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAIuM,kBAAkBvM,EAAIkL,eAAe,IAAI,CAAClL,EAAIsC,GAAG,IAAItC,EAAIuC,GAAGvC,EAAIqK,GAAG,uCAAyC,QAAQ,OAAOnK,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,kBAAkBe,GAAG,CAAC,MAAQvB,EAAIwM,oBAAoB,CAACxM,EAAIsC,GAAG,YAAYpC,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQ,KAAO,sBAAsB,SAA4C,IAAjCR,EAAIyM,kBAAkBtJ,QAAc5B,GAAG,CAAC,MAAQvB,EAAI0M,YAAY,CAAC1M,EAAIsC,GAAG,aAAa,KAAKpC,EAAG,MAAM,CAACG,YAAY,cAAc,CAACH,EAAG,SAAS,CAACM,MAAM,CAAC,OAAS,KAAK,CAACN,EAAG,SAAS,CAACM,MAAM,CAAC,KAAO,IAAI,CAACN,EAAG,MAAM,CAACG,YAAY,aAAa,CAACH,EAAG,MAAM,CAACG,YAAY,eAAe,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIkL,eAAeyB,OAAS,MAAMzM,EAAG,MAAM,CAACG,YAAY,cAAc,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIqK,GAAG,+BAAiC,eAAenK,EAAG,SAAS,CAACM,MAAM,CAAC,KAAO,IAAI,CAACN,EAAG,MAAM,CAACG,YAAY,oBAAoB,CAACH,EAAG,MAAM,CAACG,YAAY,eAAe,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIkL,eAAe0B,QAAU,MAAM1M,EAAG,MAAM,CAACG,YAAY,cAAc,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIqK,GAAG,gCAAkC,eAAenK,EAAG,SAAS,CAACM,MAAM,CAAC,KAAO,IAAI,CAACN,EAAG,MAAM,CAACG,YAAY,qBAAqB,CAACH,EAAG,MAAM,CAACG,YAAY,eAAe,CAACL,EAAIsC,GAAGtC,EAAIuC,IAAIvC,EAAIkL,eAAeyB,OAAS,IAAM3M,EAAIkL,eAAe0B,QAAU,OAAO1M,EAAG,MAAM,CAACG,YAAY,cAAc,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIqK,GAAG,iCAAmC,gBAAgB,IAAI,GAAGnK,EAAG,MAAM,CAAC2B,YAAY,CAAC,OAAS,sBAAsB,aAAa,SAAS,CAAC3B,EAAG,WAAW,CAAC2B,YAAY,CAAC,MAAQ,OAAO,aAAa,QAAQrB,MAAM,CAAC,KAAOR,EAAIkL,eAAe2B,UAAUtL,GAAG,CAAC,mBAAmBvB,EAAI8M,wBAAwB,CAAC5M,EAAG,kBAAkB,CAACM,MAAM,CAAC,KAAO,YAAY,MAAQ,QAAQN,EAAG,kBAAkB,CAACM,MAAM,CAAC,KAAO,aAAa,MAAQ,OAAO,MAAQ,QAAQ,wBAAwB,MAAMN,EAAG,kBAAkB,CAACM,MAAM,CAAC,KAAO,cAAc,MAAQ,QAAQ,MAAQ,QAAQ,wBAAwB,MAAMN,EAAG,kBAAkB,CAACM,MAAM,CAAC,MAAQ,OAAO,MAAQ,KAAK,MAAQ,UAAUuM,YAAY/M,EAAIgN,GAAG,CAAC,CAAC1L,IAAI,UAAU2L,GAAG,SAASC,GAAO,MAAO,CAAChN,EAAG,SAAS,CAACM,MAAM,CAAC,KAAO0M,EAAMC,IAAI1B,SAAW,UAAY,SAAS,KAAO,UAAU,CAACzL,EAAIsC,GAAG,IAAItC,EAAIuC,GAAG2K,EAAMC,IAAI1B,SAAW,KAAO,MAAM,OAAO,OAAOvL,EAAG,kBAAkB,CAACM,MAAM,CAAC,KAAO,SAAS,MAAQ,OAAO,wBAAwB,MAAMN,EAAG,kBAAkB,CAACM,MAAM,CAAC,MAAQ,KAAK,MAAQ,MAAM,MAAQ,UAAUuM,YAAY/M,EAAIgN,GAAG,CAAC,CAAC1L,IAAI,UAAU2L,GAAG,SAASC,GAAO,MAAO,CAAChN,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,QAAQe,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAIoL,WAAW8B,EAAMC,IAAI,IAAI,CAACnN,EAAIsC,GAAG,QAAQpC,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,OAAO,KAAO,UAAUe,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAIqL,aAAa6B,EAAMC,IAAI,IAAI,CAACnN,EAAIsC,GAAG,QAAQ,QAAQ,IAAI,MAAM,KAAlrQpC,EAAG,MAAM,CAACG,YAAY,eAAe,CAACH,EAAG,WAAW,CAACM,MAAM,CAAC,YAAcR,EAAIqK,GAAG,mCAAqC,iBAAiB,aAAa,KAAK0C,YAAY/M,EAAIgN,GAAG,CAAC,CAAC1L,IAAI,QAAQ2L,GAAG,WAAW,MAAO,CAAC/M,EAAG,IAAI,CAACG,YAAY,kCAAkC,EAAE+M,OAAM,IAAO,MAAK,EAAM,cAAc,OAA64PlN,EAAG,YAAY,CAACM,MAAM,CAAC,MAAQR,EAAIqK,GAAG,qCAAqC,QAAUrK,EAAIqN,OAAO,MAAQ,MAAM,wBAAuB,GAAO9L,GAAG,CAAC,iBAAiB,SAASC,GAAQxB,EAAIqN,OAAO7L,CAAM,EAAE,MAAQxB,EAAIsN,cAAc,CAACpN,EAAG,UAAU,CAACE,IAAI,iBAAiBI,MAAM,CAAC,MAAQR,EAAIuN,WAAW,MAAQvN,EAAIwN,MAAM,cAAc,QAAQ,iBAAiB,SAAS,CAACtN,EAAG,eAAe,CAACM,MAAM,CAAC,MAAQR,EAAIqK,GAAG,oCAAoC,KAAO,eAAe,CAACnK,EAAG,YAAY,CAAC2B,YAAY,CAAC,MAAQ,QAAQrB,MAAM,CAAC,YAAcR,EAAIqK,GAAG,yCAAyC,WAAa,GAAG,eAAe,GAAG,uBAAuB,IAAIE,MAAM,CAAC5H,MAAO3C,EAAIuN,WAAW/B,WAAYf,SAAS,SAAUC,GAAM1K,EAAIyN,KAAKzN,EAAIuN,WAAY,aAAc7C,EAAI,EAAE9H,WAAW,0BAA0B5C,EAAIkB,GAAIlB,EAAI0N,eAAe,SAAStM,GAAM,OAAOlB,EAAG,YAAY,CAACoB,IAAIF,EAAKwI,GAAGpJ,MAAM,CAAC,MAAqB,QAAbY,EAAKb,KAAiB,MAAQa,EAAKb,KAAK,MAAQa,EAAKb,OAAO,IAAG,IAAI,GAAGL,EAAG,eAAe,CAACM,MAAM,CAAC,MAAQR,EAAIqK,GAAG,oCAAoC,KAAO,aAAa,SAAW,KAAK,CAACnK,EAAG,WAAW,CAACM,MAAM,CAAC,YAAcR,EAAIqK,GAAG,yCAAyCE,MAAM,CAAC5H,MAAO3C,EAAIuN,WAAWjC,WAAYb,SAAS,SAAUC,GAAM1K,EAAIyN,KAAKzN,EAAIuN,WAAY,aAAc7C,EAAI,EAAE9H,WAAW,4BAA4B,GAAG1C,EAAG,eAAe,CAACM,MAAM,CAAC,MAAQR,EAAIqK,GAAG,4BAA4B,KAAO,gBAAgB,CAACnK,EAAG,WAAW,CAACM,MAAM,CAAC,UAAW,GAAM+J,MAAM,CAAC5H,MAAO3C,EAAIuN,WAAWhC,YAAad,SAAS,SAAUC,GAAM1K,EAAIyN,KAAKzN,EAAIuN,WAAY,cAAe7C,EAAI,EAAE9H,WAAW,6BAA6B,IAAI,GAAG1C,EAAG,OAAO,CAACG,YAAY,gBAAgBG,MAAM,CAAC,KAAO,UAAU4J,KAAK,UAAU,CAAClK,EAAG,YAAY,CAACqB,GAAG,CAAC,MAAQ,SAASC,GAAQxB,EAAIqN,QAAS,CAAK,IAAI,CAACrN,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIqK,GAAG,qBAAqBnK,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,WAAWe,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAI2N,YAAY,IAAI,CAAC3N,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIqK,GAAG,uBAAuB,IAAI,GAAGnK,EAAG,YAAY,CAACM,MAAM,CAAC,MAAQ,OAAO,QAAUR,EAAI4N,wBAAwB,MAAQ,MAAM,iBAAiB,GAAG,wBAAuB,GAAOrM,GAAG,CAAC,iBAAiB,SAASC,GAAQxB,EAAI4N,wBAAwBpM,CAAM,IAAI,CAACtB,EAAG,MAAM,CAACG,YAAY,0BAA0B,CAACH,EAAG,WAAW,CAACuC,WAAW,CAAC,CAAClC,KAAK,UAAUmC,QAAQ,YAAYC,MAAO3C,EAAI6N,kBAAmBjL,WAAW,sBAAsBf,YAAY,CAAC,MAAQ,QAAQrB,MAAM,CAAC,KAAOR,EAAI8N,UAAU,OAAS,QAAQ,OAAS,KAAK,CAAC5N,EAAG,kBAAkB,CAACM,MAAM,CAAC,MAAQ,KAAK,MAAQ,UAAUuM,YAAY/M,EAAIgN,GAAG,CAAC,CAAC1L,IAAI,UAAU2L,GAAG,SAASC,GAAO,MAAO,CAAChN,EAAG,WAAW,CAACM,MAAM,CAAC,MAAQ0M,EAAMC,IAAIvD,IAAImE,SAAS,CAAC,OAAS,SAASvM,GAAQ,OAAO,KAAU,GAAEU,MAAM,KAAMC,UAAU,GAAGoI,MAAM,CAAC5H,MAAO3C,EAAIgO,mBAAoBvD,SAAS,SAAUC,GAAM1K,EAAIgO,mBAAmBtD,CAAG,EAAE9H,WAAW,uBAAuB,CAAC5C,EAAIsC,GAAG,OAAO,OAAOpC,EAAG,kBAAkB,CAACM,MAAM,CAAC,MAAQ,MAAM,MAAQ,MAAM,MAAQ,UAAUuM,YAAY/M,EAAIgN,GAAG,CAAC,CAAC1L,IAAI,UAAU2L,GAAG,SAASC,GAAO,MAAO,CAAChN,EAAG,WAAW,CAAC2B,YAAY,CAAC,MAAQ,OAAO,OAAS,OAAO,gBAAgB,OAAOrB,MAAM,CAAC,IAAM0M,EAAMC,IAAIlB,UAAU,mBAAmB,CAACiB,EAAMC,IAAIlB,WAAW,IAAM,UAAU,CAAC/L,EAAG,MAAM,CAACG,YAAY,aAAaG,MAAM,CAAC,KAAO,SAAS4J,KAAK,SAAS,CAAClK,EAAG,IAAI,CAACG,YAAY,gCAAgC,OAAOH,EAAG,kBAAkB,CAACM,MAAM,CAAC,KAAO,QAAQ,MAAQ,OAAO,wBAAwB,MAAMN,EAAG,kBAAkB,CAACM,MAAM,CAAC,KAAO,OAAO,MAAQ,KAAK,MAAQ,MAAM,MAAQ,YAAYN,EAAG,kBAAkB,CAACM,MAAM,CAAC,KAAO,OAAO,MAAQ,KAAK,MAAQ,MAAM,MAAQ,aAAa,IAAI,GAAGN,EAAG,OAAO,CAACG,YAAY,gBAAgBG,MAAM,CAAC,KAAO,UAAU4J,KAAK,UAAU,CAAClK,EAAG,YAAY,CAACqB,GAAG,CAAC,MAAQ,SAASC,GAAQxB,EAAI4N,yBAA0B,CAAK,IAAI,CAAC5N,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIqK,GAAG,qBAAqBnK,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,WAAWe,GAAG,CAAC,MAAQvB,EAAIiO,yBAAyB,CAACjO,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIqK,GAAG,uBAAuB,KAAKnK,EAAG,YAAY,CAACM,MAAM,CAAC,MAAQ,QAAQ,QAAUR,EAAIkO,sBAAsB,MAAQ,MAAM,iBAAiB,GAAG,wBAAuB,GAAO3M,GAAG,CAAC,iBAAiB,SAASC,GAAQxB,EAAIkO,sBAAsB1M,CAAM,IAAI,CAAExB,EAAIkL,eAAgBhL,EAAG,MAAM,CAACG,YAAY,yBAAyB,CAACH,EAAG,IAAI,CAACF,EAAIsC,GAAG,kBAAkBpC,EAAG,SAAS,CAACM,MAAM,CAAC,KAAO,UAAU,CAACR,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIkL,eAAemB,UAAUrM,EAAIsC,GAAG,SAAS,GAAGpC,EAAG,cAAc,CAACM,MAAM,CAAC,YAAYR,EAAImO,eAAe,UAAY,EAAE,SAAW,eAAe5M,GAAG,CAAC,QAAUvB,EAAIe,oBAAoB,aAAef,EAAIoO,uBAAuB,GAAGpO,EAAIgD,KAAK9C,EAAG,OAAO,CAACG,YAAY,gBAAgBG,MAAM,CAAC,KAAO,UAAU4J,KAAK,UAAU,CAAClK,EAAG,YAAY,CAACqB,GAAG,CAAC,MAAQ,SAASC,GAAQxB,EAAIkO,uBAAwB,CAAK,IAAI,CAAClO,EAAIsC,GAAG,SAAS,KAAKpC,EAAG,YAAY,CAACM,MAAM,CAAC,MAAQ,OAAO,QAAUR,EAAIqO,uBAAuB,MAAQ,QAAQ,iBAAiB,GAAG,wBAAuB,GAAO9M,GAAG,CAAC,iBAAiB,SAASC,GAAQxB,EAAIqO,uBAAuB7M,CAAM,EAAE,MAAQxB,EAAIsO,qBAAqB,CAACpO,EAAG,MAAM,CAACuC,WAAW,CAAC,CAAClC,KAAK,UAAUmC,QAAQ,YAAYC,MAAO3C,EAAIuO,gBAAiB3L,WAAW,oBAAoBvC,YAAY,yBAAyBG,MAAM,CAAC,uBAAuB,gBAAgB,CAACN,EAAG,QAAQ,CAACE,IAAI,eAAeyB,YAAY,CAAC,MAAQ,OAAO,OAAS,QAAQ,WAAa,QAAQrB,MAAM,CAAC,SAAW,GAAG,YAAc,MAAQR,EAAIuO,iBAAoBvO,EAAIwO,cAA8IxO,EAAIgD,KAAnI9C,EAAG,MAAM,CAACG,YAAY,uBAAuB,CAACH,EAAG,IAAI,CAACG,YAAY,+BAA+BH,EAAG,IAAI,CAACF,EAAIsC,GAAG,mBAA4BpC,EAAG,YAAY,CAACM,MAAM,CAAC,MAAQ,SAAS,QAAUR,EAAIyO,4BAA4B,MAAQ,MAAM,iBAAiB,GAAG,wBAAuB,GAAOlN,GAAG,CAAC,iBAAiB,SAASC,GAAQxB,EAAIyO,4BAA4BjN,CAAM,IAAI,CAAExB,EAAI0O,gBAAiBxO,EAAG,UAAU,CAACE,IAAI,eAAeI,MAAM,CAAC,MAAQR,EAAI0O,gBAAgB,MAAQ1O,EAAI2O,cAAc,cAAc,UAAU,CAACzO,EAAG,eAAe,CAACM,MAAM,CAAC,MAAQ,OAAO,KAAO,SAAS,CAACN,EAAG,WAAW,CAACM,MAAM,CAAC,YAAc,WAAW+J,MAAM,CAAC5H,MAAO3C,EAAI0O,gBAAgBnO,KAAMkK,SAAS,SAAUC,GAAM1K,EAAIyN,KAAKzN,EAAI0O,gBAAiB,OAAQhE,EAAI,EAAE9H,WAAW,2BAA2B,GAAG1C,EAAG,eAAe,CAACM,MAAM,CAAC,MAAQ,OAAO,KAAO,SAAS,CAACN,EAAG,WAAW,CAACM,MAAM,CAAC,UAAW,GAAM+J,MAAM,CAAC5H,MAAO3C,EAAI0O,gBAAgBjL,KAAMgH,SAAS,SAAUC,GAAM1K,EAAIyN,KAAKzN,EAAI0O,gBAAiB,OAAQhE,EAAI,EAAE9H,WAAW,2BAA2B,GAAG1C,EAAG,eAAe,CAACM,MAAM,CAAC,MAAQ,OAAO,KAAO,SAAS,CAACN,EAAG,WAAW,CAACM,MAAM,CAAC,UAAW,GAAM+J,MAAM,CAAC5H,MAAO3C,EAAI0O,gBAAgBxC,KAAMzB,SAAS,SAAUC,GAAM1K,EAAIyN,KAAKzN,EAAI0O,gBAAiB,OAAQhE,EAAI,EAAE9H,WAAW,2BAA2B,IAAI,GAAG5C,EAAIgD,KAAK9C,EAAG,OAAO,CAACG,YAAY,gBAAgBG,MAAM,CAAC,KAAO,UAAU4J,KAAK,UAAU,CAAClK,EAAG,YAAY,CAACqB,GAAG,CAAC,MAAQ,SAASC,GAAQxB,EAAIyO,6BAA8B,CAAK,IAAI,CAACzO,EAAIsC,GAAG,QAAQpC,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,WAAWe,GAAG,CAAC,MAAQvB,EAAI4O,eAAe,CAAC5O,EAAIsC,GAAG,SAAS,IAAI,GAAGpC,EAAG,YAAY,CAACM,MAAM,CAAC,MAAQR,EAAI6O,YAAc7O,EAAIqK,GAAG,0CAA4CrK,EAAIqK,GAAG,yCAAyC,QAAUrK,EAAI8O,qBAAqB,MAAQ,MAAM,wBAAuB,GAAOvN,GAAG,CAAC,iBAAiB,SAASC,GAAQxB,EAAI8O,qBAAqBtN,CAAM,EAAE,MAAQxB,EAAI+O,mBAAmB,CAAC7O,EAAG,UAAU,CAACE,IAAI,YAAYI,MAAM,CAAC,MAAQR,EAAIgP,UAAU,MAAQhP,EAAIiP,WAAW,cAAc,QAAQ,iBAAiB,SAAS,CAAC/O,EAAG,eAAe,CAACM,MAAM,CAAC,MAAQR,EAAIqK,GAAG,yCAAyC,KAAO,SAAS,CAACnK,EAAG,WAAW,CAACM,MAAM,CAAC,YAAcR,EAAIqK,GAAG,qDAAqDE,MAAM,CAAC5H,MAAO3C,EAAIgP,UAAUzO,KAAMkK,SAAS,SAAUC,GAAM1K,EAAIyN,KAAKzN,EAAIgP,UAAW,OAAQtE,EAAI,EAAE9H,WAAW,qBAAqB,GAAG1C,EAAG,eAAe,CAACM,MAAM,CAAC,MAAQR,EAAIqK,GAAG,gDAAgD,KAAO,gBAAgB,CAACnK,EAAG,WAAW,CAACM,MAAM,CAAC,KAAO,WAAW,KAAO,EAAE,YAAcR,EAAIqK,GAAG,4DAA4DE,MAAM,CAAC5H,MAAO3C,EAAIgP,UAAUE,YAAazE,SAAS,SAAUC,GAAM1K,EAAIyN,KAAKzN,EAAIgP,UAAW,cAAetE,EAAI,EAAE9H,WAAW,4BAA4B,IAAI,GAAG1C,EAAG,OAAO,CAACG,YAAY,gBAAgBG,MAAM,CAAC,KAAO,UAAU4J,KAAK,UAAU,CAAClK,EAAG,YAAY,CAACqB,GAAG,CAAC,MAAQ,SAASC,GAAQxB,EAAI8O,sBAAuB,CAAK,IAAI,CAAC9O,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIqK,GAAG,qBAAqBnK,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,WAAWe,GAAG,CAAC,MAAQvB,EAAImP,YAAY,CAACnP,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIqK,GAAG,uBAAuB,IAAI,IAAI,EACh3jB,EACIjH,EAAkB,G,oBCQf,SAASgM,EAAaC,GAC3B,OAAOC,EAAAA,EAAAA,IAAQ,CACb3N,IAAK,+BACL4N,OAAQ,MACRF,UAEJ,CAUO,SAASG,EAAM3L,GACpB,OAAOyL,EAAAA,EAAAA,IAAQ,CACb3N,IAAK,iCACL4N,OAAQ,MACR1L,QAEJ,CAEO,SAAS4L,EAAK7F,GACnB,OAAO0F,EAAAA,EAAAA,IAAQ,CACb3N,IAAK,2BAA6BiI,EAClC2F,OAAQ,UAEZ,CAEO,SAASG,EAAkBL,GAChC,OAAOC,EAAAA,EAAAA,IAAQ,CACb3N,IAAK,iCACL4N,OAAQ,MACRF,UAEJ,CAGO,SAASM,EAAU9L,GACxB,OAAOyL,EAAAA,EAAAA,IAAQ,CACb3N,IAAK,4BACL4N,OAAQ,OACR1L,QAEJ,CAEO,SAAS+L,EAAW/L,GACzB,OAAOyL,EAAAA,EAAAA,IAAQ,CACb3N,IAAK,6BACL4N,OAAQ,MACR1L,QAEJ,CAEO,SAASgM,EAAajG,GAC3B,OAAO0F,EAAAA,EAAAA,IAAQ,CACb3N,IAAK,gCAAgCiI,IACrC2F,OAAQ,UAEZ,CAUO,SAASO,EAAkBjM,GAChC,OAAOyL,EAAAA,EAAAA,IAAQ,CACb3N,IAAK,oCACL4N,OAAQ,OACR1L,OACAnD,QAAS,CACP,eAAgB,wBAGtB,C,wBC2QA,GACAH,KAAA,kBACAwP,WAAA,CAAAC,WAAAA,EAAAA,GACA3M,MAAA,2BACA4M,MAAA,CACAzF,UAAAA,CAAA1F,GACA,KAAAsD,MAAA8H,KAAAC,OAAArL,EACA,EACA,iBACAsL,QAAA,oBACAC,MAAA,IAGAxM,IAAAA,GACA,OACA8G,cAAA,GACAC,aAAA,CACAiC,SAAA,WACAR,MAAA,QACAlB,SAAA,WACAI,YAAA,eAEAf,WAAA,GACAU,eAAA,KACAf,UAAA,IACAmG,YAAA,EACA/C,WAAA,CACAhC,YAAA,GACAD,WAAA,GACAE,WAAA,IAEAkC,cAAA,GACAF,MAAA,CACAlC,WAAA,CACA,CAAAiF,UAAA,EAAAC,QAAA,KAAAnG,GAAA,wCAAAoG,QAAA,UAGApD,QAAA,EACAqD,WAAA,EACAjE,kBAAA,GAGAmB,yBAAA,EACAE,UAAA,GACAD,mBAAA,EACAG,mBAAA,KAGAE,uBAAA,EACAC,eAAA,GACAwC,WAAA,EAGAtC,wBAAA,EACAuC,kBAAA,KACApC,cAAA,KACAD,iBAAA,EAGAE,6BAAA,EACAC,gBAAA,KACAC,cAAA,CACApO,KAAA,CACA,CAAAgQ,UAAA,EAAAC,QAAA,UAAAC,QAAA,UAKA3B,sBAAA,EACAD,aAAA,EACAG,UAAA,CACApF,GAAA,KACArJ,KAAA,GACA2O,YAAA,IAEAD,WAAA,CACA1O,KAAA,CACA,CAAAgQ,UAAA,EAAAC,QAAA,KAAAnG,GAAA,oDAAAoG,QAAA,UAIA,EACAI,OAAAA,GACA,KAAAC,SACA,EACAlM,QAAA,CACAkM,OAAAA,GACA,KAAAJ,WAAA,EACAtB,IAAA2B,MAAAxI,IACA,GAAAA,EAAAyI,OACA,KAAArG,cAAApC,EAAA1E,KACA,KAAAoN,oBACA,IACAC,SAAA,KACA,KAAAR,WAAA,IAEA,EACAO,iBAAAA,GACA,KAAAE,WAAA,KACA,SAAA/I,MAAA8H,OAAA,KAAAvF,eAAA,SAAAA,cAAAxH,OAAA,OAEA,gBAAAiO,EAAA,WAAAC,GAAA,KAAAC,OAAAjC,OAQA,GALA,KAAAnE,iBACA,KAAA9C,MAAA8H,KAAAqB,cAAA,MACA,KAAArG,eAAA,MAGAmG,EAAA,CACA,MAAAG,EAAA,KAAAC,cAAA,KAAA9G,cAAA0G,GACA,GAAAG,EAAA,CACA,KAAAtG,eAAAsG,EACA,KAAApJ,MAAA8H,KAAAqB,cAAAC,EAAA5H,IAGA,MAAArE,EAAA,KAAAmM,SAAA,KAAA/G,eAAAgH,GAAAA,EAAA9E,UAAA8E,EAAA9E,SAAA+E,MAAA/L,GAAAA,EAAA+D,KAAA4H,EAAA5H,OACArE,GAAA,KAAA6C,MAAA8H,KAAAlM,MAAA6N,SAAAtM,EAAAqE,KACA,KAAAxB,MAAA8H,KAAAlM,MAAA6N,SAAAtM,EAAAqE,IAAAkI,QAEA,CACA,SAAAV,EAAA,CACA,MAAAW,EAAA,KAAAC,gBAAA,KAAArH,cAAAyG,GACAW,IACA,KAAA7G,eAAA6G,EACA,KAAA3J,MAAA8H,KAAAqB,cAAAQ,EAAAnI,IACA,KAAAxB,MAAA8H,KAAAlM,MAAA6N,SAAAE,EAAAnI,KACA,KAAAxB,MAAA8H,KAAAlM,MAAA6N,SAAAE,EAAAnI,IAAAkI,SAGA,IAEA,EACAG,YAAAA,CAAA/B,EAAAtG,GACA,UAAAsI,KAAAhC,EAAA,CACA,GAAAgC,EAAAtI,IAAAA,EACA,OAAAsI,EAEA,GAAAA,EAAArF,SAAA,CACA,MAAAsF,EAAA,KAAAF,aAAAC,EAAArF,SAAAjD,GACA,GAAAuI,EACA,OAAAA,CAEA,CACA,CACA,WACA,EACAT,QAAAA,CAAAxB,EAAAkC,GACA,UAAAF,KAAAhC,EAAA,CACA,GAAAkC,EAAAF,GACA,OAAAA,EAEA,GAAAA,EAAArF,SAAA,CACA,MAAAsF,EAAA,KAAAT,SAAAQ,EAAArF,SAAAuF,GACA,GAAAD,EACA,OAAAA,CAEA,CACA,CACA,WACA,EACAH,eAAAA,CAAA9B,EAAA7D,GACA,YAAAqF,SAAAxB,GAAAgC,IAAAA,EAAA/G,UAAA+G,EAAA7F,QAAAA,GACA,EACAoF,aAAAA,CAAAvB,EAAAmC,GACA,YAAAX,SAAAxB,GAAAgC,GAAAA,EAAA/G,UAAA+G,EAAA3G,cAAA8G,GACA,EACAtH,eAAAA,CAAAlH,EAAAqO,GACA,IAAAd,EAAA,KACA,GAAAvN,EAAAsH,SAAA,CACA,IAAAmH,EAAAJ,EAAAI,OAEA,MAAAA,GAAAA,EAAAzO,MAAAyO,EAAAzO,KAAAsH,SACAmH,EAAAA,EAAAA,OAEAA,GAAAA,EAAAzO,OACAuN,EAAAkB,EAAAzO,KAAAwI,MAEA,MACA+E,EAAAvN,EAAAwI,MAGA,MAAAgF,EAAAxN,EAAAsH,SAAAtH,EAAA0H,iBAAAgH,EAEAC,EAAA,GACApB,IACAoB,EAAApB,UAAAA,GAEAC,IACAmB,EAAAnB,WAAAA,GAGA,KAAAC,OAAAjC,OAAA+B,YAAAoB,EAAApB,WAAA,KAAAE,OAAAjC,OAAAgC,aAAAmB,EAAAnB,YACA,KAAAoB,QAAArM,KAAA,CAAA7F,KAAA,kBAAA8O,OAAAmD,IAGA,KAAAtH,eAAArH,EAGA,KAAAqH,eAAAC,WAAA,KAAAD,eAAAc,WACA,KAAAd,eAAA,IACA,KAAAA,eACAc,SAAA,CACApC,GAAA,UACArJ,KAAA,aACA0L,UAAA,6FACAxI,KAAA,KACAyI,KAAA,YAKArI,EAAAsH,SAGA,KAAA/C,MAAA8H,KAAAqB,cAAA1N,EAAA+F,IAFAsI,EAAAQ,SAAA,KAAAtK,MAAA8H,KAAAlM,OAAA6N,SAAAhO,EAAA+F,KAAA+I,WAAA,KAAAvK,MAAA8H,KAAAlM,OAAA6N,SAAAhO,EAAA+F,KAAAkI,QAIA,EAEAjH,UAAAA,CAAAlI,EAAAkB,GACA,IAAAlB,EAAA,SACA,MAAAiQ,EAAAjQ,EAAA2G,cACA,OAAAzF,EAAAwI,QAAA,IAAAxI,EAAAwI,MAAA/C,cAAAuJ,QAAAD,IACA/O,EAAAyH,aAAA,IAAAzH,EAAAyH,WAAAhC,cAAAuJ,QAAAD,IACA/O,EAAA0H,cAAA,IAAA1H,EAAA0H,YAAAjC,cAAAuJ,QAAAD,EACA,EAEA9H,aAAAA,CAAAgI,GAAA,KAAAZ,EAAA,KAAArO,IACA,MAAAsH,EAAAtH,EAAAsH,SACA,OAAA2H,EACA,OACA,CAAAxS,MAAA,oBACA,CACAwS,EACA,OACA,CAAAxS,MAAA,gBACA,CACAwS,EAAA,KACAxS,MAAA,CACA6K,EAAA,kBAAA+G,EAAAQ,SAAA,yCACA,YACA,eAAAvH,GAAAtH,EAAA4H,SAAA,eAAAN,IAAAtH,EAAA4H,aAGAqH,EAAA,QAAAxS,MAAA,sBAAA4R,EAAA7F,MAAA,KAAAhC,GAAA,6BAAA6H,EAAA7F,OACAlB,EACA2H,EACA,SACA,CACAzP,MAAA,CACA6I,KAAA,OACAzI,KAAAI,EAAA4H,SAAA,oBAEAnL,MAAA,cAEAuD,EAAA4H,SAAA,KAAApB,GAAA,oCAAAA,GAAA,iCAEA,OAGAc,EAgBA,KAfA2H,EACA,OACA,CAAAxS,MAAA,cACA,CACAwS,EACA,OACA,CAAAxS,MAAA,gBACA,CACAwS,EAAA,QAAAxS,MAAA,gBAAAuD,EAAA+I,QAAA,GACA,MACAkG,EAAA,QAAAxS,MAAA,eAAAuD,EAAA8I,OAAA,QASA,EAGAd,cAAAA,CAAAkH,GACA,KAAA5E,eAAA,GACA,KAAAD,uBAAA,CACA,EAEAnN,mBAAAA,CAAAiS,GAIA1L,QAAAC,IAAA,QAAAyL,EAAAzS,WAEA,MAAA0S,EAAA,CACArJ,GAAA,YAAAsJ,MAAAC,YACA5S,KAAAyS,EAAAzS,KACA0L,UAAA+G,EAAArR,IAAAyR,WAAA,QAAAJ,EAAArR,IAAA,kCACAA,IAAAqR,EAAArR,IACA8B,KAAA,KAAA4P,YAAAL,EAAAzS,MACA2L,KAAA,OAIA5E,QAAAC,IAAA,YAAA2D,eAAAtB,cAAAqJ,EAAArJ,SACA,KAAAvD,SAAAiN,QAAA,iBAGA,KAAApI,eAAAc,SAAAiH,EAGA,KAAA/E,uBAAA,EACA,KAAAqF,gBACA,EAEAF,WAAAA,CAAAG,GACA,MAAA9J,EAAA8J,EAAAzL,MAAA,KAAA4B,MAAAL,cACA,iCAAA3C,SAAA+C,GAAA,KACA,oBAAA/C,SAAA+C,GAAA,KACA,IACA,EAEA0E,kBAAAA,CAAAqF,GACA,KAAA9C,UAAA8C,CACA,EAEA3H,eAAAA,CAAAiH,GACA,KAAA/E,mBAAA+E,EAAA/G,SAAA+G,EAAA/G,SAAApC,GAAA,KACA,KAAAgE,yBAAA,EACA,KAAA2F,gBACA,EAEAA,cAAAA,GACA,KAAA1F,mBAAA,GAEA6F,EAAAA,EAAAA,IAAA,CAAAC,KAAA,EAAAC,MAAA,MAAA7C,MAAAxI,IACA,IAAAA,EAAAyI,KAEA,KAAAlD,WAAAvF,EAAA1E,KAAAS,MAAAiE,EAAA1E,MAAAmE,KAAA5G,IAAA,IACAA,EACAwI,GAAAxI,EAAAwI,GACAyC,MAAAjL,EAAAiL,OAAAjL,EAAAb,KACA0L,UAAA7K,EAAA6K,WAAA,sCAGA,KAAA5F,SAAAwN,MAAA,WACA,IACAC,OAAA,KACA,KAAAzN,SAAAwN,MAAA,eACA3C,SAAA,KACA,KAAArD,mBAAA,IAEA,EAEAI,sBAAAA,GACA,SAAAD,mBAEA,YADA,KAAA3H,SAAAC,QAAA,WAIA,MAAAyN,EAAA,KAAAjG,UAAAkG,MAAAC,GAAAA,EAAArK,KAAA,KAAAoE,qBACA1G,QAAAC,IAAA,YAAA2D,eAAAtB,UAAA,KAAAoE,yBACA,KAAA3H,SAAAiN,QAAA,iBAAAS,EAAA1H,SAGA,KAAAnB,eAAAc,WAAA,KAAAd,eAAAc,SAAA,IACA,KAAAd,eAAAc,SAAAzL,KAAAwT,EAAA1H,MACA,KAAAnB,eAAAc,SAAApC,GAAAmK,EAAAnK,GACA,KAAAsB,eAAAc,SAAAC,UAAA8H,EAAA9H,UACA,KAAAf,eAAAc,SAAAvI,KAAAsQ,EAAAtQ,KACA,KAAAyH,eAAAc,SAAAE,KAAA6H,EAAA7H,KAEA,KAAA0B,yBAAA,CACA,EAEA7B,aAAAA,CAAAgH,GACA,KAAA1E,wBAAA,EACA,KAAAE,iBAAA,EACA,KAAA4C,WAAA,KACA,KAAA+C,WAAAnB,EAAA,GAEA,EAEAmB,UAAAA,CAAAnB,GACAzL,QAAAC,IAAA,mBAAAwL,EAAA1G,mBAEA,MAAA8H,EAAA,CAAAC,WAAA,EAAAC,KAAA,kCACA,KAAAzD,kBAAA,IAAA0D,kBAAAH,GAEA,KAAAvD,kBAAA2D,eAAAC,IACAA,EAAAC,WACAnN,QAAAC,IAAA,wBAAAiN,EAAAC,UAEA,EAGA,KAAA7D,kBAAA8D,QAAAF,IACAlN,QAAAC,IAAA,qBACA,KAAAgH,iBAAA,EACA,KAAAnG,MAAAY,eACA,KAAAZ,MAAAY,aAAA2L,UAAAH,EAAAI,QAAA,GACA,KAAApG,cAAAgG,EAAAI,QAAA,GACA,EAIA,KAAAC,0BACA,EAEAA,wBAAAA,GACAvN,QAAAC,IAAA,4BACA,KAAAqJ,kBAAAkE,YAAA,CACAC,qBAAA,IACAhE,MAAAiE,IACA1N,QAAAC,IAAA,0BAAAyN,GACA,KAAApE,kBAAAqE,oBAAAD,MACAjE,MAAA,KACAzJ,QAAAC,IAAA,qDAIAvC,YAAA,KACA,MAAAkQ,EAAA,CAAAzR,KAAA,SAAA0R,IAAA,OACA7N,QAAAC,IAAA,4BAAA2N,GAEA5N,QAAA8N,KAAA,sEAGApQ,YAAA,KACA,KAAAuJ,kBACA,KAAAlI,SAAAwN,MAAA,gBACA,KAAAtF,iBAAA,EACA,GACA,OAEA,QACAuF,OAAAzO,IACAiC,QAAAuM,MAAA,uBAAAxO,GACA,KAAAgB,SAAAwN,MAAA,qBACA,KAAAtF,iBAAA,IAEA,EAEAD,kBAAAA,GACAhH,QAAAC,IAAA,2BACA,KAAAqJ,oBACA,KAAAA,kBAAAyE,QACA,KAAAzE,kBAAA,MAEA,KAAApC,gBACA,KAAAA,cAAA8G,YAAA3M,SAAA4M,GAAAA,EAAAC,SACA,KAAAhH,cAAA,MAEA,KAAAH,wBAAA,EACA,KAAAE,iBAAA,CACA,EAEApC,YAAAA,CAAAH,GAEA,KAAA0C,gBAAA+G,KAAAC,MAAAD,KAAAE,UAAA3J,IACA,KAAAyC,6BAAA,CACA,EAEAG,YAAAA,GACA,KAAAxG,MAAAwN,aAAAC,UAAAC,IACAA,IAEA3J,EAAAA,EAAAA,IAAA,KAAAuC,gBAAA,KAAAA,gBAAA9E,IAAAmH,MAAA,KACA,KAAA1K,SAAAiN,QAAA,YAEA,KAAApI,eAAAc,SAAA,SAAA0C,iBACA,KAAAD,6BAAA,EAEA,KAAA8E,gBAAA,IACAO,OAAA,KACA,KAAAzN,SAAAwN,MAAA,cAEA,GAEA,EAEAzH,cAAAA,CAAA2G,GACA,KAAAgD,SAAA,YAAAhD,EAAA1G,mBAAA,MACA2J,kBAAA,KACAC,iBAAA,KACAxS,KAAA,YACAsN,MAAA,KAEAzJ,QAAAC,IAAA,aAAAwL,EAAAnJ,YACA,KAAAvD,SAAAiN,QAAA,UAEA,KAAApI,eAAAc,SAAA,QACA8H,OAAA,QACA,EAGAxJ,cAAAA,GACA,KAAAuE,aAAA,EACA,KAAAG,UAAA,CACApF,GAAA,KACArJ,KAAA,GACA2O,YAAA,IAEA,KAAAJ,sBAAA,CACA,EAEAxC,eAAAA,CAAA4J,GACA,KAAArH,aAAA,EACA,KAAAG,UAAA,CACApF,GAAAsM,EAAAtM,GACArJ,KAAA2V,EAAA7J,MACA6C,YAAAgH,EAAAhH,aAAA,IAEA,KAAAJ,sBAAA,CACA,EAEAK,SAAAA,GACA,KAAA/G,MAAA4G,UAAA6G,UAAAC,IACA,GAAAA,EAAA,CACA,MAAAK,EAAA,KAAAtH,YAAAe,EAAAD,EACAN,EAAA,CACA9O,KAAA,KAAAyO,UAAAzO,KACA2O,YAAA,KAAAF,UAAAE,aAGA,KAAAL,cACAQ,EAAAzF,GAAA,KAAAoF,UAAApF,IAGAuM,EAAA9G,GAAA0B,MAAA,KACA,KAAA1K,SAAAiN,QACA,KAAAzE,YACA,KAAAxE,GAAA,mDACA,KAAAA,GAAA,mDAEA,KAAAyE,sBAAA,EACA,KAAAgC,SAAA,IACAgD,OAAA,KACA,KAAAzN,SAAAwN,MAAA,gBAEA,IAEA,EAEAtH,iBAAAA,CAAA2J,GACA,GAAAA,EAAAE,SAIAF,EAAArJ,UAAA1J,OAAA,EACA,KAAAkD,SAAAwN,MAAA,gBAIA,KAAAkC,SACA,KAAA1L,GAAA,qDACA,KAAAA,GAAA,qDACA,CACA2L,kBAAA,KAAA3L,GAAA,kBACA4L,iBAAA,KAAA5L,GAAA,iBACA5G,KAAA,YAEAsN,MAAA,KACAlB,EAAAqG,EAAAE,UAAArF,MAAA,KACA,KAAA1K,SAAAiN,QAAA,KAAAjJ,GAAA,sDACA,KAAAa,eAAA,KACA,KAAA4F,SAAA,IACAgD,OAAA,KACA,KAAAzN,SAAAwN,MAAA,gBACA,IACAC,OAAA,SAxBA,KAAAzN,SAAAwN,MAAA,KAAAxJ,GAAA,qDA2BA,EAEA0E,gBAAAA,GACA,KAAA3G,MAAA4G,UAAAqH,cACA,KAAArH,UAAA,CACApF,GAAA,KACArJ,KAAA,GACA2O,YAAA,GAEA,EAGA9D,UAAAA,CAAA2H,GACAA,GAAAA,EAAA5H,UAIA,KAAAoC,WAAA,IAAAwF,GACA,KAAA1F,QAAA,EACA,KAAAiJ,sBALA,KAAAjQ,SAAAC,QAAA,QAMA,EAEAgQ,kBAAAA,GACA5G,IAAAqB,MAAAxI,IACA,GAAAA,EAAAyI,OACA,KAAAtD,cAAAnF,EAAA1E,KACA,GAEA,EAEAwH,YAAAA,CAAA0H,GACAA,GAAAA,EAAA5H,SAIA,KAAA4K,SAAA,WAAAhD,EAAA1G,SAAA,MACA2J,kBAAA,KACAC,iBAAA,KACAxS,KAAA,YACAsN,MAAA,KACAtB,EAAAsD,EAAAnJ,IAAAmH,MAAA,KACA,KAAA1K,SAAA,CAAA5C,KAAA,UAAA+M,QAAA,SACA,KAAAtF,gBAAA,KAAAA,eAAAtB,KAAAmJ,EAAAnJ,KACA,KAAAsB,eAAA,MAEA,KAAA4F,SAAA,IACAgD,OAAA,KACA,KAAAzN,SAAA,CAAA5C,KAAA,QAAA+M,QAAA,WACA,IACAsD,OAAA,SAjBA,KAAAzN,SAAAC,QAAA,QAoBA,EAEAqH,UAAAA,GACA,KAAAvF,MAAAmO,eAAAV,UAAAC,IACAA,GACAtG,EAAA,KAAAjC,YAAAwD,MAAA,KACA,KAAA1K,SAAA,CAAA5C,KAAA,UAAA+M,QAAA,SACA,KAAAnD,QAAA,EACA,KAAAyD,SAAA,IACAgD,OAAA,KACA,KAAAzN,SAAA,CAAA5C,KAAA,QAAA+M,QAAA,WAEA,GAEA,EAEAlD,WAAAA,GACA,KAAAlF,MAAAmO,eAAAF,cACA,KAAA9I,WAAA,CAAAhC,YAAA,GAAAD,WAAA,GAAAE,WAAA,GACA,EAEAsB,qBAAAA,CAAAhI,GACA,KAAA2H,kBAAA3H,CACA,EAEA4H,SAAAA,GACA,YAAAD,kBAAAtJ,OAEA,YADA,KAAAkD,SAAAC,QAAA,aAGA,MAAAkQ,EAAA,KAAA/J,kBAAAzE,KAAA5G,GAAAA,EAAAiL,QAAAoK,KAAA,MACA,KAAApQ,SAAAiN,QAAA,kBAAAkD,KACAlP,QAAAC,IAAA,gBAAAkF,kBAEA,EAEAd,UAAAA,CAAA+K,GACA,OAAAA,EACA,KAAAC,iBAAA,KAAAA,iBAAAD,GAAA,IAAAxD,KAAAwD,GAAAE,iBADA,GAEA,EAEA5L,gBAAAA,CAAAnH,EAAAqO,GACA,MAAA2E,EAAA,KAAAzO,MAAA8H,KAAAlM,MAAA6N,SACAiF,EAAA,GACA,UAAAxV,KAAAuV,EACAA,EAAAvV,GAAAoR,UACAoE,EAAA1Q,KAAAyQ,EAAAvV,GAAAuC,KAAAwI,OAKA,KAAAiF,OAAAjC,OAAA+B,YAAAvN,EAAAwI,OACA,KAAAoG,QAAArM,KAAA,CAAA7F,KAAA,kBAAA8O,OAAA,CAAA+B,UAAAvN,EAAAwI,QAEA,EAEApB,WAAAA,CAAA5F,GACA,KAAAiL,YAAA,EACA,MAAAyG,EAAA1R,EAAA2R,QACAC,EAAA,KAAA9M,UACA+M,EAAAC,IACA,SAAA7G,WAAA,OACA,MAAA8G,EAAAD,EAAAH,QAAAD,EACAM,EAAAJ,EAAAG,EACAC,GAAA,KAAAA,GAAA,MACA,KAAAlN,UAAAkN,EACA,EAEAC,EAAAA,KACA,KAAAhH,YAAA,EACApL,SAAAqS,oBAAA,YAAAL,GACAhS,SAAAqS,oBAAA,UAAAD,GACApS,SAAAsS,KAAAtN,MAAAuN,OAAA,GACAvS,SAAAsS,KAAAtN,MAAAwN,WAAA,IAEAxS,SAAAE,iBAAA,YAAA8R,GACAhS,SAAAE,iBAAA,UAAAkS,GACApS,SAAAsS,KAAAtN,MAAAuN,OAAA,aACAvS,SAAAsS,KAAAtN,MAAAwN,WAAA,MACA,EAEAlL,iBAAAA,GACA,SAAAtB,gBAAA,KAAAA,eAAAC,SAEA,YADA,KAAA9E,SAAAC,QAAA,cAGA,MAAAqR,EAAAzS,SAAAgC,cAAA,SACAyQ,EAAAlU,KAAA,OACAkU,EAAAC,iBAAA,EACAD,EAAAE,SAAAxS,IACA,MAAAyS,EAAAzS,EAAAC,OAAAwS,MACA,IAAAA,EAAA3U,QAGA,KAAA4U,aAAAD,EAAA,EAEAH,EAAAlS,OACA,EAEA,kBAAAsS,CAAAD,GACA,MAAAE,EAAA,IAAAC,SACAD,EAAAE,OAAA,eAAAhN,eAAAkL,UAEA,QAAA+B,EAAA,EAAAA,EAAAL,EAAA3U,OAAAgV,IACAH,EAAAE,OAAA,QAAAJ,EAAAK,IAGA,KAAAzH,WAAA,EACA,IACA,MAAAnI,QAAAuH,EAAAkI,GACA,IAAAzP,EAAAyI,MACA,KAAA3K,SAAAiN,QAAA,oBACA,KAAAxC,WAEA,KAAAzK,SAAAwN,MAAAtL,EAAA6P,KAAA,OAEA,OAAAvE,GACA,KAAAxN,SAAAwN,MAAA,YACA,SACA,KAAAnD,WAAA,CACA,CACA,IC/kC6P,I,UCSzPzG,GAAY,OACd,EACAlK,EACAqD,GACA,EACA,KACA,WACA,MAIF,EAAe6G,EAAiB,O,8IClBzB,SAAS6G,EAAQzB,GACtB,OAAOC,EAAAA,EAAAA,IAAQ,CACb3N,IAAK,gCACL4N,OAAQ,MACRF,UAEJ,CAEO,SAASgJ,EAAIxU,GAClB,OAAOyL,EAAAA,EAAAA,IAAQ,CACb3N,IAAK,0CACL4N,OAAQ,OACR1L,QAEJ,CAEO,SAAS2L,EAAK3L,EAAK+F,GACxB,OAAO0F,EAAAA,EAAAA,IAAQ,CACb3N,IAAK,4CAA8CiI,EACnD2F,OAAQ,MACR1L,QAEJ,CAEO,SAAS4L,EAAI7F,GAClB,OAAO0F,EAAAA,EAAAA,IAAQ,CACb3N,IAAK,gCAAkCiI,EACvC2F,OAAQ,UAEZ,C", "sources": ["webpack://esop-dashboard/./src/components/file-upload.vue", "webpack://esop-dashboard/src/components/file-upload.vue", "webpack://esop-dashboard/./src/components/file-upload.vue?6b69", "webpack://esop-dashboard/./src/components/file-upload.vue?49cc", "webpack://esop-dashboard/./src/views/EquipmentCenter/index.vue", "webpack://esop-dashboard/./src/api/equipmentCenter.js", "webpack://esop-dashboard/src/views/EquipmentCenter/index.vue", "webpack://esop-dashboard/./src/views/EquipmentCenter/index.vue?b408", "webpack://esop-dashboard/./src/views/EquipmentCenter/index.vue?07f6", "webpack://esop-dashboard/./src/api/material.js"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('el-upload',{ref:\"uploadRef\",staticClass:\"avatar-uploader2\",class:_vm.name,attrs:{\"action\":_vm.upload_host,\"name\":\"uploadFile\",\"headers\":_vm.headers,\"multiple\":_vm.uploadNum > 1 ? true : false,\"show-file-list\":false,\"before-upload\":_vm.beforeUpload,\"on-remove\":_vm.handleRemove,\"on-progress\":_vm.handleUploadProgress,\"on-success\":_vm.handleUploadSuccess,\"disabled\":!_vm.isDisabled,\"accept\":_vm.acceptFileType}}),_c('div',{staticClass:\"images\"},[_vm._l((_vm.fileList),function(item,index){return _c('div',{key:index,staticClass:\"item\"},[_c('div',{staticClass:\"img\",attrs:{\"id\":\"preview-item\"},on:{\"mouseover\":function($event){item.hover = true},\"mouseout\":function($event){item.hover = false}}},[(_vm.isImageUrl(item.url))?_c('el-image',{ref:\"img\",refInFor:true,staticStyle:{\"width\":\"120px\",\"height\":\"120px\"},attrs:{\"src\":_vm.imageUrl + item.url,\"preview-src-list\":_vm.previewImages(),\"fit\":\"cover\"},on:{\"click\":function($event){$event.stopPropagation();return _vm.handleClickItem.apply(null, arguments)}}}):(_vm.isVideoUrl(item.url))?_c('video',{staticStyle:{\"width\":\"120px\",\"height\":\"120px\",\"display\":\"block\"},attrs:{\"controls\":\"\"}},[_c('source',{attrs:{\"src\":_vm.imageUrl + item.url,\"type\":\"video/avi\"}})]):_c('div',{staticClass:\"file-preview\"},[_c('i',{class:_vm.getFileIconClass(item.url)}),_c('div',{staticClass:\"file-name\"},[_vm._v(_vm._s(item.name || _vm.getFileNameFromUrl(item.url)))])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(item.hover),expression:\"item.hover\"}],staticClass:\"mask\"},[(_vm.canPreview(item.url))?_c('i',{staticClass:\"el-icon-zoom-in\",on:{\"click\":function($event){return _vm.previewFile(item)}}}):_c('i',{staticClass:\"el-icon-download\",on:{\"click\":function($event){return _vm.downloadFile(item.url)}}}),(!_vm.isDisabled)?_c('i',{staticClass:\"el-icon-upload2\",on:{\"click\":function($event){return _vm.uploadImage(item.url)}}}):_vm._e(),(!_vm.isDisabled)?_c('i',{staticClass:\"el-icon-delete\",on:{\"click\":function($event){return _vm.deleteImage(item.url)}}}):_vm._e()])],1)])}),(_vm.fileList.length < _vm.uploadNum)?_c('div',{staticClass:\"add\",on:{\"click\":function($event){return _vm.uploadImage()}}},[_c('i',{staticClass:\"el-icon-plus\"})]):_vm._e()],2)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div>\n    <el-upload ref=\"uploadRef\" :action=\"upload_host\" name=\"uploadFile\" :headers=\"headers\"\n      :multiple=\"uploadNum > 1 ? true : false\" :show-file-list=\"false\" :before-upload=\"beforeUpload\"\n      :on-remove=\"handleRemove\" :on-progress=\"handleUploadProgress\" :on-success=\"handleUploadSuccess\"\n      :disabled=\"!isDisabled\" :class=\"name\" :accept=\"acceptFileType\" class=\"avatar-uploader2\">\n    </el-upload>\n    <div class=\"images\">\n      <div class=\"item\" v-for=\"(item, index) in fileList\" :key=\"index\">\n        <div class=\"img\" @mouseover=\"item.hover = true\" @mouseout=\"item.hover = false\" id=\"preview-item\">\n          <el-image @click.stop=\"handleClickItem\" ref=\"img\" v-if=\"isImageUrl(item.url)\" :src=\"imageUrl + item.url\"\n            :preview-src-list=\"previewImages()\" style=\"width: 120px; height: 120px\" fit=\"cover\"></el-image>\n          <video v-else-if=\"isVideoUrl(item.url)\" controls style=\"width: 120px; height: 120px; display: block\">\n            <source :src=\"imageUrl + item.url\" type=\"video/avi\">\n          </video>\n          <div v-else class=\"file-preview\">\n            <i :class=\"getFileIconClass(item.url)\"></i>\n            <div class=\"file-name\">{{ item.name || getFileNameFromUrl(item.url) }}</div>\n          </div>\n          <div class=\"mask\" v-show=\"item.hover\">\n            <i class=\"el-icon-zoom-in\" @click=\"previewFile(item)\" v-if=\"canPreview(item.url)\"></i>\n            <i class=\"el-icon-download\" @click=\"downloadFile(item.url)\" v-else></i>\n            <i class=\"el-icon-upload2\" @click=\"uploadImage(item.url)\" v-if=\"!isDisabled\"></i>\n            <i class=\"el-icon-delete\" @click=\"deleteImage(item.url)\" v-if=\"!isDisabled\"></i>\n          </div>\n        </div>\n      </div>\n      <div class=\"add\" v-if=\"fileList.length < uploadNum\" @click=\"uploadImage()\">\n        <i class=\"el-icon-plus\"></i>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport request from \"@/utils/request\";\nimport { findElem } from \"@/utils/util.js\";\nimport store from \"@/store\";\n\nexport default {\n  props: {\n    isDisabled: Boolean,\n    fileType: String,\n    name: String,\n    acceptFileType: {\n      type: String,\n      default: \"\",\n    },\n    fileList: Array,\n    uploadNum: {\n      type: Number,\n      default: 1,\n    },\n    index: {\n      type: Number,\n      default: 0,\n    },\n  },\n  data () {\n    return {\n\n\n\n      upload_host: process.env.VUE_APP_BASE_API + `admin/sourcematerial/upload`,\n      imageUrl: process.env.VUE_APP_BASE_API + \"assets/media/\",\n      headers: {\n        Authorization: store.getters.token,\n      },\n\n      showImage: \"\",\n      dialogVisible: false,\n      uploadImageId: \"\",\n      list: [],\n    };\n  },\n  computed: {\n    showFileList: {\n      get: function () {\n        return this.fileList && this.fileList.length > 0;\n      },\n      set: function (newValue) { },\n    },\n  },\n  methods: {\n    emitInput (val) {\n      this.$emit(\"input\", val);\n    },\n    handleClickItem () {\n      setTimeout(() => {\n        let domImageMask = document.querySelector(\".el-image-viewer__wrapper\");\n        if (!domImageMask) return;\n\n        domImageMask.addEventListener(\"click\", (e) => {\n          if (e.target.parentNode.className == \"el-image-viewer__actions__inner\") {\n            return;\n          }\n          document.querySelector(\".el-image-viewer__close\").click();\n        });\n      }, 300);\n    },\n    handleRemove (file, fileList) {\n      if (fileList.length === 0) {\n        this.fileList = [];\n      } else {\n        this.fileList = fileList;\n      }\n    },\n    getUUID () {\n      return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, (c) => {\n        return (c === \"x\" ? (Math.random() * 16) | 0 : \"r&0x3\" | \"0x8\").toString(16);\n      });\n    },\n    beforeUpload (file) {\n\n      const isValidType = this.checkFileType(file);\n\n      if (!isValidType) {\n        this.handleInvalidType(file);\n        return false;\n      }\n\n      this.list.push(file.name);\n      if (this.list.length > this.uploadNum) {\n        this.$message.warning(this.$i18n.t(\"upload.maxFileCount\", { count: this.uploadNum }));\n        this.resetUploadState();\n        return false;\n      }\n      if (file.type.includes(\"image\")) {\n\n        let reader = new FileReader();\n        reader.onload = (e) => {\n          let txt = e.target.result\n          let img = document.createElement(\"img\")\n          img.src = txt\n          img.onload = () => {\n            this.$emit(\n              \"fileData\",\n              {\n                width: img.width,\n                height: img.height,\n\n              }\n            );\n\n            console.log(\"宽度：\", img.width);\n            console.log(\"高度：\", img.height);\n          }\n        };\n        reader.readAsDataURL(file);\n\n      }\n\n      else if (file.type.includes(\"video\")) {\n        console.log(file, \"ffff\");\n        const videoElement = document.createElement(\"video\");\n        videoElement.src = URL.createObjectURL(file);\n        videoElement.addEventListener(\"loadedmetadata\", () => {\n          this.$emit(\n            \"fileData\",\n            {\n              width: videoElement.videoWidth,\n              height: videoElement.videoHeight,\n\n            }\n          );\n          console.log('width, height:', videoElement.videoWidth\n            , videoElement.videoHeight)\n        });\n\n      }\n\n\n      return true;\n    },\n    checkFileType (file) {\n      console.log(this.fileType)\n      if (this.fileType === \"image\") {\n        return file.type.includes(\"image\");\n      } else if (this.fileType === \"video\") {\n        return file.type.includes(\"video\");\n      } else if (this.fileType === \"image/video\") {\n        return file.type.includes(\"image\") || file.type.includes(\"video\");\n      } else if (this.fileType === \"file\") {\n        // 使用 acceptFileType prop 来进行校验，使其更具通用性\n        const allowedTypes = this.acceptFileType.split(',').map(item => item.trim());\n        return allowedTypes.includes(file.type);\n      }\n      return true;\n    },\n    handleInvalidType (file) {\n      const warningMsg = this.getWarningMessage();\n      this.$message.warning(warningMsg);\n      this.resetUploadState();\n    },\n    getWarningMessage () {\n      switch (this.fileType) {\n        case \"image\":\n          return this.$i18n.t(\"upload.onlyImage\");\n        case \"video\":\n          return this.$i18n.t(\"upload.onlyVideo\");\n        case \"image/video\":\n          return this.$i18n.t(\"upload.onlyVideoOrImageAgain\");\n        case \"pdf\":\n          return this.$i18n.t(\"material.dialog.form.onlyPDF\");\n        case \"file\":\n          return this.$i18n.t(\"material.dialog.form.onlyFile\");\n        default:\n          return \"\";\n      }\n    },\n    resetUploadState () {\n      this.$refs.uploadRef.clearFiles();\n      this.list = [];\n      this.fileList = [...this.fileList];\n    },\n    handleUploadProgress () {\n      this.$emit(\"uploadStatus\", true);\n    },\n    handleUploadSuccess (res, file, fileList) {\n      this.$emit(\"uploadStatus\", false);\n      this.fileList.push({\n        name: file.name,\n        hover: false,\n        url: file.response.data.file_name,\n      });\n\n      this.$emit(\n        \"editUrl\",\n        {\n          name: file.name,\n          hover: false,\n          url: file.response.data.file_name,\n        },\n        this.name,\n        this.index\n      );\n    },\n    previewImages () {\n      let images = [];\n      if (this.fileList && this.fileList.length > 0) {\n        this.fileList.forEach((item) => {\n          if (this.isImageUrl(item.url)) {\n            const fullUrl = this.imageUrl + item.url;\n            images.push(fullUrl);\n          }\n        });\n      }\n      return images;\n    },\n    previewImage (url) {\n      let images = [];\n      this.fileList.forEach((item) => {\n        if (this.isImageUrl(item.url)) {\n          images.push(item);\n        }\n      });\n      let index = findElem(images, \"url\", url);\n      if (index !== -1 && this.$refs.img && this.$refs.img[index]) {\n        this.$refs.img[index].showViewer = true;\n      }\n    },\n    previewVideo (url) {\n      // 保留原视频预览逻辑\n    },\n    previewFile (item) {\n      if (this.isImageUrl(item.url)) {\n        this.previewImage(item.url);\n      } else if (this.isVideoUrl(item.url)) {\n        this.previewVideo(item.url);\n      } else {\n        // 对于 PDF/PPT，直接在新窗口打开\n        window.open(this.imageUrl + item.url, \"_blank\");\n      }\n    },\n    isImageUrl (url) {\n      if (!url) return false;\n      const fileSuffix = url.substring(url.lastIndexOf(\".\") + 1).toLowerCase();\n      const whiteList = [\"jpg\", \"jpeg\", \"png\", \"gif\", \"webp\"];\n      return whiteList.includes(fileSuffix);\n    },\n    isVideoUrl (url) {\n      if (!url) return false;\n      const videoSuffix = [\"mp4\", \"ogg\", \"mov\", \"webm\"];\n      const suffix = url.substring(url.lastIndexOf(\".\") + 1).toLowerCase();\n      return videoSuffix.includes(suffix);\n    },\n    getFileIconClass (url) {\n      if (!url) return \"el-icon-document\";\n      const ext = url.substring(url.lastIndexOf(\".\") + 1).toLowerCase();\n      switch (ext) {\n        case \"pdf\":\n          return \"el-icon-document-pdf\";\n        case \"doc\":\n        case \"docx\":\n          return \"el-icon-document\";\n        case \"xls\":\n        case \"xlsx\":\n          return \"el-icon-document\";\n        case \"ppt\":\n        case \"pptx\":\n          return \"el-icon-document\";\n        default:\n          return \"el-icon-document\";\n      }\n    },\n    getFileNameFromUrl (url) {\n      if (!url) return \"\";\n      return url.split(\"/\").pop();\n    },\n    canPreview (url) {\n      return this.isImageUrl(url) || this.isVideoUrl(url);\n    },\n    downloadFile (url) {\n      window.open(this.imageUrl + url, \"_blank\");\n    },\n    uploadImage (id) {\n      this.list = [];\n      this.uploadImageId = id;\n      // 使用 $refs 替代 document.querySelector，确保组件内部元素被正确访问\n      if (this.$refs.uploadRef) {\n        this.$refs.uploadRef.$el.querySelector('input').click();\n      }\n    },\n    deleteImage (url) {\n      const index = findElem(this.fileList, \"url\", url);\n      if (index !== -1) {\n        this.fileList.splice(index, 1);\n      }\n    },\n    submitUpload () {\n      if (this.$refs.uploadRef) {\n        this.$refs.uploadRef.submit();\n      }\n    },\n  },\n};\n</script>\n\n<style type=\"text/css\" lang=\"scss\" scoped>\n.images {\n  display: flex;\n  flex-wrap: wrap;\n\n  .item {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n    width: 120px;\n    margin-right: 20px;\n    margin-bottom: 15px;\n\n    .img {\n      border: 1px dashed #eaeaea;\n      border-radius: 5px;\n      overflow: hidden;\n      position: relative;\n\n      .el-image {\n        display: block;\n      }\n\n      .mask {\n        position: absolute;\n        left: 0;\n        top: 0;\n        width: 120px;\n        height: 120px;\n        background: rgba($color: #000000, $alpha: 0.3);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n\n        i {\n          font-size: 20px;\n          color: #ffffff;\n          cursor: pointer;\n          margin: 0 8px;\n        }\n      }\n    }\n\n    .add {\n      border: 1px solid #dddddd;\n      border-radius: 5px;\n      cursor: pointer;\n    }\n\n    .text {\n      font-size: 14px;\n      color: #666666;\n    }\n  }\n\n  .add {\n    width: 120px;\n    height: 120px;\n    border: 1px solid #dddddd;\n    border-radius: 5px;\n    cursor: pointer;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    i {\n      font-size: 30px;\n      color: #999;\n    }\n  }\n}\n\n.avatar-uploader2 {\n  height: 0;\n}\n\n.file-preview {\n  width: 120px;\n  height: 120px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  border: 1px dashed #eaeaea;\n  border-radius: 5px;\n\n  i {\n    font-size: 40px;\n    color: #409EFF;\n    margin-bottom: 10px;\n  }\n\n  .file-name {\n    max-width: 100px;\n    text-align: center;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n    font-size: 12px;\n    color: #606266;\n  }\n}\n</style>", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./file-upload.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./file-upload.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./file-upload.vue?vue&type=template&id=84f8d436&scoped=true\"\nimport script from \"./file-upload.vue?vue&type=script&lang=js\"\nexport * from \"./file-upload.vue?vue&type=script&lang=js\"\nimport style0 from \"./file-upload.vue?vue&type=style&index=0&id=84f8d436&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"84f8d436\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"equipment-center\"},[_c('div',{staticClass:\"equipment-container\"},[_c('div',{staticClass:\"tree-panel\",style:({ width: _vm.treeWidth + 'px' })},[_c('el-card',{staticClass:\"tree-card\",attrs:{\"shadow\":\"never\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticClass:\"card-title\"},[_vm._v(_vm._s(_vm.$t('equipmentCenter.tree.title') || '设备列表'))]),_c('div',{staticClass:\"header-actions\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\",\"icon\":\"el-icon-plus\"},on:{\"click\":_vm.addGroupDialog}},[_vm._v(\" \"+_vm._s(_vm.$t('equipmentCenter.button.addGroup') || '新增分组')+\" \")])],1)]),_c('el-input',{staticClass:\"search-input\",attrs:{\"placeholder\":_vm.$t('equipmentCenter.tree.searchPlaceholder') || '输入关键字进行过滤',\"clearable\":\"\",\"prefix-icon\":\"el-icon-search\"},model:{value:(_vm.filterText),callback:function ($$v) {_vm.filterText=$$v},expression:\"filterText\"}}),_c('el-tree',{ref:\"tree\",staticClass:\"equipment-tree\",attrs:{\"data\":_vm.equipmentData,\"props\":_vm.defaultProps,\"filter-node-method\":_vm.filterNode,\"node-key\":\"id\",\"render-content\":_vm.renderContent,\"highlight-current\":true,\"empty-text\":_vm.$t('equipmentCenter.tree.noData'),\"expand-on-click-node\":false},on:{\"node-click\":_vm.handleNodeClick,\"node-expand\":_vm.handleNodeExpand}})],1)],1),_c('div',{staticClass:\"resize-handle\",on:{\"mousedown\":_vm.startResize}}),_c('div',{staticClass:\"detail-panel\",style:({ width: 'calc(100% - ' + (_vm.treeWidth + 8) + 'px)' })},[(!_vm.selectedDevice)?_c('div',{staticClass:\"empty-state\"},[_c('el-empty',{attrs:{\"description\":_vm.$t('equipmentCenter.tree.selectTip') || '请选择左侧设备或分组查看详情',\"image-size\":120},scopedSlots:_vm._u([{key:\"image\",fn:function(){return [_c('i',{staticClass:\"el-icon-s-platform empty-icon\"})]},proxy:true}],null,false,295858064)})],1):_c('div',[(_vm.selectedDevice.isClient)?_c('div',{staticClass:\"device-detail\"},[_c('el-card',{staticClass:\"info-card\",attrs:{\"shadow\":\"never\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticClass:\"card-title\"},[_vm._v(_vm._s(_vm.$t('equipmentCenter.tree.basicInfo') || '基本信息'))]),_c('div',{staticClass:\"header-actions\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\",\"icon\":\"el-icon-edit\"},on:{\"click\":function($event){return _vm.editDevice(_vm.selectedDevice)}}},[_vm._v(\" \"+_vm._s(_vm.$t('equipmentCenter.button.deviceEdit') || '编辑')+\" \")]),_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"small\",\"icon\":\"el-icon-delete\"},on:{\"click\":function($event){return _vm.deleteDevice(_vm.selectedDevice)}}},[_vm._v(\" \"+_vm._s(_vm.$t('equipmentCenter.button.delete') || '删除')+\" \")])],1)]),_c('div',{staticClass:\"device-info\"},[_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"info-label\"},[_vm._v(_vm._s(_vm.$t('equipmentCenter.table.alias_name') || '设备别名')+\":\")]),_c('span',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.selectedDevice.alias_name || '-'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"info-label\"},[_vm._v(_vm._s(_vm.$t('equipmentCenter.form.mac') || 'MAC地址')+\":\")]),_c('span',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.selectedDevice.mac_address || '-'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"info-label\"},[_vm._v(_vm._s(_vm.$t('equipmentCenter.table.group_name') || '分组名称')+\":\")]),_c('span',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.selectedDevice.group_name || '-'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"info-label\"},[_vm._v(_vm._s(_vm.$t('equipmentCenter.table.status') || '设备状态')+\":\")]),_c('el-tag',{attrs:{\"type\":_vm.selectedDevice.isOnline ? 'success' : 'danger',\"size\":\"small\"}},[_vm._v(\" \"+_vm._s(_vm.selectedDevice.isOnline ? (_vm.$t('equipmentCenter.table.online') || '在线') : (_vm.$t('equipmentCenter.table.offline') || '离线'))+\" \")])],1)]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"info-label\"},[_vm._v(_vm._s(_vm.$t('equipmentCenter.table.ip_addr'))+\":\")]),_c('span',[_vm._v(_vm._s(_vm.selectedDevice.ip_addr || '-'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"info-item\"},[_c('span',{staticClass:\"info-label\"},[_vm._v(_vm._s(_vm.$t('equipmentCenter.table.updated_at') || '最后上线时间')+\":\")]),_c('span',{staticClass:\"info-value\"},[_vm._v(_vm._s(_vm.formatTime(_vm.selectedDevice.updated_at) || '-'))])])])],1)],1)]),_c('el-card',{staticClass:\"info-card material-card\",attrs:{\"shadow\":\"never\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticClass:\"card-title\"},[_vm._v(_vm._s(_vm.$t('equipmentCenter.material.title')))]),_c('div',{staticClass:\"header-actions\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\",\"icon\":\"el-icon-upload2\"},on:{\"click\":function($event){return _vm.uploadMaterial(_vm.selectedDevice)}}},[_vm._v(\" \"+_vm._s(_vm.$t('equipmentCenter.button.upload'))+\" \")]),_c('el-button',{attrs:{\"type\":\"success\",\"size\":\"small\",\"icon\":\"el-icon-refresh\"},on:{\"click\":function($event){return _vm.replaceMaterial(_vm.selectedDevice)}}},[_vm._v(\" \"+_vm._s(_vm.$t('equipmentCenter.button.replace'))+\" \")]),_c('el-button',{attrs:{\"type\":\"warning\",\"size\":\"small\",\"icon\":\"el-icon-view\"},on:{\"click\":function($event){return _vm.previewDevice(_vm.selectedDevice)}}},[_vm._v(\" \"+_vm._s(_vm.$t('equipmentCenter.button.preview'))+\" \")])],1)]),_c('div',{staticClass:\"material-info\"},[(_vm.selectedDevice.material)?_c('div',{staticClass:\"material-details\"},[_c('img',{staticClass:\"material-thumbnail\",attrs:{\"src\":_vm.selectedDevice.material.thumbnail,\"alt\":\"素材缩略图\"}}),_c('div',{staticClass:\"material-text\"},[_c('p',{staticClass:\"material-name\"},[_vm._v(_vm._s(_vm.selectedDevice.material.name))]),_c('p',{staticClass:\"material-meta\"},[_vm._v(\"类型: \"+_vm._s(_vm.selectedDevice.material.type)+\" | 大小: \"+_vm._s(_vm.selectedDevice.material.size))])]),_c('div',{staticClass:\"material-actions\"},[_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.editMaterial(_vm.selectedDevice.material)}}},[_vm._v(\"编辑当前素材\")]),_c('el-button',{staticClass:\"danger-text\",attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.unbindMaterial(_vm.selectedDevice)}}},[_vm._v(\"解除关联\")])],1)]):_c('div',{staticClass:\"no-material\"},[_c('el-empty',{attrs:{\"description\":_vm.$t('equipmentCenter.material.noMaterial'),\"image-size\":80}})],1)])])],1):_c('div',{staticClass:\"group-detail\"},[_c('el-card',{staticClass:\"group-card\",attrs:{\"shadow\":\"never\"}},[_c('div',{staticClass:\"card-header\",attrs:{\"slot\":\"header\"},slot:\"header\"},[_c('span',{staticClass:\"card-title\"},[_vm._v(\"分组详情: \"+_vm._s(_vm.selectedDevice.label))]),_c('div',{staticClass:\"header-actions\"},[_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"small\",\"icon\":\"el-icon-edit\"},on:{\"click\":function($event){return _vm.editGroupDialog(_vm.selectedDevice)}}},[_vm._v(\" \"+_vm._s(_vm.$t('equipmentCenter.button.editGroup') || '编辑分组')+\" \")]),_c('el-button',{attrs:{\"type\":\"danger\",\"size\":\"small\",\"icon\":\"el-icon-delete\"},on:{\"click\":function($event){return _vm.deleteGroupDialog(_vm.selectedDevice)}}},[_vm._v(\" \"+_vm._s(_vm.$t('equipmentCenter.button.deleteGroup') || '删除分组')+\" \")]),_c('el-button',{attrs:{\"type\":\"success\",\"size\":\"small\",\"icon\":\"el-icon-upload\"},on:{\"click\":_vm.handleBatchUpload}},[_vm._v(\" 批量上传 \")]),_c('el-button',{attrs:{\"type\":\"success\",\"size\":\"small\",\"icon\":\"el-icon-s-promotion\",\"disabled\":_vm.multipleSelection.length === 0},on:{\"click\":_vm.batchPush}},[_vm._v(\" 批量推送 \")])],1)]),_c('div',{staticClass:\"stats-info\"},[_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"stat-item\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.selectedDevice.total || 0))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(_vm._s(_vm.$t('equipmentCenter.tree.total') || '设备总数'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"stat-item online\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s(_vm.selectedDevice.online || 0))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(_vm._s(_vm.$t('equipmentCenter.tree.online') || '在线设备'))])])]),_c('el-col',{attrs:{\"span\":8}},[_c('div',{staticClass:\"stat-item offline\"},[_c('div',{staticClass:\"stat-number\"},[_vm._v(_vm._s((_vm.selectedDevice.total || 0) - (_vm.selectedDevice.online || 0)))]),_c('div',{staticClass:\"stat-label\"},[_vm._v(_vm._s(_vm.$t('equipmentCenter.tree.offline') || '离线设备'))])])])],1)],1),_c('div',{staticStyle:{\"height\":\"calc(100vh - 400px)\",\"overflow-y\":\"auto\"}},[_c('el-table',{staticStyle:{\"width\":\"100%\",\"margin-top\":\"20px\"},attrs:{\"data\":_vm.selectedDevice.children},on:{\"selection-change\":_vm.handleSelectionChange}},[_c('el-table-column',{attrs:{\"type\":\"selection\",\"width\":\"55\"}}),_c('el-table-column',{attrs:{\"prop\":\"alias_name\",\"label\":\"设备别名\",\"width\":\"150px\",\"show-overflow-tooltip\":\"\"}}),_c('el-table-column',{attrs:{\"prop\":\"mac_address\",\"label\":\"MAC地址\",\"width\":\"150px\",\"show-overflow-tooltip\":\"\"}}),_c('el-table-column',{attrs:{\"label\":\"连接状态\",\"width\":\"80\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-tag',{attrs:{\"type\":scope.row.isOnline ? 'success' : 'danger',\"size\":\"small\"}},[_vm._v(\" \"+_vm._s(scope.row.isOnline ? '在线' : '离线')+\" \")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"status\",\"label\":\"下发状态\",\"show-overflow-tooltip\":\"\"}}),_c('el-table-column',{attrs:{\"label\":\"操作\",\"width\":\"150\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"size\":\"mini\"},on:{\"click\":function($event){return _vm.editDevice(scope.row)}}},[_vm._v(\"编辑\")]),_c('el-button',{attrs:{\"size\":\"mini\",\"type\":\"danger\"},on:{\"click\":function($event){return _vm.deleteDevice(scope.row)}}},[_vm._v(\"删除\")])]}}])})],1)],1)])],1)])])]),_c('el-dialog',{attrs:{\"title\":_vm.$t('equipmentCenter.dialog.title.edit'),\"visible\":_vm.isShow,\"width\":\"40%\",\"close-on-click-modal\":false},on:{\"update:visible\":function($event){_vm.isShow=$event},\"close\":_vm.closeDialog}},[_c('el-form',{ref:\"deviceInfoForm\",attrs:{\"model\":_vm.deviceInfo,\"rules\":_vm.rules,\"label-width\":\"120px\",\"label-position\":\"left\"}},[_c('el-form-item',{attrs:{\"label\":_vm.$t('equipmentCenter.table.group_name'),\"prop\":\"group_name\"}},[_c('el-select',{staticStyle:{\"width\":\"100%\"},attrs:{\"placeholder\":_vm.$t('equipmentCenter.form.groupPlaceholder'),\"filterable\":\"\",\"allow-create\":\"\",\"default-first-option\":\"\"},model:{value:(_vm.deviceInfo.group_name),callback:function ($$v) {_vm.$set(_vm.deviceInfo, \"group_name\", $$v)},expression:\"deviceInfo.group_name\"}},_vm._l((_vm.groupNameList),function(item){return _c('el-option',{key:item.id,attrs:{\"label\":item.name == 'null' ? '无分组' : item.name,\"value\":item.name}})}),1)],1),_c('el-form-item',{attrs:{\"label\":_vm.$t('equipmentCenter.table.alias_name'),\"prop\":\"alias_name\",\"required\":\"\"}},[_c('el-input',{attrs:{\"placeholder\":_vm.$t('equipmentCenter.form.namePlaceholder')},model:{value:(_vm.deviceInfo.alias_name),callback:function ($$v) {_vm.$set(_vm.deviceInfo, \"alias_name\", $$v)},expression:\"deviceInfo.alias_name\"}})],1),_c('el-form-item',{attrs:{\"label\":_vm.$t('equipmentCenter.form.mac'),\"prop\":\"mac_address\"}},[_c('el-input',{attrs:{\"disabled\":true},model:{value:(_vm.deviceInfo.mac_address),callback:function ($$v) {_vm.$set(_vm.deviceInfo, \"mac_address\", $$v)},expression:\"deviceInfo.mac_address\"}})],1)],1),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.isShow = false}}},[_vm._v(_vm._s(_vm.$t('public.cancel')))]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":function($event){return _vm.saveDevice()}}},[_vm._v(_vm._s(_vm.$t('public.confirm')))])],1)],1),_c('el-dialog',{attrs:{\"title\":\"更换素材\",\"visible\":_vm.isMaterialDialogVisible,\"width\":\"60%\",\"append-to-body\":\"\",\"close-on-click-modal\":false},on:{\"update:visible\":function($event){_vm.isMaterialDialogVisible=$event}}},[_c('div',{staticClass:\"material-select-dialog\"},[_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.isMaterialLoading),expression:\"isMaterialLoading\"}],staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.materials,\"height\":\"400px\",\"border\":\"\"}},[_c('el-table-column',{attrs:{\"width\":\"55\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-radio',{attrs:{\"label\":scope.row.id},nativeOn:{\"change\":function($event){return (() => { }).apply(null, arguments)}},model:{value:(_vm.selectedMaterialId),callback:function ($$v) {_vm.selectedMaterialId=$$v},expression:\"selectedMaterialId\"}},[_vm._v(\" \")])]}}])}),_c('el-table-column',{attrs:{\"label\":\"缩略图\",\"width\":\"120\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-image',{staticStyle:{\"width\":\"80px\",\"height\":\"80px\",\"border-radius\":\"4px\"},attrs:{\"src\":scope.row.thumbnail,\"preview-src-list\":[scope.row.thumbnail],\"fit\":\"cover\"}},[_c('div',{staticClass:\"image-slot\",attrs:{\"slot\":\"error\"},slot:\"error\"},[_c('i',{staticClass:\"el-icon-picture-outline\"})])])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"label\",\"label\":\"素材名称\",\"show-overflow-tooltip\":\"\"}}),_c('el-table-column',{attrs:{\"prop\":\"type\",\"label\":\"类型\",\"width\":\"100\",\"align\":\"center\"}}),_c('el-table-column',{attrs:{\"prop\":\"size\",\"label\":\"大小\",\"width\":\"120\",\"align\":\"center\"}})],1)],1),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.isMaterialDialogVisible = false}}},[_vm._v(_vm._s(_vm.$t('public.cancel')))]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.confirmReplaceMaterial}},[_vm._v(_vm._s(_vm.$t('public.confirm')))])],1)]),_c('el-dialog',{attrs:{\"title\":\"上传新素材\",\"visible\":_vm.isUploadDialogVisible,\"width\":\"50%\",\"append-to-body\":\"\",\"close-on-click-modal\":false},on:{\"update:visible\":function($event){_vm.isUploadDialogVisible=$event}}},[(_vm.selectedDevice)?_c('div',{staticClass:\"upload-dialog-content\"},[_c('p',[_vm._v(\"上传的素材将自动与当前设备 \"),_c('el-tag',{attrs:{\"size\":\"small\"}},[_vm._v(_vm._s(_vm.selectedDevice.label))]),_vm._v(\" 关联。\")],1),_c('file-upload',{attrs:{\"file-list\":_vm.uploadFileList,\"uploadNum\":1,\"fileType\":\"image/video\"},on:{\"editUrl\":_vm.handleUploadSuccess,\"uploadStatus\":_vm.handleUploadStatus}})],1):_vm._e(),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.isUploadDialogVisible = false}}},[_vm._v(\"关闭\")])],1)]),_c('el-dialog',{attrs:{\"title\":\"实时预览\",\"visible\":_vm.isPreviewDialogVisible,\"width\":\"800px\",\"append-to-body\":\"\",\"close-on-click-modal\":false},on:{\"update:visible\":function($event){_vm.isPreviewDialogVisible=$event},\"close\":_vm.closePreviewDialog}},[_c('div',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.isRtcConnecting),expression:\"isRtcConnecting\"}],staticClass:\"preview-dialog-content\",attrs:{\"element-loading-text\":\"正在建立视频连接...\"}},[_c('video',{ref:\"previewVideo\",staticStyle:{\"width\":\"100%\",\"height\":\"450px\",\"background\":\"#000\"},attrs:{\"autoplay\":\"\",\"playsinline\":\"\"}}),(!_vm.isRtcConnecting && !_vm.previewStream)?_c('div',{staticClass:\"preview-placeholder\"},[_c('i',{staticClass:\"el-icon-video-camera-solid\"}),_c('p',[_vm._v(\"无法加载视频流\")])]):_vm._e()])]),_c('el-dialog',{attrs:{\"title\":\"编辑素材信息\",\"visible\":_vm.isEditMaterialDialogVisible,\"width\":\"40%\",\"append-to-body\":\"\",\"close-on-click-modal\":false},on:{\"update:visible\":function($event){_vm.isEditMaterialDialogVisible=$event}}},[(_vm.editingMaterial)?_c('el-form',{ref:\"materialForm\",attrs:{\"model\":_vm.editingMaterial,\"rules\":_vm.materialRules,\"label-width\":\"100px\"}},[_c('el-form-item',{attrs:{\"label\":\"素材名称\",\"prop\":\"name\"}},[_c('el-input',{attrs:{\"placeholder\":\"请输入素材名称\"},model:{value:(_vm.editingMaterial.name),callback:function ($$v) {_vm.$set(_vm.editingMaterial, \"name\", $$v)},expression:\"editingMaterial.name\"}})],1),_c('el-form-item',{attrs:{\"label\":\"素材类型\",\"prop\":\"type\"}},[_c('el-input',{attrs:{\"disabled\":true},model:{value:(_vm.editingMaterial.type),callback:function ($$v) {_vm.$set(_vm.editingMaterial, \"type\", $$v)},expression:\"editingMaterial.type\"}})],1),_c('el-form-item',{attrs:{\"label\":\"素材大小\",\"prop\":\"size\"}},[_c('el-input',{attrs:{\"disabled\":true},model:{value:(_vm.editingMaterial.size),callback:function ($$v) {_vm.$set(_vm.editingMaterial, \"size\", $$v)},expression:\"editingMaterial.size\"}})],1)],1):_vm._e(),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.isEditMaterialDialogVisible = false}}},[_vm._v(\"取消\")]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.saveMaterial}},[_vm._v(\"保存\")])],1)],1),_c('el-dialog',{attrs:{\"title\":_vm.isEditGroup ? _vm.$t('equipmentCenter.dialog.title.editGroup') : _vm.$t('equipmentCenter.dialog.title.addGroup'),\"visible\":_vm.isGroupDialogVisible,\"width\":\"40%\",\"close-on-click-modal\":false},on:{\"update:visible\":function($event){_vm.isGroupDialogVisible=$event},\"close\":_vm.closeGroupDialog}},[_c('el-form',{ref:\"groupForm\",attrs:{\"model\":_vm.groupForm,\"rules\":_vm.groupRules,\"label-width\":\"120px\",\"label-position\":\"left\"}},[_c('el-form-item',{attrs:{\"label\":_vm.$t('equipmentCenter.dialog.form.groupName'),\"prop\":\"name\"}},[_c('el-input',{attrs:{\"placeholder\":_vm.$t('equipmentCenter.dialog.form.groupNamePlaceholder')},model:{value:(_vm.groupForm.name),callback:function ($$v) {_vm.$set(_vm.groupForm, \"name\", $$v)},expression:\"groupForm.name\"}})],1),_c('el-form-item',{attrs:{\"label\":_vm.$t('equipmentCenter.dialog.form.groupDescription'),\"prop\":\"description\"}},[_c('el-input',{attrs:{\"type\":\"textarea\",\"rows\":3,\"placeholder\":_vm.$t('equipmentCenter.dialog.form.groupDescriptionPlaceholder')},model:{value:(_vm.groupForm.description),callback:function ($$v) {_vm.$set(_vm.groupForm, \"description\", $$v)},expression:\"groupForm.description\"}})],1)],1),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{on:{\"click\":function($event){_vm.isGroupDialogVisible = false}}},[_vm._v(_vm._s(_vm.$t('public.cancel')))]),_c('el-button',{attrs:{\"type\":\"primary\"},on:{\"click\":_vm.saveGroup}},[_vm._v(_vm._s(_vm.$t('public.confirm')))])],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "import request, { postBlob, getBlob, handleImport } from '@/utils/request'\n\nexport function getList (params) {\n  return request({\n    url: `/admin/equipment/getList`,\n    method: 'get',\n    params\n  })\n}\n\nexport function getTreeList (params) {\n  return request({\n    url: `/admin/equipment/getTreeList`,\n    method: 'get',\n    params\n  })\n}\n\nexport function add (data) {\n  return request({\n    url: `/manage/equipment/addEquipment`,\n    method: 'post',\n    data\n  })\n}\n\nexport function edit (data) {\n  return request({\n    url: `/admin/equipment/editEquipment`,\n    method: 'put',\n    data\n  })\n}\n\nexport function del (id) {\n  return request({\n    url: `/admin/equipment/delete/` + id,\n    method: 'delete'\n  })\n}\n\nexport function getGroupNameList (params) {\n  return request({\n    url: `/admin/equipment/groupNameList`,\n    method: 'get',\n    params\n  })\n}\n\n// 分组管理相关API\nexport function addGroup (data) {\n  return request({\n    url: `/admin/equipment/addGroup`,\n    method: 'post',\n    data\n  })\n}\n\nexport function editGroup (data) {\n  return request({\n    url: `/admin/equipment/editGroup`,\n    method: 'put',\n    data\n  })\n}\n\nexport function deleteGroup (id) {\n  return request({\n    url: `/admin/equipment/deleteGroup/${id}`,\n    method: 'delete'\n  })\n}\n\nexport function getGroupDetail (id) {\n  return request({\n    url: `/admin/equipment/getGroupDetail/${id}`,\n    method: 'get'\n  })\n}\n\n// 批量上传文件并关联设备\nexport function batchUploadFiles (data) {\n  return request({\n    url: `/admin/sourcematerial/batchUpload`,\n    method: 'post',\n    data, // data 应该是 FormData\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  })\n}\n", "<template>\n  <div class=\"equipment-center\">\n    <!-- 主要内容区域 -->\n    <div class=\"equipment-container\">\n      <!-- 左侧设备树 -->\n      <div class=\"tree-panel\" :style=\"{ width: treeWidth + 'px' }\">\n        <el-card class=\"tree-card\" shadow=\"never\">\n          <div slot=\"header\" class=\"card-header\">\n            <span class=\"card-title\">{{ $t('equipmentCenter.tree.title') || '设备列表' }}</span>\n            <div class=\"header-actions\">\n              <el-button type=\"primary\" size=\"small\" icon=\"el-icon-plus\" @click=\"addGroupDialog\">\n                {{ $t('equipmentCenter.button.addGroup') || '新增分组' }}\n              </el-button>\n            </div>\n          </div>\n\n          <el-input :placeholder=\"$t('equipmentCenter.tree.searchPlaceholder') || '输入关键字进行过滤'\" v-model=\"filterText\"\n            clearable prefix-icon=\"el-icon-search\" class=\"search-input\" />\n\n          <el-tree class=\"equipment-tree\" :data=\"equipmentData\" :props=\"defaultProps\" :filter-node-method=\"filterNode\"\n            ref=\"tree\" node-key=\"id\" @node-click=\"handleNodeClick\" :render-content=\"renderContent\"\n            :highlight-current=\"true\" :empty-text=\"$t('equipmentCenter.tree.noData')\" @node-expand=\"handleNodeExpand\"\n            :expand-on-click-node=\"false\">\n          </el-tree>\n        </el-card>\n      </div>\n\n      <!-- 拖拽分隔条 -->\n      <div class=\"resize-handle\" @mousedown=\"startResize\"></div>\n\n      <!-- 右侧设备详情 -->\n      <div class=\"detail-panel\" :style=\"{ width: 'calc(100% - ' + (treeWidth + 8) + 'px)' }\">\n        <!-- 未选择设备时的提示 -->\n        <div v-if=\"!selectedDevice\" class=\"empty-state\">\n          <el-empty :description=\"$t('equipmentCenter.tree.selectTip') || '请选择左侧设备或分组查看详情'\" :image-size=\"120\">\n            <template #image>\n              <i class=\"el-icon-s-platform empty-icon\"></i>\n            </template>\n          </el-empty>\n        </div>\n\n        <!-- 详情内容 -->\n        <div v-else>\n          <!-- 单个设备详情视图 -->\n          <div v-if=\"selectedDevice.isClient\" class=\"device-detail\">\n            <el-card class=\"info-card\" shadow=\"never\">\n              <div slot=\"header\" class=\"card-header\">\n                <span class=\"card-title\">{{ $t('equipmentCenter.tree.basicInfo') || '基本信息' }}</span>\n                <div class=\"header-actions\">\n                  <el-button type=\"primary\" size=\"small\" icon=\"el-icon-edit\" @click=\"editDevice(selectedDevice)\">\n                    {{ $t('equipmentCenter.button.deviceEdit') || '编辑' }}\n                  </el-button>\n                  <el-button type=\"danger\" size=\"small\" icon=\"el-icon-delete\" @click=\"deleteDevice(selectedDevice)\">\n                    {{ $t('equipmentCenter.button.delete') || '删除' }}\n                  </el-button>\n                </div>\n              </div>\n              <div class=\"device-info\">\n                <el-row :gutter=\"20\">\n                  <!-- <el-col :span=\"8\">\n                    <div class=\"info-item\">\n                      <span class=\"info-label\">{{ $t('equipmentCenter.table.name') || '设备名称' }}:</span>\n                      <span class=\"info-value\">{{ selectedDevice.label || '-' }}</span>\n                    </div>\n                  </el-col> -->\n                  <el-col :span=\"8\">\n                    <div class=\"info-item\">\n                      <span class=\"info-label\">{{ $t('equipmentCenter.table.alias_name') || '设备别名' }}:</span>\n                      <span class=\"info-value\">{{ selectedDevice.alias_name || '-' }}</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"info-item\">\n                      <span class=\"info-label\">{{ $t('equipmentCenter.form.mac') || 'MAC地址' }}:</span>\n                      <span class=\"info-value\">{{ selectedDevice.mac_address || '-' }}</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"info-item\">\n                      <span class=\"info-label\">{{ $t('equipmentCenter.table.group_name') || '分组名称' }}:</span>\n                      <span class=\"info-value\">{{ selectedDevice.group_name || '-' }}</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"info-item\">\n                      <span class=\"info-label\">{{ $t('equipmentCenter.table.status') || '设备状态' }}:</span>\n                      <el-tag :type=\"selectedDevice.isOnline ? 'success' : 'danger'\" size=\"small\">\n                        {{ selectedDevice.isOnline ? ($t('equipmentCenter.table.online') || '在线') :\n                          ($t('equipmentCenter.table.offline') || '离线') }}\n                      </el-tag>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"info-item\">\n                      <span class=\"info-label\">{{ $t('equipmentCenter.table.ip_addr') }}:</span>\n                      <span>{{ selectedDevice.ip_addr || '-' }}</span>\n\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"info-item\">\n                      <span class=\"info-label\">{{ $t('equipmentCenter.table.updated_at') || '最后上线时间' }}:</span>\n                      <span class=\"info-value\">{{ formatTime(selectedDevice.updated_at) || '-' }}</span>\n                    </div>\n                  </el-col>\n                </el-row>\n              </div>\n            </el-card>\n\n            <!-- 素材与预览 -->\n            <el-card class=\"info-card material-card\" shadow=\"never\">\n              <div slot=\"header\" class=\"card-header\">\n                <span class=\"card-title\">{{ $t('equipmentCenter.material.title') }}</span>\n                <div class=\"header-actions\">\n                  <el-button type=\"primary\" size=\"small\" icon=\"el-icon-upload2\" @click=\"uploadMaterial(selectedDevice)\">\n                    {{ $t('equipmentCenter.button.upload') }}\n                  </el-button>\n                  <el-button type=\"success\" size=\"small\" icon=\"el-icon-refresh\"\n                    @click=\"replaceMaterial(selectedDevice)\">\n                    {{ $t('equipmentCenter.button.replace') }}\n                  </el-button>\n                  <el-button type=\"warning\" size=\"small\" icon=\"el-icon-view\" @click=\"previewDevice(selectedDevice)\">\n                    {{ $t('equipmentCenter.button.preview') }}\n                  </el-button>\n                </div>\n              </div>\n              <div class=\"material-info\">\n                <!-- 假设 selectedDevice.material 存在 -->\n                <div v-if=\"selectedDevice.material\" class=\"material-details\">\n                  <img :src=\"selectedDevice.material.thumbnail\" alt=\"素材缩略图\" class=\"material-thumbnail\" />\n                  <div class=\"material-text\">\n                    <p class=\"material-name\">{{ selectedDevice.material.name }}</p>\n                    <p class=\"material-meta\">类型: {{ selectedDevice.material.type }} | 大小: {{\n                      selectedDevice.material.size }}</p>\n                  </div>\n                  <div class=\"material-actions\">\n                    <el-button type=\"text\" size=\"small\"\n                      @click=\"editMaterial(selectedDevice.material)\">编辑当前素材</el-button>\n                    <el-button type=\"text\" size=\"small\" class=\"danger-text\"\n                      @click=\"unbindMaterial(selectedDevice)\">解除关联</el-button>\n                  </div>\n                </div>\n                <div v-else class=\"no-material\">\n                  <el-empty :description=\"$t('equipmentCenter.material.noMaterial')\" :image-size=\"80\"></el-empty>\n                </div>\n              </div>\n            </el-card>\n          </div>\n\n          <!-- 分组详情视图 -->\n          <div v-else class=\"group-detail\">\n            <el-card class=\"group-card\" shadow=\"never\">\n              <div slot=\"header\" class=\"card-header\">\n                <span class=\"card-title\">分组详情: {{ selectedDevice.label }}</span>\n                <div class=\"header-actions\">\n                  <el-button type=\"primary\" size=\"small\" icon=\"el-icon-edit\" @click=\"editGroupDialog(selectedDevice)\">\n                    {{ $t('equipmentCenter.button.editGroup') || '编辑分组' }}\n                  </el-button>\n                  <el-button type=\"danger\" size=\"small\" icon=\"el-icon-delete\"\n                    @click=\"deleteGroupDialog(selectedDevice)\">\n                    {{ $t('equipmentCenter.button.deleteGroup') || '删除分组' }}\n                  </el-button>\n                  <el-button type=\"success\" size=\"small\" icon=\"el-icon-upload\" @click=\"handleBatchUpload\">\n                    批量上传\n                  </el-button>\n                  <el-button type=\"success\" size=\"small\" icon=\"el-icon-s-promotion\" @click=\"batchPush\"\n                    :disabled=\"multipleSelection.length === 0\">\n                    批量推送\n                  </el-button>\n                </div>\n              </div>\n\n\n\n              <!-- 统计信息 -->\n              <div class=\"stats-info\">\n                <el-row :gutter=\"20\">\n                  <el-col :span=\"8\">\n                    <div class=\"stat-item\">\n                      <div class=\"stat-number\">{{ selectedDevice.total || 0 }}</div>\n                      <div class=\"stat-label\">{{ $t('equipmentCenter.tree.total') || '设备总数' }}</div>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"stat-item online\">\n                      <div class=\"stat-number\">{{ selectedDevice.online || 0 }}</div>\n                      <div class=\"stat-label\">{{ $t('equipmentCenter.tree.online') || '在线设备' }}</div>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"stat-item offline\">\n                      <div class=\"stat-number\">{{ (selectedDevice.total || 0) - (selectedDevice.online || 0) }}</div>\n                      <div class=\"stat-label\">{{ $t('equipmentCenter.tree.offline') || '离线设备' }}</div>\n                    </div>\n                  </el-col>\n                </el-row>\n              </div>\n              <!-- 设备列表 -->\n              <div style=\"height:calc(100vh - 400px);overflow-y: auto;\"\">\n                <el-table :data=\"selectedDevice.children\" @selection-change=\"handleSelectionChange\"\n                style=\"width: 100%; margin-top: 20px; \">\n                <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n                <el-table-column prop=\"alias_name\" label=\"设备别名\" width=\"150px\" show-overflow-tooltip></el-table-column>\n                <el-table-column prop=\"mac_address\" label=\"MAC地址\" width=\"150px\" show-overflow-tooltip></el-table-column>\n                <el-table-column label=\"连接状态\" width=\"80\" align=\"center\">\n                  <template slot-scope=\"scope\">\n                    <el-tag :type=\"scope.row.isOnline ? 'success' : 'danger'\" size=\"small\">\n                      {{ scope.row.isOnline ? '在线' : '离线' }}\n                    </el-tag>\n                  </template>\n                </el-table-column>\n                <el-table-column prop=\"status\" label=\"下发状态\" show-overflow-tooltip></el-table-column>\n                <el-table-column label=\"操作\" width=\"150\" align=\"center\">\n                  <template slot-scope=\"scope\">\n\n                    <el-button size=\"mini\" @click=\"editDevice(scope.row)\">编辑</el-button>\n                    <el-button size=\"mini\" type=\"danger\" @click=\"deleteDevice(scope.row)\">删除</el-button>\n                  </template>\n                </el-table-column>\n                </el-table>\n              </div>\n            </el-card>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 编辑设备弹窗 -->\n    <el-dialog :title=\"$t('equipmentCenter.dialog.title.edit')\" :visible.sync=\"isShow\" width=\"40%\" @close=\"closeDialog\"\n      :close-on-click-modal=\"false\">\n      <el-form :model=\"deviceInfo\" :rules=\"rules\" ref=\"deviceInfoForm\" label-width=\"120px\" label-position=\"left\">\n        <el-form-item :label=\"$t('equipmentCenter.table.group_name')\" prop=\"group_name\">\n          <el-select v-model=\"deviceInfo.group_name\" :placeholder=\"$t('equipmentCenter.form.groupPlaceholder')\"\n            style=\"width: 100%;\" filterable allow-create default-first-option>\n            <el-option v-for=\"item in groupNameList\" :key=\"item.id\" :label=\"item.name == 'null' ? '无分组' : item.name\"\n              :value=\"item.name\">\n            </el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item :label=\"$t('equipmentCenter.table.alias_name')\" prop=\"alias_name\" required>\n          <el-input v-model=\"deviceInfo.alias_name\"\n            :placeholder=\"$t('equipmentCenter.form.namePlaceholder')\"></el-input>\n        </el-form-item>\n        <el-form-item :label=\"$t('equipmentCenter.form.mac')\" prop=\"mac_address\">\n          <el-input v-model=\"deviceInfo.mac_address\" :disabled=\"true\"></el-input>\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"isShow = false\">{{ $t('public.cancel') }}</el-button>\n        <el-button type=\"primary\" @click=\"saveDevice()\">{{ $t('public.confirm') }}</el-button>\n      </span>\n    </el-dialog>\n\n    <!-- 素材选择弹窗 -->\n    <el-dialog title=\"更换素材\" :visible.sync=\"isMaterialDialogVisible\" width=\"60%\" append-to-body\n      :close-on-click-modal=\"false\">\n      <div class=\"material-select-dialog\">\n        <el-table v-loading=\"isMaterialLoading\" :data=\"materials\" height=\"400px\" style=\"width: 100%;\" border>\n          <el-table-column width=\"55\" align=\"center\">\n            <template slot-scope=\"scope\">\n              <el-radio :label=\"scope.row.id\" v-model=\"selectedMaterialId\" @change.native=\"() => { }\">&nbsp;</el-radio>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"缩略图\" width=\"120\" align=\"center\">\n            <template slot-scope=\"scope\">\n              <el-image style=\"width: 80px; height: 80px; border-radius: 4px;\" :src=\"scope.row.thumbnail\"\n                :preview-src-list=\"[scope.row.thumbnail]\" fit=\"cover\">\n                <div slot=\"error\" class=\"image-slot\">\n                  <i class=\"el-icon-picture-outline\"></i>\n                </div>\n              </el-image>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"label\" label=\"素材名称\" show-overflow-tooltip></el-table-column>\n          <el-table-column prop=\"type\" label=\"类型\" width=\"100\" align=\"center\"></el-table-column>\n          <el-table-column prop=\"size\" label=\"大小\" width=\"120\" align=\"center\"></el-table-column>\n        </el-table>\n      </div>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"isMaterialDialogVisible = false\">{{ $t('public.cancel') }}</el-button>\n        <el-button type=\"primary\" @click=\"confirmReplaceMaterial\">{{ $t('public.confirm') }}</el-button>\n      </span>\n    </el-dialog>\n\n    <!-- 素材上传弹窗 -->\n    <el-dialog title=\"上传新素材\" :visible.sync=\"isUploadDialogVisible\" width=\"50%\" append-to-body\n      :close-on-click-modal=\"false\">\n      <div v-if=\"selectedDevice\" class=\"upload-dialog-content\">\n        <p>上传的素材将自动与当前设备 <el-tag size=\"small\">{{ selectedDevice.label }}</el-tag> 关联。</p>\n        <file-upload :file-list=\"uploadFileList\" :uploadNum=\"1\" fileType=\"image/video\" @editUrl=\"handleUploadSuccess\"\n          @uploadStatus=\"handleUploadStatus\" />\n      </div>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"isUploadDialogVisible = false\">关闭</el-button>\n      </span>\n    </el-dialog>\n\n    <!-- WebRTC预览弹窗 -->\n    <el-dialog title=\"实时预览\" :visible.sync=\"isPreviewDialogVisible\" width=\"800px\" @close=\"closePreviewDialog\"\n      append-to-body :close-on-click-modal=\"false\">\n      <div class=\"preview-dialog-content\" v-loading=\"isRtcConnecting\" element-loading-text=\"正在建立视频连接...\">\n        <video ref=\"previewVideo\" autoplay playsinline style=\"width: 100%; height: 450px; background: #000;\"></video>\n        <div v-if=\"!isRtcConnecting && !previewStream\" class=\"preview-placeholder\">\n          <i class=\"el-icon-video-camera-solid\"></i>\n          <p>无法加载视频流</p>\n        </div>\n      </div>\n    </el-dialog>\n\n    <!-- 素材编辑弹窗 -->\n    <el-dialog title=\"编辑素材信息\" :visible.sync=\"isEditMaterialDialogVisible\" width=\"40%\" append-to-body\n      :close-on-click-modal=\"false\">\n      <el-form v-if=\"editingMaterial\" :model=\"editingMaterial\" :rules=\"materialRules\" ref=\"materialForm\"\n        label-width=\"100px\">\n        <el-form-item label=\"素材名称\" prop=\"name\">\n          <el-input v-model=\"editingMaterial.name\" placeholder=\"请输入素材名称\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"素材类型\" prop=\"type\">\n          <el-input v-model=\"editingMaterial.type\" :disabled=\"true\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"素材大小\" prop=\"size\">\n          <el-input v-model=\"editingMaterial.size\" :disabled=\"true\"></el-input>\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"isEditMaterialDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"saveMaterial\">保存</el-button>\n      </span>\n    </el-dialog>\n\n    <!-- 分组新增/编辑弹窗 -->\n    <el-dialog\n      :title=\"isEditGroup ? $t('equipmentCenter.dialog.title.editGroup') : $t('equipmentCenter.dialog.title.addGroup')\"\n      :visible.sync=\"isGroupDialogVisible\" width=\"40%\" @close=\"closeGroupDialog\" :close-on-click-modal=\"false\">\n      <el-form :model=\"groupForm\" :rules=\"groupRules\" ref=\"groupForm\" label-width=\"120px\" label-position=\"left\">\n        <el-form-item :label=\"$t('equipmentCenter.dialog.form.groupName')\" prop=\"name\">\n          <el-input v-model=\"groupForm.name\"\n            :placeholder=\"$t('equipmentCenter.dialog.form.groupNamePlaceholder')\"></el-input>\n        </el-form-item>\n        <el-form-item :label=\"$t('equipmentCenter.dialog.form.groupDescription')\" prop=\"description\">\n          <el-input v-model=\"groupForm.description\" type=\"textarea\" :rows=\"3\"\n            :placeholder=\"$t('equipmentCenter.dialog.form.groupDescriptionPlaceholder')\"></el-input>\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"isGroupDialogVisible = false\">{{ $t('public.cancel') }}</el-button>\n        <el-button type=\"primary\" @click=\"saveGroup\">{{ $t('public.confirm') }}</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getTreeList, edit, del, getGroupNameList, addGroup, editGroup, deleteGroup, batchUploadFiles } from '@/api/equipmentCenter.js'\nimport { getList as getMaterialList, edit as editMaterial } from '@/api/material.js'\nimport FileUpload from '@/components/file-upload.vue'\n\nexport default {\n  name: 'EquipmentCenter',\n  components: { FileUpload },\n  props: ['groupName', 'macAddress'],\n  watch: {\n    filterText (val) {\n      this.$refs.tree.filter(val);\n    },\n    '$route.params': {\n      handler: 'handleRouteChange',\n      deep: true\n    }\n  },\n  data () {\n    return {\n      equipmentData: [],\n      defaultProps: {\n        children: 'children',\n        label: 'label',\n        isClient: 'isClient',\n        mac_address: 'mac_address',\n      },\n      filterText: \"\",\n      selectedDevice: null, // 当前选中的设备或分组\n      treeWidth: 250,\n      isResizing: false,\n      deviceInfo: {\n        mac_address: '',\n        alias_name: '',\n        group_name: '',\n      },\n      groupNameList: [],\n      rules: {\n        alias_name: [\n          { required: true, message: this.$t('equipmentCenter.form.namePlaceholder'), trigger: 'blur' },\n        ],\n      },\n      isShow: false,\n      isLoading: false,\n      multipleSelection: [], // 用于存储表格中的多选行\n\n      // 素材选择弹窗\n      isMaterialDialogVisible: false,\n      materials: [],\n      isMaterialLoading: false,\n      selectedMaterialId: null,\n\n      // 素材上传弹窗\n      isUploadDialogVisible: false,\n      uploadFileList: [],\n      uploading: false,\n\n      // WebRTC 预览弹窗\n      isPreviewDialogVisible: false,\n      rtcPeerConnection: null,\n      previewStream: null,\n      isRtcConnecting: false,\n\n      // 素材编辑弹窗\n      isEditMaterialDialogVisible: false,\n      editingMaterial: null,\n      materialRules: {\n        name: [\n          { required: true, message: '请输入素材名称', trigger: 'blur' },\n        ],\n      },\n\n      // 分组管理弹窗\n      isGroupDialogVisible: false,\n      isEditGroup: false,\n      groupForm: {\n        id: null,\n        name: '',\n        description: '',\n      },\n      groupRules: {\n        name: [\n          { required: true, message: this.$t('equipmentCenter.dialog.message.groupNameRequired'), trigger: 'blur' },\n        ],\n      }\n    }\n  },\n  created () {\n    this.getList()\n  },\n  methods: {\n    getList () {\n      this.isLoading = true\n      getTreeList().then(res => {\n        if (res.code == 0) {\n          this.equipmentData = res.data\n          this.handleRouteChange();\n        }\n      }).finally(() => {\n        this.isLoading = false\n      })\n    },\n    handleRouteChange () {\n      this.$nextTick(() => {\n        if (!this.$refs.tree || !this.equipmentData || this.equipmentData.length === 0) return;\n\n        const { groupName, macAddress } = this.$route.params;\n\n        // 清除之前的状态\n        if (this.selectedDevice) {\n          this.$refs.tree.setCurrentKey(null);\n          this.selectedDevice = null;\n        }\n\n        if (macAddress) {\n          const deviceNode = this.findNodeByMac(this.equipmentData, macAddress);\n          if (deviceNode) {\n            this.selectedDevice = deviceNode;\n            this.$refs.tree.setCurrentKey(deviceNode.id);\n\n            // 查找并展开父节点\n            const parentNode = this.findNode(this.equipmentData, n => n.children && n.children.some(c => c.id === deviceNode.id));\n            if (parentNode && this.$refs.tree.store.nodesMap[parentNode.id]) {\n              this.$refs.tree.store.nodesMap[parentNode.id].expand();\n            }\n          }\n        } else if (groupName) {\n          const groupNode = this.findNodeByLabel(this.equipmentData, groupName);\n          if (groupNode) {\n            this.selectedDevice = groupNode;\n            this.$refs.tree.setCurrentKey(groupNode.id);\n            if (this.$refs.tree.store.nodesMap[groupNode.id]) {\n              this.$refs.tree.store.nodesMap[groupNode.id].expand();\n            }\n          }\n        }\n      });\n    },\n    findNodeById (tree, id) {\n      for (const node of tree) {\n        if (node.id == id) {\n          return node;\n        }\n        if (node.children) {\n          const found = this.findNodeById(node.children, id);\n          if (found) {\n            return found;\n          }\n        }\n      }\n      return null;\n    },\n    findNode (tree, predicate) {\n      for (const node of tree) {\n        if (predicate(node)) {\n          return node;\n        }\n        if (node.children) {\n          const found = this.findNode(node.children, predicate);\n          if (found) {\n            return found;\n          }\n        }\n      }\n      return null;\n    },\n    findNodeByLabel (tree, label) {\n      return this.findNode(tree, (node) => !node.isClient && node.label === label);\n    },\n    findNodeByMac (tree, mac) {\n      return this.findNode(tree, (node) => node.isClient && node.mac_address === mac);\n    },\n    handleNodeClick (data, node) {\n      let groupName = null;\n      if (data.isClient) {\n        let parent = node.parent;\n        // 循环向上查找，直到找到非客户端的父节点（即分组）\n        while (parent && parent.data && parent.data.isClient) {\n          parent = parent.parent;\n        }\n        if (parent && parent.data) {\n          groupName = parent.data.label;\n        }\n      } else {\n        groupName = data.label;\n      }\n\n      const macAddress = data.isClient ? data.mac_address : undefined;\n\n      const newParams = {};\n      if (groupName) {\n        newParams.groupName = groupName;\n      }\n      if (macAddress) {\n        newParams.macAddress = macAddress;\n      }\n\n      if (this.$route.params.groupName !== newParams.groupName || this.$route.params.macAddress !== newParams.macAddress) {\n        this.$router.push({ name: 'EquipmentCenter', params: newParams });\n      }\n\n      this.selectedDevice = data;\n\n      // TODO: 此处为前端模拟数据，实际数据应从API获取\n      if (this.selectedDevice.isClient && !this.selectedDevice.material) {\n        this.selectedDevice = {\n          ...this.selectedDevice,\n          material: {\n            id: 'mat-001',\n            name: '默认宣传视频.mp4',\n            thumbnail: 'https://shadow.elemecdn.com/app/element/hamburger.9cf7b091-55e9-11e9-a976-7f4d0b07eef6.png',\n            type: '视频',\n            size: '15.8MB'\n          }\n        }\n      }\n\n      if (!data.isClient) {\n        node.expanded ? this.$refs.tree.store?.nodesMap[data.id]?.collapse() : this.$refs.tree.store?.nodesMap[data.id]?.expand();\n      } else {\n        this.$refs.tree.setCurrentKey(data.id);\n      }\n    },\n\n    filterNode (value, data) {\n      if (!value) return true;\n      const lowerCaseValue = value.toLowerCase();\n      return (data.label && data.label.toLowerCase().indexOf(lowerCaseValue) !== -1) ||\n        (data.alias_name && data.alias_name.toLowerCase().indexOf(lowerCaseValue) !== -1) ||\n        (data.mac_address && data.mac_address.toLowerCase().indexOf(lowerCaseValue) !== -1);\n    },\n\n    renderContent (h, { node, data }) {\n      const isClient = data.isClient;\n      return h(\n        'span',\n        { class: 'custom-tree-node' },\n        [\n          h(\n            'span',\n            { class: 'node-content' },\n            [\n              h('i', {\n                class: [\n                  isClient ? 'el-icon-monitor' : (!node.expanded ? 'el-icon-folder' : 'el-icon-folder-opened'),\n                  'node-icon',\n                  { 'online-icon': isClient && data.isOnline, 'offline-icon': isClient && !data.isOnline }\n                ]\n              }),\n              h('span', { class: 'node-label' }, node.label == 'null' ? this.$t('equipmentCenter.tree.null') : node.label),\n              isClient\n                ? h(\n                  'el-tag',\n                  {\n                    props: {\n                      size: 'mini',\n                      type: data.isOnline ? 'success' : 'danger',\n                    },\n                    class: 'status-tag',\n                  },\n                  data.isOnline ? this.$t('equipmentCenter.tree.online') : this.$t(\"equipmentCenter.tree.offline\")\n                )\n                : null,\n            ]\n          ),\n          !isClient\n            ? h(\n              'span',\n              { class: 'node-extra' },\n              [\n                h(\n                  'span',\n                  { class: 'device-count' },\n                  [\n                    h('span', { class: 'online-count' }, data.online || 0),\n                    ' / ',\n                    h('span', { class: 'total-count' }, data.total || 0),\n                  ]\n                )\n              ]\n            )\n            : null,\n        ]\n\n      );\n    },\n\n    // =================== 素材与预览相关方法 ===================\n    uploadMaterial (device) {\n      this.uploadFileList = [];\n      this.isUploadDialogVisible = true;\n    },\n\n    handleUploadSuccess (fileInfo) {\n      // 文件上传成功后，我们认为它已经是一个新的素材了\n      // 此时，我们应该将这个新素材与当前设备关联\n      // 1. (模拟)调用接口将文件信息保存为新素材\n      console.log(`上传文件 ${fileInfo.name} 成功`);\n\n      const newMaterial = {\n        id: `mat-${new Date().getTime()}`, // 模拟一个新ID\n        name: fileInfo.name,\n        thumbnail: fileInfo.url.startsWith('http') ? fileInfo.url : `https://via.placeholder.com/100`, // 模拟缩略图\n        url: fileInfo.url,\n        type: this.getFileType(fileInfo.name),\n        size: 'N/A'\n      };\n\n      // 2. (模拟)调用接口将设备与新素材关联\n      console.log(`将设备 ${this.selectedDevice.id} 与新上传的素材 ${newMaterial.id} 关联`);\n      this.$message.success(`上传成功并已关联到当前设备`);\n\n      // 3. 更新前端UI\n      this.selectedDevice.material = newMaterial;\n\n      // 4. 关闭上传弹窗并刷新素材列表\n      this.isUploadDialogVisible = false;\n      this.fetchMaterials(); // 刷新素材列表，以便在“更换素材”中看到\n    },\n\n    getFileType (fileName) {\n      const ext = fileName.split('.').pop().toLowerCase();\n      if (['jpg', 'jpeg', 'png', 'gif'].includes(ext)) return '图片';\n      if (['mp4', 'mov', 'avi'].includes(ext)) return '视频';\n      return '文件';\n    },\n\n    handleUploadStatus (status) {\n      this.uploading = status;\n    },\n\n    replaceMaterial (device) {\n      this.selectedMaterialId = device.material ? device.material.id : null;\n      this.isMaterialDialogVisible = true;\n      this.fetchMaterials();\n    },\n\n    fetchMaterials () {\n      this.isMaterialLoading = true;\n      // 假设 getMaterialList 返回素材列表\n      getMaterialList({ page: 1, limit: 1000 }).then(res => {\n        if (res.code === 0) {\n          // 这里假设API返回的数据结构，并为缩略图提供一个占位符\n          this.materials = (res.data.list || res.data).map(item => ({\n            ...item,\n            id: item.id, // 确保id存在\n            label: item.label || item.name, // 兼容不同字段\n            thumbnail: item.thumbnail || 'https://via.placeholder.com/100' // 使用占位图\n          }));\n        } else {\n          this.$message.error('获取素材列表失败');\n        }\n      }).catch(() => {\n        this.$message.error('获取素材列表失败');\n      }).finally(() => {\n        this.isMaterialLoading = false;\n      });\n    },\n\n    confirmReplaceMaterial () {\n      if (!this.selectedMaterialId) {\n        this.$message.warning('请选择一个素材');\n        return;\n      }\n      // TODO: 调用API将设备与素材关联\n      const selectedMat = this.materials.find(m => m.id === this.selectedMaterialId);\n      console.log(`将设备 ${this.selectedDevice.id} 与素材 ${this.selectedMaterialId} 关联`);\n      this.$message.success(`操作成功，设备已关联素材: ${selectedMat.label}`);\n\n      // 前端模拟更新\n      if (!this.selectedDevice.material) this.selectedDevice.material = {};\n      this.selectedDevice.material.name = selectedMat.label;\n      this.selectedDevice.material.id = selectedMat.id;\n      this.selectedDevice.material.thumbnail = selectedMat.thumbnail;\n      this.selectedDevice.material.type = selectedMat.type; // 假设有类型\n      this.selectedDevice.material.size = selectedMat.size; // 假设有大小\n\n      this.isMaterialDialogVisible = false;\n    },\n\n    previewDevice (device) {\n      this.isPreviewDialogVisible = true;\n      this.isRtcConnecting = true;\n      this.$nextTick(() => {\n        this.initWebRTC(device);\n      });\n    },\n\n    initWebRTC (device) {\n      console.log(`[WebRTC] 开始为设备 [${device.label}] 初始化连接...`);\n      // 模拟信令服务器配置\n      const configuration = { iceServers: [{ urls: 'stun:stun.l.google.com:19302' }] };\n      this.rtcPeerConnection = new RTCPeerConnection(configuration);\n\n      this.rtcPeerConnection.onicecandidate = event => {\n        if (event.candidate) {\n          console.log('[WebRTC] 发现新的 ICE 候选:', event.candidate);\n          // TODO: 将 candidate 发送到信令服务器\n        }\n      };\n\n      this.rtcPeerConnection.ontrack = event => {\n        console.log('[WebRTC] 接收到远程视频流');\n        this.isRtcConnecting = false;\n        if (this.$refs.previewVideo) {\n          this.$refs.previewVideo.srcObject = event.streams[0];\n          this.previewStream = event.streams[0];\n        }\n      };\n\n      // 模拟从信令服务器接收 Offer 并创建 Answer\n      this.createMockOfferAndAnswer();\n    },\n\n    createMockOfferAndAnswer () {\n      console.log('[WebRTC] 正在创建模拟 Offer...');\n      this.rtcPeerConnection.createOffer({\n        offerToReceiveVideo: true,\n      }).then(offer => {\n        console.log('[WebRTC] 模拟 Offer 创建成功:', offer);\n        return this.rtcPeerConnection.setLocalDescription(offer);\n      }).then(() => {\n        console.log('[WebRTC] 模拟: LocalDescription 设置成功，等待远端 Answer...');\n        // 在真实场景中，这里会把 offer 发送给信令服务器\n        // 然后等待远端（设备端）的 Answer\n        // 这里我们模拟一个延迟后收到了 Answer\n        setTimeout(() => {\n          const mockAnswer = { type: 'answer', sdp: '...' }; // 这是一个无效的SDP，仅作演示\n          console.log('[WebRTC] 模拟: 收到远端 Answer:', mockAnswer);\n          // this.rtcPeerConnection.setRemoteDescription(new RTCSessionDescription(mockAnswer));\n          console.warn('[WebRTC] 模拟: setRemoteDescription 已被注释，因为没有真实的Answer。在真实场景中需要取消注释。');\n\n          // 由于没有真实的流，我们将在几秒后显示连接失败\n          setTimeout(() => {\n            if (this.isRtcConnecting) {\n              this.$message.error('实时预览连接超时（模拟）');\n              this.isRtcConnecting = false;\n            }\n          }, 5000);\n\n        }, 2000);\n      }).catch(e => {\n        console.error('[WebRTC] Offer 创建失败:', e);\n        this.$message.error('WebRTC Offer 创建失败');\n        this.isRtcConnecting = false;\n      });\n    },\n\n    closePreviewDialog () {\n      console.log('[WebRTC] 关闭预览窗口，清理资源...');\n      if (this.rtcPeerConnection) {\n        this.rtcPeerConnection.close();\n        this.rtcPeerConnection = null;\n      }\n      if (this.previewStream) {\n        this.previewStream.getTracks().forEach(track => track.stop());\n        this.previewStream = null;\n      }\n      this.isPreviewDialogVisible = false;\n      this.isRtcConnecting = false;\n    },\n\n    editMaterial (material) {\n      // 深拷贝一份，避免直接修改\n      this.editingMaterial = JSON.parse(JSON.stringify(material));\n      this.isEditMaterialDialogVisible = true;\n    },\n\n    saveMaterial () {\n      this.$refs.materialForm.validate((valid) => {\n        if (valid) {\n          // 调用API保存\n          editMaterial(this.editingMaterial, this.editingMaterial.id).then(() => {\n            this.$message.success('素材信息更新成功');\n            // 更新selectedDevice中的素材信息\n            this.selectedDevice.material = { ...this.editingMaterial };\n            this.isEditMaterialDialogVisible = false;\n            // 同时刷新素材列表，以保证数据一致性\n            this.fetchMaterials();\n          }).catch(() => {\n            this.$message.error('素材信息更新失败');\n          });\n        }\n      });\n    },\n\n    unbindMaterial (device) {\n      this.$confirm(`确定要解除设备 [${device.label}] 与其素材的关联吗?`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // TODO: 调用API解除关联\n        console.log(`调用API解除设备 ${device.id} 的素材关联`);\n        this.$message.success('解除关联成功');\n        // 前端模拟更新\n        this.selectedDevice.material = null;\n      }).catch(() => { });\n    },\n\n    // =================== 分组管理相关方法 ===================\n    addGroupDialog () {\n      this.isEditGroup = false;\n      this.groupForm = {\n        id: null,\n        name: '',\n        description: '',\n      };\n      this.isGroupDialogVisible = true;\n    },\n\n    editGroupDialog (group) {\n      this.isEditGroup = true;\n      this.groupForm = {\n        id: group.id,\n        name: group.label,\n        description: group.description || '',\n      };\n      this.isGroupDialogVisible = true;\n    },\n\n    saveGroup () {\n      this.$refs.groupForm.validate((valid) => {\n        if (valid) {\n          const api = this.isEditGroup ? editGroup : addGroup;\n          const params = {\n            name: this.groupForm.name,\n            description: this.groupForm.description,\n          };\n\n          if (this.isEditGroup) {\n            params.id = this.groupForm.id;\n          }\n\n          api(params).then(() => {\n            this.$message.success(\n              this.isEditGroup\n                ? this.$t('equipmentCenter.dialog.message.groupEditSuccess')\n                : this.$t('equipmentCenter.dialog.message.groupAddSuccess')\n            );\n            this.isGroupDialogVisible = false;\n            this.getList(); // 刷新设备树\n          }).catch(() => {\n            this.$message.error('操作失败，请稍后重试');\n          });\n        }\n      });\n    },\n\n    deleteGroupDialog (group) {\n      if (group.group_id == 1) {\n        this.$message.error(this.$t('equipmentCenter.dialog.message.defaultGroupDelete'));\n        return;\n      }\n      if (group.children?.length > 0) {\n        this.$message.error('请先删除分组下的所有设备');\n        return;\n      }\n\n      this.$confirm(\n        this.$t('equipmentCenter.dialog.message.deleteGroupWarning'),\n        this.$t('equipmentCenter.dialog.message.deleteGroupConfirm'),\n        {\n          confirmButtonText: this.$t('public.confirm'),\n          cancelButtonText: this.$t('public.cancel'),\n          type: 'warning'\n        }\n      ).then(() => {\n        deleteGroup(group.group_id).then(() => {\n          this.$message.success(this.$t('equipmentCenter.dialog.message.groupDeleteSuccess'));\n          this.selectedDevice = null; // 清空选中状态\n          this.getList(); // 刷新设备树\n        }).catch(() => {\n          this.$message.error('删除失败，请稍后重试');\n        });\n      }).catch(() => {\n        // 用户取消\n      });\n    },\n\n    closeGroupDialog () {\n      this.$refs.groupForm.resetFields();\n      this.groupForm = {\n        id: null,\n        name: '',\n        description: '',\n      };\n    },\n\n    // =================== 原有方法 ===================\n    editDevice (device) {\n      if (!device || !device.isClient) {\n        this.$message.warning('无效的设备');\n        return;\n      }\n      this.deviceInfo = { ...device };\n      this.isShow = true;\n      this.fetchGroupNameList();\n    },\n\n    fetchGroupNameList () {\n      getGroupNameList().then(res => {\n        if (res.code == 0) {\n          this.groupNameList = res.data\n        }\n      })\n    },\n\n    deleteDevice (device) {\n      if (!device || !device.isClient) {\n        this.$message.warning('无效的设备');\n        return;\n      }\n      this.$confirm(`确定删除设备: ${device.label}?`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        del(device.id).then(() => {\n          this.$message({ type: 'success', message: '删除成功' });\n          if (this.selectedDevice && this.selectedDevice.id === device.id) {\n            this.selectedDevice = null;\n          }\n          this.getList(); // 刷新列表\n        }).catch(() => {\n          this.$message({ type: 'error', message: '删除失败' });\n        });\n      }).catch(() => {\n        // 用户取消\n      });\n    },\n\n    saveDevice () {\n      this.$refs.deviceInfoForm.validate((valid) => {\n        if (valid) {\n          edit(this.deviceInfo).then(() => {\n            this.$message({ type: 'success', message: '保存成功' });\n            this.isShow = false;\n            this.getList(); // 刷新整个树以保证数据一致性\n          }).catch(() => {\n            this.$message({ type: 'error', message: '保存失败' });\n          });\n        }\n      });\n    },\n\n    closeDialog () {\n      this.$refs.deviceInfoForm.resetFields();\n      this.deviceInfo = { mac_address: '', alias_name: '', group_name: '' };\n    },\n\n    handleSelectionChange (val) {\n      this.multipleSelection = val;\n    },\n\n    batchPush () {\n      if (this.multipleSelection.length === 0) {\n        this.$message.warning('请至少选择一个设备');\n        return;\n      }\n      const deviceNames = this.multipleSelection.map(item => item.label).join(', ');\n      this.$message.success(`准备向以下设备进行批量推送: ${deviceNames}`);\n      console.log('批量推送的设备:', this.multipleSelection);\n      // 此处可添加调用API的逻辑\n    },\n\n    formatTime (timestamp) {\n      if (!timestamp) return '-';\n      return this.$formatTimeStamp ? this.$formatTimeStamp(timestamp) : new Date(timestamp).toLocaleString();\n    },\n\n    handleNodeExpand (data, node) {\n      const expandedGroups = this.$refs.tree.store.nodesMap;\n      const expandedGroupNames = [];\n      for (const key in expandedGroups) {\n        if (expandedGroups[key].expanded) {\n          expandedGroupNames.push(expandedGroups[key].data.label);\n        }\n      }\n      // 路由可以设计的更复杂，例如 /EquipmentCenter/group1,group2/deviceMac\n      // 这里为了简单，只记录当前操作展开的分组\n      if (this.$route.params.groupName !== data.label) {\n        this.$router.push({ name: 'EquipmentCenter', params: { groupName: data.label } });\n      }\n    },\n\n    startResize (e) {\n      this.isResizing = true;\n      const startX = e.clientX;\n      const startWidth = this.treeWidth;\n      const handleMouseMove = (ev) => {\n        if (!this.isResizing) return;\n        const deltaX = ev.clientX - startX;\n        const newWidth = startWidth + deltaX;\n        if (newWidth >= 150 && newWidth <= 500) {\n          this.treeWidth = newWidth;\n        }\n      };\n      const handleMouseUp = () => {\n        this.isResizing = false;\n        document.removeEventListener('mousemove', handleMouseMove);\n        document.removeEventListener('mouseup', handleMouseUp);\n        document.body.style.cursor = '';\n        document.body.style.userSelect = '';\n      };\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n      document.body.style.cursor = 'col-resize';\n      document.body.style.userSelect = 'none';\n    },\n\n    handleBatchUpload () {\n      if (!this.selectedDevice || this.selectedDevice.isClient) {\n        this.$message.warning('请先选择一个设备分组');\n        return;\n      }\n      const input = document.createElement('input');\n      input.type = 'file';\n      input.webkitdirectory = true;\n      input.onchange = e => {\n        const files = e.target.files;\n        if (files.length === 0) {\n          return;\n        }\n        this.uploadFolder(files);\n      };\n      input.click();\n    },\n\n    async uploadFolder (files) {\n      const formData = new FormData();\n      formData.append('groupId', this.selectedDevice.group_id);\n\n      for (let i = 0; i < files.length; i++) {\n        formData.append('files', files[i]);\n      }\n\n      this.isLoading = true;\n      try {\n        const res = await batchUploadFiles(formData);\n        if (res.code === 0) {\n          this.$message.success('文件上传成功，正在处理分配...');\n          this.getList(); // 刷新列表\n        } else {\n          this.$message.error(res.msg || '上传失败');\n        }\n      } catch (error) {\n        this.$message.error('上传过程中发生错误');\n      } finally {\n        this.isLoading = false;\n      }\n    },\n  },\n};\n</script>\n\n<style>\n.custom-tree-node {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: 14px;\n  padding: 6px 12px;\n  border-radius: 6px;\n  transition: all 0.2s ease;\n  margin: 1px 0;\n}\n\n\n.custom-tree-node:hover {\n  background-color: #f0f2f5;\n}\n\n.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {\n  background-color: #d9ecff;\n}\n\n.node-content {\n  display: flex;\n  align-items: center;\n  flex: 1;\n  overflow: hidden;\n}\n\n.node-icon {\n  margin-right: 10px;\n  font-size: 16px;\n  color: #606266;\n}\n\n.node-icon.online-icon {\n  color: #67c23a;\n}\n\n.node-icon.offline-icon {\n  color: #f56c6c;\n}\n\n.node-label {\n  margin-right: 8px;\n  color: #303133;\n  font-weight: 500;\n  font-size: 13px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.status-tag {\n  margin-left: auto;\n  flex-shrink: 0;\n}\n\n.node-extra {\n  display: flex;\n  align-items: center;\n  margin-left: 10px;\n  flex-shrink: 0;\n}\n\n.device-count {\n  font-size: 11px;\n  color: #909399;\n  background-color: #f4f4f5;\n  padding: 3px 8px;\n  border-radius: 12px;\n  border: 1px solid #e4e7ed;\n  font-weight: 500;\n}\n\n.online-count {\n  color: #67c23a;\n  font-weight: 600;\n}\n\n.total-count {\n  color: #606266;\n}\n\n\n\n.image-slot {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  height: 100%;\n  background: #f5f7fa;\n  color: #909399;\n  font-size: 30px;\n}\n</style>\n\n<style scoped>\n.equipment-center {\n  padding: 8px;\n  background-color: #f5f7fa;\n  min-height: calc(100vh - 100px);\n}\n\n.equipment-container {\n  height: calc(100vh - 120px);\n  background-color: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  display: flex;\n  position: relative;\n  overflow: hidden;\n}\n\n.tree-panel {\n  height: 100%;\n  flex-shrink: 0;\n  position: relative;\n  background-color: #fafbfc;\n  border-right: 1px solid #e4e7ed;\n}\n\n.resize-handle {\n  width: 8px;\n  height: 100%;\n  background-color: transparent;\n  cursor: col-resize;\n  position: absolute;\n  right: -4px;\n  top: 0;\n  z-index: 10;\n}\n\n.tree-card {\n  height: 100%;\n  border: none;\n  background-color: transparent;\n}\n\n.tree-card .el-card__header {\n  padding: 16px 20px;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.tree-card .el-card__body {\n  padding: 10px;\n  height: calc(100% - 65px);\n  display: flex;\n  flex-direction: column;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: #303133;\n}\n\n.search-input {\n  margin-bottom: 8px;\n  flex-shrink: 0;\n}\n\n.equipment-tree {\n  background-color: transparent;\n  flex-grow: 1;\n  height: calc(100vh - 250px);\n  overflow-y: auto;\n}\n\n.detail-panel {\n  height: calc(100vh - 250px);\n  flex: 1;\n  /* overflow-y: auto; */\n  padding: 16px;\n  background-color: #fff;\n}\n\n.empty-state {\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.empty-icon {\n  font-size: 48px;\n  color: #c0c4cc;\n}\n\n.info-card,\n.group-card {\n  border: 1px solid #e4e7ed;\n  border-radius: 8px;\n}\n\n.material-card {\n  margin-top: 16px;\n}\n\n.header-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.device-info,\n.group-info,\n.stats-info,\n.material-info {\n  padding: 20px;\n}\n\n.group-info {\n  border-bottom: 1px solid #e4e7ed;\n  margin-bottom: 0;\n}\n\n.material-details {\n  display: flex;\n  align-items: center;\n}\n\n.material-thumbnail {\n  width: 100px;\n  height: 100px;\n  border-radius: 6px;\n  margin-right: 20px;\n  object-fit: cover;\n  border: 1px solid #ebeef5;\n}\n\n.material-text {\n  flex-grow: 1;\n}\n\n.material-name {\n  font-size: 16px;\n  font-weight: 500;\n  color: #303133;\n  margin: 0 0 8px 0;\n}\n\n.material-meta {\n  font-size: 13px;\n  color: #909399;\n  margin: 0;\n}\n\n.material-actions {\n  margin-left: auto;\n}\n\n.danger-text {\n  color: #f56c6c;\n}\n\n.danger-text:hover {\n  color: #f78989;\n}\n\n.no-material {\n  text-align: center;\n  color: #909399;\n}\n\n.info-item {\n  margin-bottom: 16px;\n  display: flex;\n  align-items: center;\n  font-size: 14px;\n}\n\n.info-label {\n  font-weight: 500;\n  color: #606266;\n  margin-right: 16px;\n  min-width: 100px;\n}\n\n.info-value {\n  color: #303133;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 8px;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n}\n\n.stat-item.online {\n  border-color: #b3d8ff;\n}\n\n.stat-item.offline {\n  border-color: #fbc4c4;\n}\n\n.stat-number {\n  font-size: 28px;\n  font-weight: 700;\n  color: #303133;\n  margin-bottom: 8px;\n}\n\n.stat-item.online .stat-number {\n  color: #67c23a;\n}\n\n.stat-item.offline .stat-number {\n  color: #f56c6c;\n}\n\n.stat-label {\n  font-size: 13px;\n  color: #606266;\n}\n\n.preview-dialog-content {\n  position: relative;\n  min-height: 450px;\n}\n\n.preview-placeholder {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: rgba(0, 0, 0, 0.5);\n  color: #fff;\n}\n\n.preview-placeholder i {\n  font-size: 60px;\n  margin-bottom: 20px;\n}\n</style>", "import mod from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=11baf1ba&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=11baf1ba&prod&lang=css\"\nimport style1 from \"./index.vue?vue&type=style&index=1&id=11baf1ba&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"11baf1ba\",\n  null\n  \n)\n\nexport default component.exports", "import request, { postBlob, getBlob, handleImport } from '@/utils/request'\n\nexport function getList(params) {\n  return request({\n    url: `/admin/sourcematerial/getList`,\n    method: 'get',\n    params\n  })\n}\n\nexport function add(data) {\n  return request({\n    url: `/admin/sourcematerial/addSourceMaterial`,\n    method: 'post',\n    data\n  })\n}\n\nexport function edit(data,id) {\n  return request({\n    url: `/admin/sourcematerial/editSourceMaterial/` + id,\n    method: 'put',\n    data\n  })\n}\n\nexport function del(id) {\n  return request({\n    url: `/admin/sourcematerial/delete/` + id,\n    method: 'delete'\n  })\n}"], "names": ["render", "_vm", "this", "_c", "_self", "ref", "staticClass", "class", "name", "attrs", "upload_host", "headers", "uploadNum", "beforeUpload", "handleRemove", "handleUploadProgress", "handleUploadSuccess", "isDisabled", "acceptFileType", "_l", "fileList", "item", "index", "key", "on", "$event", "hover", "isImageUrl", "url", "refInFor", "staticStyle", "imageUrl", "previewImages", "stopPropagation", "handleClickItem", "apply", "arguments", "isVideoUrl", "getFileIconClass", "_v", "_s", "getFileNameFromUrl", "directives", "rawName", "value", "expression", "canPreview", "previewFile", "downloadFile", "_e", "uploadImage", "deleteImage", "length", "staticRenderFns", "props", "Boolean", "fileType", "String", "type", "default", "Array", "Number", "data", "process", "Authorization", "store", "getters", "token", "showImage", "dialogVisible", "uploadImageId", "list", "computed", "showFileList", "get", "set", "newValue", "methods", "emitInput", "val", "$emit", "setTimeout", "domImageMask", "document", "querySelector", "addEventListener", "e", "target", "parentNode", "className", "click", "file", "getUUID", "replace", "c", "Math", "random", "toString", "isValidType", "checkFileType", "handleInvalidType", "push", "$message", "warning", "$i18n", "t", "count", "resetUploadState", "includes", "reader", "FileReader", "onload", "txt", "result", "img", "createElement", "src", "width", "height", "console", "log", "readAsDataURL", "videoElement", "URL", "createObjectURL", "videoWidth", "videoHeight", "allowedTypes", "split", "map", "trim", "warningMsg", "getWarningMessage", "$refs", "uploadRef", "clearFiles", "res", "response", "file_name", "images", "for<PERSON>ach", "fullUrl", "previewImage", "findElem", "showViewer", "previewVideo", "window", "open", "fileSuffix", "substring", "lastIndexOf", "toLowerCase", "whiteList", "videoSuffix", "suffix", "ext", "pop", "id", "$el", "splice", "submitUpload", "submit", "component", "style", "treeWidth", "slot", "$t", "addGroupDialog", "model", "filterText", "callback", "$$v", "equipmentData", "defaultProps", "filterNode", "renderContent", "handleNodeClick", "handleNodeExpand", "startResize", "selected<PERSON><PERSON><PERSON>", "isClient", "editDevice", "deleteDevice", "alias_name", "mac_address", "group_name", "isOnline", "ip_addr", "formatTime", "updated_at", "uploadMaterial", "replaceMaterial", "previewDevice", "material", "thumbnail", "size", "editMaterial", "unbindMaterial", "label", "editGroupDialog", "deleteGroupDialog", "handleBatchUpload", "multipleSelection", "batchPush", "total", "online", "children", "handleSelectionChange", "scopedSlots", "_u", "fn", "scope", "row", "proxy", "isShow", "closeDialog", "deviceInfo", "rules", "$set", "groupNameList", "saveDevice", "isMaterialDialogVisible", "isMaterialLoading", "materials", "nativeOn", "selectedMaterialId", "confirmReplaceMaterial", "isUploadDialogVisible", "uploadFileList", "handleUploadStatus", "isPreviewDialogVisible", "closePreviewDialog", "isRtcConnecting", "previewStream", "isEditMaterialDialogVisible", "editingMaterial", "materialRules", "saveMaterial", "isEditGroup", "isGroupDialogVisible", "closeGroupDialog", "groupForm", "groupRules", "description", "saveGroup", "getTreeList", "params", "request", "method", "edit", "del", "getGroupNameList", "addGroup", "editGroup", "deleteGroup", "batchUploadFiles", "components", "FileUpload", "watch", "tree", "filter", "handler", "deep", "isResizing", "required", "message", "trigger", "isLoading", "uploading", "rtcPeerConnection", "created", "getList", "then", "code", "handleRouteChange", "finally", "$nextTick", "groupName", "<PERSON><PERSON><PERSON><PERSON>", "$route", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "deviceNode", "findNodeByMac", "findNode", "n", "some", "nodesMap", "expand", "groupNode", "findNodeByLabel", "findNodeById", "node", "found", "predicate", "mac", "parent", "undefined", "newParams", "$router", "expanded", "collapse", "lowerCaseValue", "indexOf", "h", "device", "fileInfo", "newMaterial", "Date", "getTime", "startsWith", "getFileType", "success", "fetchMaterials", "fileName", "status", "getMaterialList", "page", "limit", "error", "catch", "selectedMat", "find", "m", "initWebRTC", "configuration", "iceServers", "urls", "RTCPeerConnection", "onicecandidate", "event", "candidate", "ontrack", "srcObject", "streams", "createMockOfferAndAnswer", "createOffer", "offerToReceiveVideo", "offer", "setLocalDescription", "mockAnswer", "sdp", "warn", "close", "getTracks", "track", "stop", "JSON", "parse", "stringify", "materialForm", "validate", "valid", "$confirm", "confirmButtonText", "cancelButtonText", "group", "api", "group_id", "resetFields", "fetchGroupNameList", "deviceInfoForm", "deviceNames", "join", "timestamp", "$formatTimeStamp", "toLocaleString", "expandedGroups", "expandedGroupNames", "startX", "clientX", "startWidth", "handleMouseMove", "ev", "deltaX", "newWidth", "handleMouseUp", "removeEventListener", "body", "cursor", "userSelect", "input", "webkitdirectory", "onchange", "files", "uploadFolder", "formData", "FormData", "append", "i", "msg", "add"], "sourceRoot": ""}