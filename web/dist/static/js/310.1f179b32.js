"use strict";(self["webpackChunkesop_dashboard"]=self["webpackChunkesop_dashboard"]||[]).push([[310],{9310:function(t,e,a){a.r(e),a.d(e,{default:function(){return h}});var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"account-container"},[e("el-form",{ref:"form",staticClass:"demo-ruleForm",attrs:{model:t.form,"label-width":"80px","label-position":"left"}},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:4}},[e("el-form-item",{attrs:{label:t.$t("account.form.name"),prop:"name"}},[e("el-input",{attrs:{placeholder:t.$t("account.form.name")},model:{value:t.form.name,callback:function(e){t.$set(t.form,"name",e)},expression:"form.name"}})],1)],1),e("el-col",{attrs:{span:4}},[e("el-form-item",{attrs:{label:t.$t("account.form.createTime"),prop:"date"}},[e("el-date-picker",{attrs:{"value-format":"timestamp",type:"daterange","range-separator":t.$t("public.to"),"start-placeholder":t.$t("public.startDate"),"end-placeholder":t.$t("public.endDate")},model:{value:t.form.date,callback:function(e){t.$set(t.form,"date",e)},expression:"form.date"}})],1)],1)],1),e("el-row",{attrs:{type:"flex",justify:"space-between"}},[e("el-button",{attrs:{type:"primary",icon:"el-icon-plus",size:"mini"},on:{click:function(e){return t.add()}}},[t._v(t._s(t.$t("account.button.addAccount")))]),e("el-col",{attrs:{span:4}},[e("el-button",{attrs:{size:"mini"},on:{click:function(e){return t.resetForm("form","getList")}}},[t._v(t._s(t.$t("public.reset")))]),e("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.searchForm()}}},[t._v(t._s(t.$t("public.search")))])],1)],1)],1),e("div",{staticStyle:{height:"60vh","background-color":"#ccc",margin:"10px 0"}},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:"",height:"100%"}},[e("el-table-column",{attrs:{prop:"num",label:t.$t("account.table.num"),width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),e("el-table-column",{attrs:{prop:"name",label:t.$t("account.table.name"),width:"180",align:"center"}}),e("el-table-column",{attrs:{prop:"account",label:t.$t("account.table.account"),align:"center"}}),e("el-table-column",{attrs:{prop:"created_at",label:t.$t("account.table.created_at"),align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.$formatTimeStamp(e.row.created_at))+" ")]}}])}),e("el-table-column",{attrs:{label:t.$t("public.operation"),fixed:"right",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.edit(a.row)}}},[t._v(t._s(t.$t("public.edit")))]),e("el-popconfirm",{staticStyle:{"margin-left":"10px"},attrs:{title:t.$t("account.table.confirmDelete")},on:{confirm:function(e){return t.del(a.row.id)}}},[e("el-button",{staticStyle:{color:"#ff0000"},attrs:{slot:"reference",type:"text",size:"small"},slot:"reference"},[t._v(t._s(t.$t("public.delete")))])],1)]}}])})],1)],1),e("el-row",{attrs:{gutter:20,type:"flex",justify:"end"}},[e("el-pagination",{attrs:{background:"","current-page":t.pageNum,"page-sizes":[10,20,50],"page-size":t.pageSize,layout:"total, prev, pager, next",total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange,"update:currentPage":function(e){t.pageNum=e},"update:current-page":function(e){t.pageNum=e}}})],1),e("el-dialog",{attrs:{title:t.isEdit?t.$t("account.dialog.title.edit"):t.$t("account.dialog.title.add"),visible:t.isShow,width:"30%"},on:{"update:visible":function(e){t.isShow=e},close:t.close}},[e("el-form",{ref:"addForm",staticClass:"demo-ruleForm",attrs:{model:t.addForm,rules:t.rules,"label-width":"80px","label-position":"left"}},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:t.$t("account.form.name"),prop:"name",required:""}},[e("el-input",{attrs:{placeholder:t.$t("account.form.namePlaceholder")},model:{value:t.addForm.name,callback:function(e){t.$set(t.addForm,"name",e)},expression:"addForm.name"}})],1)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:t.$t("account.form.account"),prop:"account",required:""}},[e("el-input",{attrs:{placeholder:t.$t("account.form.accountPlaceholder")},model:{value:t.addForm.account,callback:function(e){t.$set(t.addForm,"account",e)},expression:"addForm.account"}})],1)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:t.$t("account.form.password"),prop:"password"}},[e("el-input",{attrs:{placeholder:t.$t("account.form.passwordPlaceholder")},model:{value:t.addForm.password,callback:function(e){t.$set(t.addForm,"password",e)},expression:"addForm.password"}})],1)],1)],1)],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.isShow=!1}}},[t._v(t._s(t.$t("public.cancel")))]),e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.save()}}},[t._v(t._s(t.$t("public.confirm")))])],1)],1)],1)},r=[],o=a(7120);function i(t){return(0,o.Ay)({url:"/admin/admin/getList",method:"get",params:t})}function n(t){return(0,o.Ay)({url:"/admin/admin/addAdmin",method:"post",data:t})}function l(t,e){return(0,o.Ay)({url:"/admin/admin/editAdmin/"+e,method:"put",data:t})}function c(t){return(0,o.Ay)({url:"/admin/admin/delete/"+t,method:"delete"})}var d={data(){return{dataList:[],pageNum:1,pageSize:10,total:0,form:{name:"",date:[]},addForm:{name:"",password:"",account:""},id:"",rules:{name:[{required:!0,message:this.$i18n.t("account.form.namePlaceholder"),trigger:"blur"}],account:[{required:!0,message:this.$i18n.t("account.form.accountPlaceholder"),trigger:"blur"}]},isShow:!1,isEdit:!1,isLoading:!1}},created(){this.getList()},methods:{getList(){this.isLoading=!0,i({page:this.pageNum,pageSize:this.pageSize,name:this.form.name,created_at_start:this.form.date.length>0?this.form.date[0]/1e3:"",created_at_end:this.form.date.length>0?this.form.date[1]/1e3:""}).then((t=>{0==t.code&&(this.dataList=t.data.data,this.total=t.data.total,this.isLoading=!1)}))},add(){this.isShow=!0},edit(t){this.id=t.id,this.addForm.name=t.name,this.addForm.account=t.account,this.addForm.password=t.password,this.isEdit=!0,this.isShow=!0},save(){this.$refs.addForm.validate((t=>{t&&(this.isEdit?l({account:this.addForm.account,password:this.addForm.password,name:this.addForm.name},this.id).then((t=>{this.$message({type:"success",message:this.$i18n.t("public.editSuccess")}),this.$refs.addForm.resetFields(),this.getList()})):n({account:this.addForm.account,password:this.addForm.password,name:this.addForm.name}).then((t=>{this.$message({type:"success",message:this.$i18n.t("public.addSuccess")}),this.getList()})),this.isShow=!1,this.getList())}))},del(t){c(t).then((t=>{this.$message({type:"success",message:this.$i18n.t("public.deleteSuccess")}),this.getList()}))},close(){},searchForm(){this.getList()},resetForm(){this.pageNum=1,this.$refs.form.resetFields(),this.getList()},handleSizeChange(t){this.pageNum=1,this.pageSize=t,this.getList()},handleCurrentChange(t){this.pageNum=t,this.getList()}}},u=d,m=a(1656),p=(0,m.A)(u,s,r,!1,null,"0ed9e980",null),h=p.exports}}]);
//# sourceMappingURL=310.1f179b32.js.map