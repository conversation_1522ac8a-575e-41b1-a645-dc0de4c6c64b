"use strict";(self["webpackChunkesop_dashboard"]=self["webpackChunkesop_dashboard"]||[]).push([[726],{3043:function(e,t,i){i.d(t,{A:function(){return p}});var a=function(){var e=this,t=e._self._c;return t("div",[t("el-upload",{ref:"uploadRef",staticClass:"avatar-uploader2",class:e.name,attrs:{action:e.upload_host,name:"uploadFile",headers:e.headers,multiple:e.uploadNum>1,"show-file-list":!1,"before-upload":e.beforeUpload,"on-remove":e.handleRemove,"on-progress":e.handleUploadProgress,"on-success":e.handleUploadSuccess,disabled:!e.isDisabled,accept:e.acceptFileType}}),t("div",{staticClass:"images"},[e._l(e.fileList,(function(i,a){return t("div",{key:a,staticClass:"item"},[t("div",{staticClass:"img",attrs:{id:"preview-item"},on:{mouseover:function(e){i.hover=!0},mouseout:function(e){i.hover=!1}}},[e.isImageUrl(i.url)?t("el-image",{ref:"img",refInFor:!0,staticStyle:{width:"120px",height:"120px"},attrs:{src:e.imageUrl+i.url,"preview-src-list":e.previewImages(),fit:"cover"},on:{click:function(t){return t.stopPropagation(),e.handleClickItem.apply(null,arguments)}}}):e.isVideoUrl(i.url)?t("video",{staticStyle:{width:"120px",height:"120px",display:"block"},attrs:{controls:""}},[t("source",{attrs:{src:e.imageUrl+i.url,type:"video/avi"}})]):t("div",{staticClass:"file-preview"},[t("i",{class:e.getFileIconClass(i.url)}),t("div",{staticClass:"file-name"},[e._v(e._s(i.name||e.getFileNameFromUrl(i.url)))])]),t("div",{directives:[{name:"show",rawName:"v-show",value:i.hover,expression:"item.hover"}],staticClass:"mask"},[e.canPreview(i.url)?t("i",{staticClass:"el-icon-zoom-in",on:{click:function(t){return e.previewFile(i)}}}):t("i",{staticClass:"el-icon-download",on:{click:function(t){return e.downloadFile(i.url)}}}),e.isDisabled?e._e():t("i",{staticClass:"el-icon-upload2",on:{click:function(t){return e.uploadImage(i.url)}}}),e.isDisabled?e._e():t("i",{staticClass:"el-icon-delete",on:{click:function(t){return e.deleteImage(i.url)}}})])],1)])})),e.fileList.length<e.uploadNum?t("div",{staticClass:"add",on:{click:function(t){return e.uploadImage()}}},[t("i",{staticClass:"el-icon-plus"})]):e._e()],2)],1)},s=[],l=(i(4114),i(4603),i(7566),i(8721),i(7120),i(7282)),r=i(1974),o={props:{isDisabled:Boolean,fileType:String,name:String,acceptFileType:{type:String,default:""},fileList:Array,uploadNum:{type:Number,default:1},index:{type:Number,default:0}},data(){return{upload_host:"/admin/sourcematerial/upload",imageUrl:"/assets/media/",headers:{Authorization:r.A.getters.token},showImage:"",dialogVisible:!1,uploadImageId:"",list:[]}},computed:{showFileList:{get:function(){return this.fileList&&this.fileList.length>0},set:function(e){}}},methods:{emitInput(e){this.$emit("input",e)},handleClickItem(){setTimeout((()=>{let e=document.querySelector(".el-image-viewer__wrapper");e&&e.addEventListener("click",(e=>{"el-image-viewer__actions__inner"!=e.target.parentNode.className&&document.querySelector(".el-image-viewer__close").click()}))}),300)},handleRemove(e,t){0===t.length?this.fileList=[]:this.fileList=t},getUUID(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(e=>("x"===e?16*Math.random()|0:8).toString(16)))},beforeUpload(e){const t=this.checkFileType(e);if(!t)return this.handleInvalidType(e),!1;if(this.list.push(e.name),this.list.length>this.uploadNum)return this.$message.warning(this.$i18n.t("upload.maxFileCount",{count:this.uploadNum})),this.resetUploadState(),!1;if(e.type.includes("image")){let t=new FileReader;t.onload=e=>{let t=e.target.result,i=document.createElement("img");i.src=t,i.onload=()=>{this.$emit("fileData",{width:i.width,height:i.height}),console.log("宽度：",i.width),console.log("高度：",i.height)}},t.readAsDataURL(e)}else if(e.type.includes("video")){console.log(e,"ffff");const t=document.createElement("video");t.src=URL.createObjectURL(e),t.addEventListener("loadedmetadata",(()=>{this.$emit("fileData",{width:t.videoWidth,height:t.videoHeight}),console.log("width, height:",t.videoWidth,t.videoHeight)}))}return!0},checkFileType(e){if(console.log(this.fileType),"image"===this.fileType)return e.type.includes("image");if("video"===this.fileType)return e.type.includes("video");if("image/video"===this.fileType)return e.type.includes("image")||e.type.includes("video");if("file"===this.fileType){const t=this.acceptFileType.split(",").map((e=>e.trim()));return t.includes(e.type)}return!0},handleInvalidType(e){const t=this.getWarningMessage();this.$message.warning(t),this.resetUploadState()},getWarningMessage(){switch(this.fileType){case"image":return this.$i18n.t("upload.onlyImage");case"video":return this.$i18n.t("upload.onlyVideo");case"image/video":return this.$i18n.t("upload.onlyVideoOrImageAgain");case"pdf":return this.$i18n.t("material.dialog.form.onlyPDF");case"file":return this.$i18n.t("material.dialog.form.onlyFile");default:return""}},resetUploadState(){this.$refs.uploadRef.clearFiles(),this.list=[],this.fileList=[...this.fileList]},handleUploadProgress(){this.$emit("uploadStatus",!0)},handleUploadSuccess(e,t,i){this.$emit("uploadStatus",!1),this.fileList.push({name:t.name,hover:!1,url:t.response.data.file_name}),this.$emit("editUrl",{name:t.name,hover:!1,url:t.response.data.file_name},this.name,this.index)},previewImages(){let e=[];return this.fileList&&this.fileList.length>0&&this.fileList.forEach((t=>{if(this.isImageUrl(t.url)){const i=this.imageUrl+t.url;e.push(i)}})),e},previewImage(e){let t=[];this.fileList.forEach((e=>{this.isImageUrl(e.url)&&t.push(e)}));let i=(0,l.NO)(t,"url",e);-1!==i&&this.$refs.img&&this.$refs.img[i]&&(this.$refs.img[i].showViewer=!0)},previewVideo(e){},previewFile(e){this.isImageUrl(e.url)?this.previewImage(e.url):this.isVideoUrl(e.url)?this.previewVideo(e.url):window.open(this.imageUrl+e.url,"_blank")},isImageUrl(e){if(!e)return!1;const t=e.substring(e.lastIndexOf(".")+1).toLowerCase(),i=["jpg","jpeg","png","gif","webp"];return i.includes(t)},isVideoUrl(e){if(!e)return!1;const t=["mp4","ogg","mov","webm"],i=e.substring(e.lastIndexOf(".")+1).toLowerCase();return t.includes(i)},getFileIconClass(e){if(!e)return"el-icon-document";const t=e.substring(e.lastIndexOf(".")+1).toLowerCase();switch(t){case"pdf":return"el-icon-document-pdf";case"doc":case"docx":return"el-icon-document";case"xls":case"xlsx":return"el-icon-document";case"ppt":case"pptx":return"el-icon-document";default:return"el-icon-document"}},getFileNameFromUrl(e){return e?e.split("/").pop():""},canPreview(e){return this.isImageUrl(e)||this.isVideoUrl(e)},downloadFile(e){window.open(this.imageUrl+e,"_blank")},uploadImage(e){this.list=[],this.uploadImageId=e,this.$refs.uploadRef&&this.$refs.uploadRef.$el.querySelector("input").click()},deleteImage(e){const t=(0,l.NO)(this.fileList,"url",e);-1!==t&&this.fileList.splice(t,1)},submitUpload(){this.$refs.uploadRef&&this.$refs.uploadRef.submit()}}},n=o,d=i(1656),c=(0,d.A)(n,a,s,!1,null,"84f8d436",null),p=c.exports},8726:function(e,t,i){i.r(t),i.d(t,{default:function(){return p}});var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"material-container"},[t("el-form",{ref:"form",staticClass:"demo-ruleForm",attrs:{model:e.form,"label-width":"80px","label-position":"left"}},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:4}},[t("el-form-item",{attrs:{label:e.$t("material.form.name"),prop:"name"}},[t("el-input",{attrs:{placeholder:e.$t("material.dialog.form.namePlaceholder")},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),t("el-col",{attrs:{span:4}},[t("el-form-item",{attrs:{label:e.$t("material.table.created_at"),prop:"date"}},[t("el-date-picker",{attrs:{"value-format":"timestamp",type:"daterange","range-separator":e.$t("public.to"),"start-placeholder":e.$t("public.startDate"),"end-placeholder":e.$t("public.endDate")},model:{value:e.form.date,callback:function(t){e.$set(e.form,"date",t)},expression:"form.date"}})],1)],1)],1),t("el-row",{attrs:{type:"flex",justify:"space-between"}},[t("el-button",{attrs:{type:"primary",icon:"el-icon-plus",size:"mini"},on:{click:function(t){return e.add()}}},[e._v(e._s(e.$t("material.button.add")))]),t("el-col",{attrs:{span:4}},[t("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.resetForm("form","getList")}}},[e._v(e._s(e.$t("public.reset")))]),t("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.searchForm()}}},[e._v(e._s(e.$t("public.search")))])],1)],1)],1),t("div",{staticStyle:{height:"60vh","background-color":"#ccc",margin:"10px 0"}},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],staticStyle:{width:"100%"},attrs:{data:e.dataList,border:"",height:"100%"}},[t("el-table-column",{attrs:{prop:"num",label:e.$t("material.table.num"),width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),t("el-table-column",{attrs:{prop:"name",label:e.$t("material.table.name"),width:"180",align:"center"}}),t("el-table-column",{attrs:{prop:"type",label:e.$t("material.table.type"),align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getTypeName(t.row.type))+" ")]}}])}),t("el-table-column",{attrs:{prop:"path",label:e.$t("material.table.preview"),align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[1==i.row.type&&i.row.path?t("el-image",{staticClass:"img",attrs:{src:e.imageUrl+i.row.path,"preview-src-list":[e.imageUrl+i.row.path],fit:"cover"}}):2==i.row.type&&i.row.path?t("video",{staticClass:"img",attrs:{src:e.imageUrl+i.row.path,controls:""}}):3==i.row.type&&i.row.path?t("div",{staticClass:"file-preview"},[t("i",{class:e.getFileIconClass(i.row.path)}),t("div",{staticClass:"file-name"},[e._v(e._s(e.getFileName(i.row.path)))])]):t("div",[e._v("--")])]}}])}),t("el-table-column",{attrs:{prop:"created_at",label:e.$t("material.table.created_at"),align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.$formatTimeStamp(t.row.created_at))+" ")]}}])}),t("el-table-column",{attrs:{label:e.$t("material.table.operation"),fixed:"right",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.edit(i.row)}}},[e._v(e._s(e.$t("public.edit")))]),t("el-popconfirm",{staticStyle:{"margin-left":"10px"},attrs:{title:e.$t("material.table.sureToDelete")},on:{confirm:function(t){return e.del(i.row.id)}}},[t("el-button",{staticStyle:{color:"#ff0000"},attrs:{slot:"reference",type:"text",size:"small"},slot:"reference"},[e._v(e._s(e.$t("public.delete")))])],1)]}}])})],1)],1),t("el-row",{attrs:{gutter:20,type:"flex",justify:"end"}},[t("el-pagination",{attrs:{background:"","current-page":e.pageNum,"page-sizes":[10,20,50],"page-size":e.pageSize,layout:"total, prev, pager, next",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange,"update:currentPage":function(t){e.pageNum=t},"update:current-page":function(t){e.pageNum=t}}})],1),t("el-dialog",{attrs:{title:e.isEdit?e.$t("material.dialog.title.editMaterial"):e.$t("material.dialog.title.addMaterial"),visible:e.isShow,width:"600px","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":!1},on:{"update:visible":function(t){e.isShow=t},close:e.close}},[t("el-form",{ref:"addForm",staticClass:"demo-ruleForm",attrs:{model:e.addForm,rules:e.rules,"label-width":"80px","label-position":"left"}},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:15}},[t("el-form-item",{attrs:{label:e.$t("material.dialog.form.name"),prop:"name"}},[t("el-input",{attrs:{placeholder:e.$t("material.dialog.form.namePlaceholder")},model:{value:e.addForm.name,callback:function(t){e.$set(e.addForm,"name",t)},expression:"addForm.name"}})],1)],1),t("el-col",{attrs:{span:15}},[t("el-form-item",{attrs:{label:e.$t("material.dialog.form.type"),prop:"type",required:""}},[t("el-radio-group",{attrs:{direction:"vertical"},on:{change:e.changeType},model:{value:e.addForm.type,callback:function(t){e.$set(e.addForm,"type",t)},expression:"addForm.type"}},[t("el-radio",{attrs:{label:1}},[e._v(e._s(e.$t("material.table.image")))]),t("el-radio",{attrs:{label:2}},[e._v(e._s(e.$t("material.table.video")))]),t("el-radio",{attrs:{label:3}},[e._v(e._s(e.$t("material.table.file")))])],1)],1)],1),t("el-col",{attrs:{span:24}},[t("el-form-item",{attrs:{label:e.$t("material.dialog.form.upload"),prop:"path"}},[t("fileUpload",{attrs:{uploadNum:1,name:"path",fileType:e.getFileType(e.addForm.type),acceptFileType:e.acceptFileType,fileList:e.fileList},on:{uploadStatus:e.uploadStatus,editUrl:e.editUrl,fileData:e.fileData}})],1)],1)],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{loading:e.uploading},on:{click:function(t){e.isShow=!1}}},[e._v(e._s(e.$t("public.cancel")))]),t("el-button",{attrs:{type:"primary",loading:e.uploading},on:{click:function(t){return e.save()}}},[e._v(e._s(e.$t("public.confirm")))])],1)],1)],1)},s=[],l=i(4759),r=i(3043),o={components:{fileUpload:r.A},data(){return{uploading:!1,acceptFileType:"image/*",dataList:[],pageNum:1,pageSize:10,total:0,form:{name:"",date:[],type:""},addForm:{name:"",type:1,path:""},fileList:[],upfileData:{width:0,height:0},id:"",imageUrl:"/assets/media/",rules:{name:[{required:!0,message:this.$i18n.t("material.dialog.form.namePlaceholder"),trigger:"blur"}],path:[{required:!0,message:this.$i18n.t("material.dialog.form.pathPlaceholder"),trigger:"change"}]},isShow:!1,isEdit:!1,isLoading:!1}},created(){this.getList()},methods:{fileData(e){console.log(e,"upfileData"),this.upfileData=e},uploadStatus(e){this.uploading=e},changeType(e){switch(e){case 1:this.acceptFileType="image/*";break;case 2:this.acceptFileType="video/mp4,video/ogg,video/mov,video/webm";break;case 3:this.acceptFileType="application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation";break;default:break}},getList(){this.isLoading=!0,(0,l.vS)({page:this.pageNum,pageSize:this.pageSize,name:this.form.name,type:this.form.type,created_at_start:this.form.date.length>0?this.form.date[0]/1e3:"",created_at_end:this.form.date.length>0?this.form.date[1]/1e3:""}).then((e=>{0==e.code&&(this.dataList=e.data.data,this.total=e.data.total,this.isLoading=!1)}))},add(){this.isShow=!0,this.isEdit=!1,this.addForm={name:"",type:1,path:""},this.fileList=[]},edit(e){this.fileList=[],this.id=e.id,this.addForm.name=e.name,this.addForm.type=e.type,this.addForm.path=e.path,this.isEdit=!0,this.isShow=!0,this.fileList=[{url:e.path,hover:!1,name:this.getFileName(e.path)}]},save(){this.$refs.addForm.validate((e=>{if(e){if(0===this.fileList.length)return void this.$message.error(this.$i18n.t("material.dialog.form.selectFile"));const e=this.getFileType(this.addForm.type),t=this.fileList[0].url;if("image"===e&&!this.isImageUrl(t))return void this.$message.error(this.$i18n.t("material.dialog.form.onlyImage"));if("video"===e&&!this.isVideoUrl(t))return void this.$message.error(this.$i18n.t("material.dialog.form.onlyVideo"));if("file"===e&&!this.isAllowedFileUrl(t))return void this.$message.error(this.$i18n.t("material.dialog.form.onlyFile"));const i={type:this.addForm.type,path:this.addForm.path,name:this.addForm.name,source_width:this.upfileData.width,source_height:this.upfileData.height};this.isEdit?(0,l.hc)(i,this.id).then((e=>{this.$message({type:"success",message:this.$i18n.t("public.editSuccess")}),this.$refs.addForm.resetFields(),this.isShow=!1,this.getList()})):(0,l.WQ)(i).then((e=>{this.$message({type:"success",message:this.$i18n.t("public.addSuccess")}),this.$refs.addForm.resetFields(),this.isShow=!1,this.getList()}))}}))},isImageUrl(e){if(!e)return!1;const t=e.split(".").pop().toLowerCase();return["jpg","jpeg","png","gif","bmp"].includes(t)},isVideoUrl(e){if(!e)return!1;const t=e.split(".").pop().toLowerCase();return["mp4","avi","mov","webm"].includes(t)},isAllowedFileUrl(e){if(!e)return!1;const t=e.split(".").pop().toLowerCase();return["pdf","doc","docx","xls","xlsx","ppt","pptx"].includes(t)},del(e){(0,l.yH)(e).then((e=>{this.$message({type:"success",message:this.$i18n.t("public.deleteSuccess")}),this.getList()}))},editUrl(e){this.fileList=[e],this.addForm.path=e.url},close(){this.uploading||(this.addForm={name:"",type:1,path:""},this.fileList=[])},searchForm(){this.getList()},resetForm(){this.pageNum=1,this.$refs.form.resetFields(),this.getList()},handleSizeChange(e){this.pageNum=1,this.pageSize=e,this.getList()},handleCurrentChange(e){this.pageNum=e,this.getList()},getTypeName(e){const t={1:this.$t("material.table.image"),2:this.$t("material.table.video"),3:this.$t("material.table.file")};return t[e]||"-"},getFileIconClass(e){if(!e)return"el-icon-document";const t=e.split(".").pop().toLowerCase();switch(t){case"pdf":return"el-icon-document-pdf";case"doc":case"docx":case"xls":case"xlsx":case"ppt":case"pptx":return"el-icon-document";case"jpg":case"jpeg":case"png":case"gif":case"bmp":return"el-icon-picture";case"mp4":case"avi":case"mov":case"webm":return"el-icon-video-camera";default:return"el-icon-document"}},getFileName(e){return e?e.split("/").pop():""},getFileType(e){switch(e){case 1:return"image";case 2:return"video";case 3:return"file";default:return"image"}}}},n=o,d=i(1656),c=(0,d.A)(n,a,s,!1,null,"72891219",null),p=c.exports},4759:function(e,t,i){i.d(t,{WQ:function(){return l},hc:function(){return r},vS:function(){return s},yH:function(){return o}});var a=i(7120);function s(e){return(0,a.Ay)({url:"/admin/sourcematerial/getList",method:"get",params:e})}function l(e){return(0,a.Ay)({url:"/admin/sourcematerial/addSourceMaterial",method:"post",data:e})}function r(e,t){return(0,a.Ay)({url:"/admin/sourcematerial/editSourceMaterial/"+t,method:"put",data:e})}function o(e){return(0,a.Ay)({url:"/admin/sourcematerial/delete/"+e,method:"delete"})}}}]);
//# sourceMappingURL=726.777044ff.js.map