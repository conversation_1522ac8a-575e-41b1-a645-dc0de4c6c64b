"use strict";(self["webpackChunkesop_dashboard"]=self["webpackChunkesop_dashboard"]||[]).push([[119],{2119:function(t,e,a){a.r(e),a.d(e,{default:function(){return p}});var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"operationlog-container"},[e("el-form",{ref:"form",staticClass:"demo-ruleForm",attrs:{model:t.form,"label-width":"120px","label-position":"left"}},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:4}},[e("el-form-item",{attrs:{label:t.$t("operationLog.form.operatorName"),prop:"admin_name"}},[e("el-input",{attrs:{placeholder:t.$t("operationLog.form.operatorNamePlaceholder")},model:{value:t.form.admin_name,callback:function(e){t.$set(t.form,"admin_name",e)},expression:"form.admin_name"}})],1)],1),e("el-col",{attrs:{span:4}},[e("el-form-item",{attrs:{label:t.$t("operationLog.form.operatorAccount"),prop:"admin_account"}},[e("el-input",{attrs:{placeholder:t.$t("operationLog.form.operatorAccountPlaceholder")},model:{value:t.form.admin_account,callback:function(e){t.$set(t.form,"admin_account",e)},expression:"form.admin_account"}})],1)],1),e("el-col",{attrs:{span:4}},[e("el-form-item",{attrs:{label:t.$t("operationLog.form.operationTime"),prop:"date"}},[e("el-date-picker",{attrs:{"value-format":"timestamp",type:"daterange","range-separator":"至","start-placeholder":t.$t("public.startDate"),"end-placeholder":t.$t("public.endDate")},model:{value:t.form.date,callback:function(e){t.$set(t.form,"date",e)},expression:"form.date"}})],1)],1)],1),e("el-row",{attrs:{type:"flex",justify:"end"}},[e("el-col",{attrs:{span:4}},[e("el-button",{attrs:{size:"mini"},on:{click:function(e){return t.resetForm()}}},[t._v(t._s(t.$t("public.reset")))]),e("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.searchForm()}}},[t._v(t._s(t.$t("public.search")))])],1)],1)],1),e("div",{staticStyle:{height:"60vh","background-color":"#ccc",margin:"10px 0"}},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticStyle:{width:"100%"},attrs:{data:t.dataList,border:"",height:"100%"}},[e("el-table-column",{attrs:{prop:"num",label:t.$t("operationLog.table.num"),width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),e("el-table-column",{attrs:{prop:"admin_name",label:t.$t("operationLog.table.operatorName"),align:"center"}}),e("el-table-column",{attrs:{prop:"admin_account",label:t.$t("operationLog.table.operatorAccount"),align:"center"}}),e("el-table-column",{attrs:{prop:"action",label:t.$t("operationLog.table.action"),align:"center"}}),e("el-table-column",{attrs:{prop:"module",label:t.$t("operationLog.table.module"),align:"center"}}),e("el-table-column",{attrs:{prop:"created_at",label:t.$t("operationLog.table.operationTime"),align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.$formatTimeStamp(e.row.created_at))+" ")]}}])})],1)],1),e("el-row",{attrs:{gutter:20,type:"flex",justify:"end"}},[e("el-pagination",{attrs:{background:"","current-page":t.pageNum,"page-sizes":[10,20,50],"page-size":t.pageSize,layout:"total, prev, pager, next",total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange,"update:currentPage":function(e){t.pageNum=e},"update:current-page":function(e){t.pageNum=e}}})],1)],1)},r=[],n=a(7120);function i(t){return(0,n.Ay)({url:"/admin/operationlog/getList",method:"get",params:t})}var l={data(){return{dataList:[],pageNum:1,pageSize:10,total:0,form:{admin_name:"",admin_account:"",date:[]},isLoading:!1}},created(){this.getList()},methods:{getList(){this.isLoading=!0,i({page:this.pageNum,pageSize:this.pageSize,admin_name:this.form.admin_name,admin_account:this.form.admin_account,created_at_start:this.form.date.length>0?this.form.date[0]/1e3:"",created_at_end:this.form.date.length>0?this.form.date[1]/1e3:""}).then((t=>{0==t.code&&(this.dataList=t.data.data,this.total=t.data.total,this.isLoading=!1)}))},searchForm(){this.getList()},resetForm(){this.page=1,this.$refs.form.resetFields(),this.getList()},handleSizeChange(t){this.pageNum=1,this.pageSize=t,this.getList()},handleCurrentChange(t){this.pageNum=t,this.getList()}}},s=l,c=a(1656),m=(0,c.A)(s,o,r,!1,null,"50089e33",null),p=m.exports}}]);
//# sourceMappingURL=119.ba77b662.js.map