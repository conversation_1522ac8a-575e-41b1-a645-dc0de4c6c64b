"use strict";(self["webpackChunkesop_dashboard"]=self["webpackChunkesop_dashboard"]||[]).push([[232],{4232:function(e,t,s){s.r(t),s.d(t,{default:function(){return g}});var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"resource-container"},[t("el-form",{ref:"form",staticClass:"demo-ruleForm",attrs:{model:e.form,"label-width":"120px","label-position":"left"}},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:4}},[t("el-form-item",{attrs:{label:e.$t("resource.form.name"),prop:"name"}},[t("el-input",{attrs:{placeholder:e.$t("resource.form.namePlaceholder")},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),t("el-col",{attrs:{span:4}},[t("el-form-item",{attrs:{label:e.$t("resource.form.packName"),prop:"packName"}},[t("el-input",{attrs:{placeholder:e.$t("resource.form.packNamePlaceholder")},model:{value:e.form.packName,callback:function(t){e.$set(e.form,"packName",t)},expression:"form.packName"}})],1)],1),t("el-col",{attrs:{span:4}},[t("el-form-item",{attrs:{label:e.$t("resource.form.date"),prop:"date"}},[t("el-date-picker",{attrs:{"value-format":"timestamp",type:"daterange","range-separator":e.$t("public.to"),"start-placeholder":e.$t("public.startDate"),"end-placeholder":e.$t("public.endDate")},model:{value:e.form.date,callback:function(t){e.$set(e.form,"date",t)},expression:"form.date"}})],1)],1)],1),t("el-row",{attrs:{type:"flex",justify:"space-between"}},[t("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.openResourceSelectionDialog}},[e._v(e._s(e.$t("resource.button.sendByRule")))]),t("el-col",{attrs:{span:4}},[t("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.resetForm("form","getList")}}},[e._v(e._s(e.$t("public.reset")))]),t("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.searchForm()}}},[e._v(e._s(e.$t("public.search")))])],1)],1)],1),t("div",{staticStyle:{height:"60vh","background-color":"#fff",margin:"10px 0","border-radius":"8px","box-shadow":"0 2px 12px 0 rgba(0, 0, 0, 0.1)",padding:"16px"}},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.dataList,border:"",height:"100%","header-cell-style":{background:"#f5f7fa",color:"#606266",fontWeight:"bold",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},"row-style":{cursor:"pointer",transition:"all 0.3s ease"},"cell-style":{padding:"12px 16px",borderColor:"#ebeef5"}}},[t("el-table-column",{attrs:{prop:"num",label:e.$t("resource.table.num"),width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),t("el-table-column",{attrs:{prop:"name",label:e.$t("resource.table.name"),align:"center"}}),t("el-table-column",{attrs:{prop:"pack_name",label:e.$t("resource.table.pack_name"),align:"center"}}),t("el-table-column",{attrs:{prop:"created_at",label:e.$t("resource.table.created_at"),align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.$formatTimeStamp(t.row.created_at))+" ")]}}])}),t("el-table-column",{attrs:{label:e.$t("public.operation"),fixed:"right",align:"center"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.sendAll(s.row.id)}}},[e._v(e._s(e.$t("resource.button.sendAll")))]),t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){e.isShow=!0,e.sendPartId=s.row.id,e.pack_name=s.row.name,e.pageNum1=1,e.getEquipmentList()}}},[e._v(e._s(e.$t("resource.button.sendPart")))]),t("el-popconfirm",{staticStyle:{"margin-left":"10px"},attrs:{title:e.$t("resource.table.deleteResource")},on:{confirm:function(t){return e.del(s.row.id)}}},[t("el-button",{staticStyle:{color:"#ff0000"},attrs:{slot:"reference",type:"text",size:"small"},slot:"reference"},[e._v(e._s(e.$t("public.delete")))])],1)]}}])})],1)],1),t("el-row",{attrs:{gutter:20,type:"flex",justify:"end"}},[t("el-pagination",{attrs:{background:"","current-page":e.pageNum,"page-sizes":[10,20,50],"page-size":e.pageSize,layout:"total, prev, pager, next",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange,"update:currentPage":function(t){e.pageNum=t},"update:current-page":function(t){e.pageNum=t}}})],1),t("el-dialog",{attrs:{title:e.$t("resource.dialog.title.selectDevice"),visible:e.isShow,width:"50%"},on:{"update:visible":function(t){e.isShow=t},close:e.close}},[t("el-input",{attrs:{placeholder:"输入关键字进行过滤"},model:{value:e.filterText,callback:function(t){e.filterText=t},expression:"filterText"}}),t("el-tree",{ref:"tree",staticClass:"filter-tree",attrs:{data:e.equipmentData,props:e.defaultProps,"filter-node-method":e.filterNode,"node-key":"id","render-content":e.renderContent,"show-checkbox":"","check-strictly":"","highlight-current":!0},on:{"node-click":e.handleNodeClick,"check-change":e.handleCheckChange}}),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.isShow=!1}}},[e._v(e._s(e.$t("public.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:e.confirm}},[e._v(e._s(e.$t("public.confirm")))])],1)],1),t("el-dialog",{attrs:{title:e.$t("resource.dialog.title.selectResource"),visible:e.isResourceSelectionDialogVisible,width:"60%"},on:{"update:visible":function(t){e.isResourceSelectionDialogVisible=t}}},[t("el-form",{ref:"resourceForm",staticClass:"demo-ruleForm",attrs:{model:e.resourceForm,"label-width":"80px","label-position":"left"}},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:6}},[t("el-form-item",{attrs:{label:e.$t("resource.form.name"),prop:"name"}},[t("el-input",{attrs:{placeholder:e.$t("resource.form.namePlaceholder")},model:{value:e.resourceForm.name,callback:function(t){e.$set(e.resourceForm,"name",t)},expression:"resourceForm.name"}})],1)],1),t("el-col",{attrs:{span:6}},[t("el-form-item",{attrs:{label:e.$t("resource.form.packName"),prop:"packName"}},[t("el-input",{attrs:{placeholder:e.$t("resource.form.packNamePlaceholder")},model:{value:e.resourceForm.packName,callback:function(t){e.$set(e.resourceForm,"packName",t)},expression:"resourceForm.packName"}})],1)],1),t("el-col",{attrs:{span:4}},[t("el-form-item",{attrs:{label:e.$t("resource.form.date"),prop:"date"}},[t("el-date-picker",{attrs:{"value-format":"timestamp",type:"datetimerange","range-separator":e.$t("public.to"),"start-placeholder":e.$t("public.startDate"),"end-placeholder":e.$t("public.endDate")},model:{value:e.resourceForm.date,callback:function(t){e.$set(e.resourceForm,"date",t)},expression:"resourceForm.date"}})],1)],1)],1),t("el-row",{attrs:{type:"flex",justify:"end"}},[t("el-col",{attrs:{span:4}},[t("el-button",{on:{click:function(t){return e.resetForm("resourceForm","getResourceList")}}},[e._v(" "+e._s(e.$t("public.reset")))]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){e.resourcePageNum=1,e.getResourceList()}}},[e._v(e._s(e.$t("public.search")))])],1)],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isResourceLoading,expression:"isResourceLoading"}],ref:"resourceTable",staticStyle:{width:"100%"},attrs:{"tooltip-effect":"dark",data:e.resourceList,border:"","row-key":e=>e.id},on:{"selection-change":e.handleResourceSelectionChange}},[t("el-table-column",{attrs:{type:"selection",width:"40","reserve-selection":"",selectable:e.selectable}}),t("el-table-column",{attrs:{prop:"num",label:"序号",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),t("el-table-column",{attrs:{prop:"name",label:e.$t("resource.table.name"),align:"center"}}),t("el-table-column",{attrs:{prop:"pack_name",label:e.$t("resource.table.pack_name"),align:"center"}}),t("el-table-column",{attrs:{prop:"created_at",label:e.$t("resource.table.created_at"),align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.$formatTimeStamp(t.row.created_at))+" ")]}}])})],1),t("el-row",{attrs:{gutter:20,type:"flex",justify:"end"}},[t("el-pagination",{attrs:{background:"","current-page":e.resourcePageNum,"page-sizes":[10,20,50],"page-size":e.resourcePageSize,layout:"total, prev, pager, next",total:e.resourceTotal},on:{"size-change":e.handleResourceSizeChange,"current-change":e.handleResourceCurrentChange,"update:currentPage":function(t){e.resourcePageNum=t},"update:current-page":function(t){e.resourcePageNum=t}}})],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.handleCancelInResourceDialog}},[e._v(e._s(e.$t("public.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:e.handleResourceSelectionConfirm}},[e._v(e._s(e.$t("resource.button.nextStep")))])],1)],1),t("el-dialog",{attrs:{title:e.$t("resource.dialog.title.group_name"),visible:e.isGroupSelectionDialogVisible,width:"60%"},on:{"update:visible":function(t){e.isGroupSelectionDialogVisible=t}}},[t("el-form",{ref:"groupForm",staticClass:"demo-ruleForm",attrs:{model:e.groupForm,"label-width":"80px","label-position":"left"}}),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isGroupLoading,expression:"isGroupLoading"}],ref:"groupTable",staticStyle:{width:"100%"},attrs:{"tooltip-effect":"dark",data:e.groupList,border:"","row-key":e=>e.id},on:{"selection-change":e.handleGroupSelectionChange}},[t("el-table-column",{attrs:{type:"selection",width:"40","reserve-selection":"",selectable:e.selectable}}),t("el-table-column",{attrs:{prop:"num",label:e.$t("resource.table.num"),width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),t("el-table-column",{attrs:{prop:"name",label:e.$t("resource.dialog.title.group_name"),align:"center"}})],1),t("el-row",{attrs:{gutter:20,type:"flex",justify:"end"}},[t("el-pagination",{attrs:{background:"","current-page":e.groupPageNum,"page-sizes":[10,20,50],"page-size":e.groupPageSize,layout:"total, prev, pager, next",total:e.groupTotal},on:{"size-change":e.handleGroupSizeChange,"current-change":e.handleGroupCurrentChange,"update:currentPage":function(t){e.groupPageNum=t},"update:current-page":function(t){e.groupPageNum=t}}})],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.handleCancelInGroupDialog}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.handleGroupNextStep}},[e._v(e._s(e.$t("resource.button.nextStep")))])],1)],1),t("el-dialog",{attrs:{title:e.$t("resource.dialog.title.inputDevice"),visible:e.isResourceDisplayDialogVisible,width:"60%"},on:{"update:visible":function(t){e.isResourceDisplayDialogVisible=t}}},[e.selectedResources.length>0?t("div",[t("el-form",{staticClass:"vertical-form"},e._l(e.selectedResources,(function(s){return t("el-form-item",{key:s.id,scopedSlots:e._u([{key:"label",fn:function(){return[t("div",{staticClass:"resource-label"},[e._v("资源名称 ： "+e._s(s.name))])]},proxy:!0}],null,!0)},[t("el-input",{staticClass:"input-block",attrs:{placeholder:e.$t("resource.dialog.tip.deviceAliasHint")},model:{value:s.inputValue,callback:function(t){e.$set(s,"inputValue",t)},expression:"resource.inputValue"}})],1)})),1)],1):t("div",[e._v(e._s(e.$t("resource.dialog.tip.noSelectedResources")))]),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.handleCancelInGroupResourceDialog}},[e._v(" "+e._s(e.$t("public.cancel"))+" ")]),t("el-button",{attrs:{type:"primary"},on:{click:e.sendRule}},[e._v(" "+e._s(e.$t("public.confirm"))+" ")])],1)]),t("el-dialog",{attrs:{title:e.$t("resource.dialog.title.selectDevice"),visible:e.isShowForNextStep,width:"80%"},on:{"update:visible":function(t){e.isShowForNextStep=t},close:e.closeForNextStep}},[t("el-form",{ref:"equipmentForm",staticClass:"demo-ruleForm",attrs:{model:e.equipmentForm,"label-width":"80px","label-position":"left"}},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:6}},[t("el-form-item",{attrs:{label:e.$t("resource.form.deviceName"),prop:"name"}},[t("el-input",{attrs:{placeholder:e.$t("resource.form.deviceNamePlaceholder")},model:{value:e.equipmentForm.name,callback:function(t){e.$set(e.equipmentForm,"name",t)},expression:"equipmentForm.name"}})],1)],1),t("el-col",{attrs:{span:6}},[t("el-form-item",{attrs:{label:e.$t("resource.form.deviceId"),prop:"mac_address"}},[t("el-input",{attrs:{placeholder:e.$t("resource.form.deviceIdPlaceholder")},model:{value:e.equipmentForm.mac_address,callback:function(t){e.$set(e.equipmentForm,"mac_address",t)},expression:"equipmentForm.mac_address"}})],1)],1),t("el-col",{attrs:{span:12}},[t("el-form-item",{attrs:{label:e.$t("resource.form.date"),prop:"date"}},[t("el-date-picker",{attrs:{"value-format":"timestamp",type:"datetimerange","range-separator":e.$t("public.to"),"start-placeholder":e.$t("public.startDate"),"end-placeholder":e.$t("public.endDate")},model:{value:e.equipmentForm.date,callback:function(t){e.$set(e.equipmentForm,"date",t)},expression:"equipmentForm.date"}})],1)],1)],1),t("el-row",{attrs:{type:"flex",justify:"end"}},[t("el-col",{attrs:{span:4}},[t("el-button",{on:{click:function(t){return e.resetForm("equipmentForm","getEquipmentList")}}},[e._v(' $t("public.reset")')]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){e.pageNum1=1,e.getEquipmentList()}}},[e._v(e._s(e.$t("public.search")))])],1)],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoading,expression:"isLoading"}],ref:"singleTable",staticStyle:{width:"100%"},attrs:{"tooltip-effect":"dark",data:e.equipmentList,border:"","row-key":e=>e.id,"row-class-name":e.tableRowClassName,"cell-class-name":e.tableCellClassName},on:{"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{type:"selection",width:"40","reserve-selection":"",selectable:e.selectable}}),t("el-table-column",{attrs:{prop:"num",label:e.$t("resource.table.num"),width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),t("el-table-column",{attrs:{prop:"name",label:e.$t("resource.form.deviceName"),align:"center"}}),t("el-table-column",{attrs:{prop:"mac_address",label:e.$t("resource.form.mac_address"),align:"center"}}),t("el-table-column",{attrs:{prop:"created_at",label:e.$t("resource.table.created_at"),align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.$formatTimeStamp(t.row.created_at))+" ")]}}])})],1),t("el-row",{attrs:{gutter:20,type:"flex",justify:"end"}},[t("el-pagination",{attrs:{background:"","current-page":e.pageNum1,"page-sizes":[10,20,50],"page-size":e.pageSize1,layout:"total, prev, pager, next",total:e.total1},on:{"size-change":e.handleEquipmentSizeChange,"current-change":e.handleEquipmentCurrentChange,"update:currentPage":function(t){e.pageNum1=t},"update:current-page":function(t){e.pageNum1=t}}})],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.isShowForNextStep=!1}}},[e._v(e._s(e.$t("public.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.confirmForNextStep()}}},[e._v(e._s(e.$t("public.confirm")))])],1)],1)],1)},a=[],i=(s(4114),s(7120));function o(e){return(0,i.Ay)({url:"/admin/resourcepack/getList",method:"get",params:e})}function l(e){return(0,i.Ay)({url:"/admin/equipment/getTreeList",method:"get",params:e})}function n(e){return(0,i.Ay)({url:"/admin/resourcepack/delete/"+e,method:"delete"})}function c(e){return(0,i.Ay)({url:"/admin/resourcepack/pub",method:"post",data:e})}function u(e){return(0,i.Ay)({url:"/admin/equipment/getGroupList",method:"get",params:e})}var d={watch:{filterText(e){this.$refs.tree.filter(e)}},data(){return{equipmentData:[],defaultProps:{children:"children",label:"label",isClient:"isClient"},selectedNode:null,disabledNodes:[],dataList:[],equipmentList:[],resourceList:[],pageNum:1,pageSize:10,groupList:[],groupPageNum:1,groupPageSize:10,groupTotal:0,isGroupLoading:!1,selectedGroupIds:[],isGroupSelectionDialogVisible:!1,sendPartId:0,total:0,pageNum1:1,pageSize1:5,total1:0,resourcePageNum:1,resourcePageSize:10,resourceTotal:0,form:{name:"",packName:"",date:[]},equipmentForm:{name:"",mac_address:"",date:[]},resourceForm:{name:"",packName:"",date:[]},groupForm:{name:"",packName:"",date:[]},id:"",currentNodeKey:null,equipment_name:"",equipment_id_str:"",pack_name:"",isShow:!1,isEdit:!1,isLoading:!1,isResourceLoading:!1,selectedEquipmentIds:[],isResourceSelectionDialogVisible:!1,isShowForNextStep:!1,selectedResourceIds:[],selectedResources:[],isResourceDisplayDialogVisible:!1,filterText:""}},created(){this.getList()},methods:{renderContent(e,{node:t,data:s}){const r=s.isClient,a=!r||this.selectedNode&&this.selectedNode.id!==s.id;return e("span",{style:{color:a?"#7e7e7e":"#333",cursor:a?"not-allowed":"pointer",pointerEvents:a?"none":"auto"},class:{"disabled-node":a,"enabled-node":!a&&r}},t.label)},handleCheckChange(e,t){if(t){const e=this.$refs.tree.getCheckedNodes(),t=e.filter((e=>e.isClient));if(t.length>0){const e=t[t.length-1];this.$refs.tree.setCheckedNodes([e],!1),this.selectedNode=e,this.disableOtherNodes(e.id)}}else this.selectedNode=null},handleNodeClick(e){e.isClient&&(this.selectedNode&&this.selectedNode.id===e.id?(this.selectedNode=null,this.disabledNodes=[],this.$refs.tree.setCheckedKeys([])):(this.selectedNode=e,this.$refs.tree.setCheckedKeys([e.id]),this.disableOtherNodes(e.id)))},disableOtherNodes(e){const t=s=>{let r=[];return s.forEach((s=>{s.isClient&&s.id!==e&&r.push(s.id),s.children&&(r=r.concat(t(s.children)))})),r};this.disabledNodeIds=t(this.equipmentData).filter((t=>t!==e))},filterNode(e,t){return!e||-1!==t.label.indexOf(e)},selectable(e,t){return"disabled"!==e.status},tableRowClassName({row:e,rowIndex:t}){return this.selectedEquipmentIds.includes(e.id)?"selected-row":""},tableCellClassName({row:e,column:t,rowIndex:s,columnIndex:r}){return"name"===t.property&&this.selectedEquipmentIds.includes(e.id)?"selected-cell":""},getList(){this.isLoading=!0,o({page:this.pageNum,pageSize:this.pageSize,name:this.form.name,packName:this.form.packName,created_at_start:this.form.date.length>0?this.form.date[0]/1e3:"",created_at_end:this.form.date.length>0?this.form.date[1]/1e3:""}).then((e=>{0==e.code&&(this.dataList=e.data.data,this.total=e.data.total,this.isLoading=!1)}))},getGroupList(){this.isGroupLoading=!0,u({page:this.groupPageNum,pageSize:this.groupPageSize}).then((e=>{0===e.code&&(this.groupList=e.data.data,this.groupTotal=e.data.total,this.isGroupLoading=!1)}))},handleGroupSelectionChange(e){this.selectedGroupIds=[],e.forEach((e=>{this.selectedGroupIds.push(e.id)}))},handleGroupSelectionConfirm(){this.isGroupSelectionDialogVisible=!1,this.$message({type:"info",message:this.$t("resource.dialog.message.selectedGroups",{count:this.selectedGroupIds.length})})},handleGroupSizeChange(e){this.groupPageNum=1,this.groupPageSize=e,this.getGroupList()},handleGroupCurrentChange(e){this.groupPageNum=e,this.getGroupList()},getResourceList(){this.isResourceLoading=!0,o({page:this.resourcePageNum,pageSize:this.resourcePageSize,name:this.resourceForm.name,packName:this.resourceForm.packName,created_at_start:this.resourceForm.date.length>0?this.resourceForm.date[0]/1e3:"",created_at_end:this.resourceForm.date.length>0?this.resourceForm.date[1]/1e3:""}).then((e=>{0===e.code&&(this.resourceList=e.data.data,this.resourceTotal=e.data.total,this.isResourceLoading=!1)}))},closeForNextStep(){this.pack_name="",this.equipment_name="",this.selectedEquipmentIds=[],this.$refs.equipmentForm.resetFields()},confirmForNextStep(){if(!this.equipment_id_str)return this.$message({type:"warning",message:this.$t("resource.dialog.tip.selectAtLeastOneDevice")});this.$confirm(this.$t("resource.confirm.sendToDevices"),this.$t("resource.confirm.title"),{confirmButtonText:this.$t("public.confirm"),cancelButtonText:this.$t("public.cancel"),type:"warning"}).then((()=>{this.sendPart(this.equipment_id_str),this.equipment_id_str="",this.sendPartId=0}))},handleResourceSizeChange(e){this.resourcePageNum=1,this.resourcePageSize=e,this.getResourceList()},handleResourceCurrentChange(e){this.resourcePageNum=e,this.getResourceList()},getEquipmentList(){var e=this;l().then((t=>{0==t.code&&(e.equipmentData=t.data)}))},confirm(e){let t=this.$refs.tree.getCheckedNodes();if(console.log(t,"dfsdfsdfsdfds"),this.equipment_id_str=t.map((e=>e.id)).join(","),console.log(this.equipment_id_str,"equipment_id_strequipment_id_strequipment_id_strequipment_id_str"),!this.equipment_id_str)return this.$message({type:"warning",message:this.$t("resource.dialog.tip.selectAtLeastOneDevice")});this.$confirm(this.$t("resource.confirm.sendToDevices"),this.$t("resource.confirm.title"),{confirmButtonText:this.$t("public.confirm"),cancelButtonText:this.$t("public.cancel"),type:"warning"}).then((()=>{this.sendPart(this.equipment_id_str),this.equipment_id_str="",this.sendPartId=0,this.selectedNode=null,this.disabledNodes=[],this.$refs.tree&&this.$refs.tree.setCheckedKeys([]),this.isShow=!1}))},handleSelectionChange(e){let t=[];this.selectedEquipmentIds=[],e.forEach((e=>{t.push(e.id),this.selectedEquipmentIds.push(e.id)})),this.equipment_id_str=t.join(",")},sendAll(e){const t={type:2,list:[{resource_id:e}]};c(t).then((e=>{0===e.code&&(this.$message({type:"success",message:this.$t("resource.dialog.message.sendSuccess")}),this.getList())}))},sendRule(){const e=this.selectedGroupIds.join(","),t=this.selectedResources.map((e=>({resource_id:e.id,equipment_name:e.inputValue}))),s={type:3,group_id:e,list:t};c(s).then((e=>{0===e.code?(this.$message({type:"success",message:this.$t("resource.dialog.message.sendByRuleSuccess")}),this.selectedResourceIds=[],this.selectedResources=[],this.selectedGroupIds=[],this.$refs.resourceTable&&this.$refs.resourceTable.clearSelection(),this.$refs.groupTable&&this.$refs.groupTable.clearSelection(),this.isResourceDisplayDialogVisible=!1):this.$message({type:"error",message:this.$t("resource.dialog.message.sendFailed")})})).catch((e=>{this.$message({type:"error",message:this.$t("resource.dialog.message.requestError")}),console.error(e)}))},sendPart(e){const t={type:1,list:[{resource_id:this.sendPartId,equipment_id:e}]};c(t).then((e=>{0===e.code&&(this.$message({type:"success",message:this.$t("resource.dialog.message.sendSuccess")}),this.getList())}))},send(e,t,s){c({equipment_name:s,pack_name:t,type:e}).then((e=>{0==e.code&&(this.$message({type:"success",message:this.$t("resource.dialog.message.sendSuccess")}),this.getList())}))},del(e){n(e).then((e=>{this.$message({type:"success",message:this.$t("public.deleteSuccess")}),this.getList()}))},close(){this.pack_name="",this.equipment_name="",this.selectedEquipmentIds=[],this.$refs.equipmentForm&&this.$refs.equipmentForm.resetFields(),this.$refs.singleTable&&this.$refs.singleTable.clearSelection()},searchForm(){this.getList()},resetForm(e,t){this.pageNum=1,this.$refs[e]&&this.$refs[e].resetFields(),this[t]()},handleSizeChange(e){this.pageNum=1,this.pageSize=e,this.getList()},handleCurrentChange(e){this.pageNum=e,this.getList()},handleEquipmentSizeChange(e){this.pageNum1=1,this.pageSize1=e,this.getEquipmentList()},handleEquipmentCurrentChange(e){this.pageNum1=e,this.getEquipmentList()},handleCancelInResourceDialog(){this.isResourceSelectionDialogVisible=!1,this.selectedResourceIds=[],this.selectedResources=[],this.$refs.resourceTable&&this.$refs.resourceTable.clearSelection(),this.$refs.resourceForm&&this.$refs.resourceForm.resetFields()},openResourceSelectionDialog(){this.isResourceSelectionDialogVisible=!0,this.resourcePageNum=1,this.getResourceList()},handleResourceSelectionChange(e){this.selectedResourceIds=[],e.forEach((t=>{this.selectedResourceIds.push(t.id),this.selectedResources=e}))},handleGroupNextStep(){0!==this.selectedGroupIds.length?(this.isGroupSelectionDialogVisible=!1,this.isResourceDisplayDialogVisible=!0):this.$message({type:"warning",message:this.$t("resource.dialog.tip.selectAtLeastOneGroup")})},handleCancelInGroupResourceDialog(){this.handleCancelInResourceDialog(),this.isResourceDisplayDialogVisible=!1},handleCancelInGroupDialog(){this.isGroupSelectionDialogVisible=!1,this.selectedGroupIds=[],this.selectedResourceIds=[],this.$refs.groupTable&&this.$refs.groupTable.clearSelection(),this.handleCancelInResourceDialog()},handleResourceSelectionConfirm(){0!==this.selectedResourceIds.length?(this.isResourceSelectionDialogVisible=!1,this.isGroupSelectionDialogVisible=!0,this.groupPageNum=1,this.getGroupList()):this.$message({type:"warning",message:this.$t("resource.dialog.tip.selectAtLeastOneResource")})}}},p=d,m=s(1656),h=(0,m.A)(p,r,a,!1,null,"a726ff14",null),g=h.exports}}]);
//# sourceMappingURL=232.1f7b88f4.js.map