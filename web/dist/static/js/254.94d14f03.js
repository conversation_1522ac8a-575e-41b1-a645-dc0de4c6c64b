"use strict";(self["webpackChunkesop_dashboard"]=self["webpackChunkesop_dashboard"]||[]).push([[254],{3043:function(e,t,i){i.d(t,{A:function(){return u}});var s=function(){var e=this,t=e._self._c;return t("div",[t("el-upload",{ref:"uploadRef",staticClass:"avatar-uploader2",class:e.name,attrs:{action:e.upload_host,name:"uploadFile",headers:e.headers,multiple:e.uploadNum>1,"show-file-list":!1,"before-upload":e.beforeUpload,"on-remove":e.handleRemove,"on-progress":e.handleUploadProgress,"on-success":e.handleUploadSuccess,disabled:!e.isDisabled,accept:e.acceptFileType}}),t("div",{staticClass:"images"},[e._l(e.fileList,(function(i,s){return t("div",{key:s,staticClass:"item"},[t("div",{staticClass:"img",attrs:{id:"preview-item"},on:{mouseover:function(e){i.hover=!0},mouseout:function(e){i.hover=!1}}},[e.isImageUrl(i.url)?t("el-image",{ref:"img",refInFor:!0,staticStyle:{width:"120px",height:"120px"},attrs:{src:e.imageUrl+i.url,"preview-src-list":e.previewImages(),fit:"cover"},on:{click:function(t){return t.stopPropagation(),e.handleClickItem.apply(null,arguments)}}}):e.isVideoUrl(i.url)?t("video",{staticStyle:{width:"120px",height:"120px",display:"block"},attrs:{controls:""}},[t("source",{attrs:{src:e.imageUrl+i.url,type:"video/avi"}})]):t("div",{staticClass:"file-preview"},[t("i",{class:e.getFileIconClass(i.url)}),t("div",{staticClass:"file-name"},[e._v(e._s(i.name||e.getFileNameFromUrl(i.url)))])]),t("div",{directives:[{name:"show",rawName:"v-show",value:i.hover,expression:"item.hover"}],staticClass:"mask"},[e.canPreview(i.url)?t("i",{staticClass:"el-icon-zoom-in",on:{click:function(t){return e.previewFile(i)}}}):t("i",{staticClass:"el-icon-download",on:{click:function(t){return e.downloadFile(i.url)}}}),e.isDisabled?e._e():t("i",{staticClass:"el-icon-upload2",on:{click:function(t){return e.uploadImage(i.url)}}}),e.isDisabled?e._e():t("i",{staticClass:"el-icon-delete",on:{click:function(t){return e.deleteImage(i.url)}}})])],1)])})),e.fileList.length<e.uploadNum?t("div",{staticClass:"add",on:{click:function(t){return e.uploadImage()}}},[t("i",{staticClass:"el-icon-plus"})]):e._e()],2)],1)},a=[],l=(i(4114),i(4603),i(7566),i(8721),i(7120),i(7282)),n=i(1974),r={props:{isDisabled:Boolean,fileType:String,name:String,acceptFileType:{type:String,default:""},fileList:Array,uploadNum:{type:Number,default:1},index:{type:Number,default:0}},data(){return{upload_host:"/admin/sourcematerial/upload",imageUrl:"/assets/media/",headers:{Authorization:n.A.getters.token},showImage:"",dialogVisible:!1,uploadImageId:"",list:[]}},computed:{showFileList:{get:function(){return this.fileList&&this.fileList.length>0},set:function(e){}}},methods:{emitInput(e){this.$emit("input",e)},handleClickItem(){setTimeout((()=>{let e=document.querySelector(".el-image-viewer__wrapper");e&&e.addEventListener("click",(e=>{"el-image-viewer__actions__inner"!=e.target.parentNode.className&&document.querySelector(".el-image-viewer__close").click()}))}),300)},handleRemove(e,t){0===t.length?this.fileList=[]:this.fileList=t},getUUID(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(e=>("x"===e?16*Math.random()|0:8).toString(16)))},beforeUpload(e){const t=this.checkFileType(e);if(!t)return this.handleInvalidType(e),!1;if(this.list.push(e.name),this.list.length>this.uploadNum)return this.$message.warning(this.$i18n.t("upload.maxFileCount",{count:this.uploadNum})),this.resetUploadState(),!1;if(e.type.includes("image")){let t=new FileReader;t.onload=e=>{let t=e.target.result,i=document.createElement("img");i.src=t,i.onload=()=>{this.$emit("fileData",{width:i.width,height:i.height}),console.log("宽度：",i.width),console.log("高度：",i.height)}},t.readAsDataURL(e)}else if(e.type.includes("video")){console.log(e,"ffff");const t=document.createElement("video");t.src=URL.createObjectURL(e),t.addEventListener("loadedmetadata",(()=>{this.$emit("fileData",{width:t.videoWidth,height:t.videoHeight}),console.log("width, height:",t.videoWidth,t.videoHeight)}))}return!0},checkFileType(e){if(console.log(this.fileType),"image"===this.fileType)return e.type.includes("image");if("video"===this.fileType)return e.type.includes("video");if("image/video"===this.fileType)return e.type.includes("image")||e.type.includes("video");if("file"===this.fileType){const t=this.acceptFileType.split(",").map((e=>e.trim()));return t.includes(e.type)}return!0},handleInvalidType(e){const t=this.getWarningMessage();this.$message.warning(t),this.resetUploadState()},getWarningMessage(){switch(this.fileType){case"image":return this.$i18n.t("upload.onlyImage");case"video":return this.$i18n.t("upload.onlyVideo");case"image/video":return this.$i18n.t("upload.onlyVideoOrImageAgain");case"pdf":return this.$i18n.t("material.dialog.form.onlyPDF");case"file":return this.$i18n.t("material.dialog.form.onlyFile");default:return""}},resetUploadState(){this.$refs.uploadRef.clearFiles(),this.list=[],this.fileList=[...this.fileList]},handleUploadProgress(){this.$emit("uploadStatus",!0)},handleUploadSuccess(e,t,i){this.$emit("uploadStatus",!1),this.fileList.push({name:t.name,hover:!1,url:t.response.data.file_name}),this.$emit("editUrl",{name:t.name,hover:!1,url:t.response.data.file_name},this.name,this.index)},previewImages(){let e=[];return this.fileList&&this.fileList.length>0&&this.fileList.forEach((t=>{if(this.isImageUrl(t.url)){const i=this.imageUrl+t.url;e.push(i)}})),e},previewImage(e){let t=[];this.fileList.forEach((e=>{this.isImageUrl(e.url)&&t.push(e)}));let i=(0,l.NO)(t,"url",e);-1!==i&&this.$refs.img&&this.$refs.img[i]&&(this.$refs.img[i].showViewer=!0)},previewVideo(e){},previewFile(e){this.isImageUrl(e.url)?this.previewImage(e.url):this.isVideoUrl(e.url)?this.previewVideo(e.url):window.open(this.imageUrl+e.url,"_blank")},isImageUrl(e){if(!e)return!1;const t=e.substring(e.lastIndexOf(".")+1).toLowerCase(),i=["jpg","jpeg","png","gif","webp"];return i.includes(t)},isVideoUrl(e){if(!e)return!1;const t=["mp4","ogg","mov","webm"],i=e.substring(e.lastIndexOf(".")+1).toLowerCase();return t.includes(i)},getFileIconClass(e){if(!e)return"el-icon-document";const t=e.substring(e.lastIndexOf(".")+1).toLowerCase();switch(t){case"pdf":return"el-icon-document-pdf";case"doc":case"docx":return"el-icon-document";case"xls":case"xlsx":return"el-icon-document";case"ppt":case"pptx":return"el-icon-document";default:return"el-icon-document"}},getFileNameFromUrl(e){return e?e.split("/").pop():""},canPreview(e){return this.isImageUrl(e)||this.isVideoUrl(e)},downloadFile(e){window.open(this.imageUrl+e,"_blank")},uploadImage(e){this.list=[],this.uploadImageId=e,this.$refs.uploadRef&&this.$refs.uploadRef.$el.querySelector("input").click()},deleteImage(e){const t=(0,l.NO)(this.fileList,"url",e);-1!==t&&this.fileList.splice(t,1)},submitUpload(){this.$refs.uploadRef&&this.$refs.uploadRef.submit()}}},o=r,c=i(1656),d=(0,c.A)(o,s,a,!1,null,"84f8d436",null),u=d.exports},2254:function(e,t,i){i.r(t),i.d(t,{default:function(){return _}});var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"equipment-center"},[t("div",{staticClass:"equipment-container"},[t("div",{staticClass:"tree-panel",style:{width:e.treeWidth+"px"}},[t("el-card",{staticClass:"tree-card",attrs:{shadow:"never"}},[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("span",{staticClass:"card-title"},[e._v(e._s(e.$t("equipmentCenter.tree.title")||"设备列表"))]),t("div",{staticClass:"header-actions"},[t("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-plus"},on:{click:e.addGroupDialog}},[e._v(" "+e._s(e.$t("equipmentCenter.button.addGroup")||"新增分组")+" ")])],1)]),t("el-input",{staticClass:"search-input",attrs:{placeholder:e.$t("equipmentCenter.tree.searchPlaceholder")||"输入关键字进行过滤",clearable:"","prefix-icon":"el-icon-search"},model:{value:e.filterText,callback:function(t){e.filterText=t},expression:"filterText"}}),t("el-tree",{ref:"tree",staticClass:"equipment-tree",attrs:{data:e.equipmentData,props:e.defaultProps,"filter-node-method":e.filterNode,"node-key":"id","render-content":e.renderContent,"highlight-current":!0,"empty-text":e.$t("equipmentCenter.tree.noData"),"expand-on-click-node":!1},on:{"node-click":e.handleNodeClick,"node-expand":e.handleNodeExpand}})],1)],1),t("div",{staticClass:"resize-handle",on:{mousedown:e.startResize}}),t("div",{staticClass:"detail-panel",style:{width:"calc(100% - "+(e.treeWidth+8)+"px)"}},[e.selectedDevice?t("div",[e.selectedDevice.isClient?t("div",{staticClass:"device-detail"},[t("el-card",{staticClass:"info-card",attrs:{shadow:"never"}},[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("span",{staticClass:"card-title"},[e._v(e._s(e.$t("equipmentCenter.tree.basicInfo")||"基本信息"))]),t("div",{staticClass:"header-actions"},[t("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-edit"},on:{click:function(t){return e.editDevice(e.selectedDevice)}}},[e._v(" "+e._s(e.$t("equipmentCenter.button.deviceEdit")||"编辑")+" ")]),t("el-button",{attrs:{type:"danger",size:"small",icon:"el-icon-delete"},on:{click:function(t){return e.deleteDevice(e.selectedDevice)}}},[e._v(" "+e._s(e.$t("equipmentCenter.button.delete")||"删除")+" ")])],1)]),t("div",{staticClass:"device-info"},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("div",{staticClass:"info-item"},[t("span",{staticClass:"info-label"},[e._v(e._s(e.$t("equipmentCenter.table.alias_name")||"设备别名")+":")]),t("span",{staticClass:"info-value"},[e._v(e._s(e.selectedDevice.alias_name||"-"))])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"info-item"},[t("span",{staticClass:"info-label"},[e._v(e._s(e.$t("equipmentCenter.form.mac")||"MAC地址")+":")]),t("span",{staticClass:"info-value"},[e._v(e._s(e.selectedDevice.mac_address||"-"))])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"info-item"},[t("span",{staticClass:"info-label"},[e._v(e._s(e.$t("equipmentCenter.table.group_name")||"分组名称")+":")]),t("span",{staticClass:"info-value"},[e._v(e._s(e.selectedDevice.group_name||"-"))])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"info-item"},[t("span",{staticClass:"info-label"},[e._v(e._s(e.$t("equipmentCenter.table.status")||"设备状态")+":")]),t("el-tag",{attrs:{type:e.selectedDevice.isOnline?"success":"danger",size:"small"}},[e._v(" "+e._s(e.selectedDevice.isOnline?e.$t("equipmentCenter.table.online")||"在线":e.$t("equipmentCenter.table.offline")||"离线")+" ")])],1)]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"info-item"},[t("span",{staticClass:"info-label"},[e._v(e._s(e.$t("equipmentCenter.table.ip_addr"))+":")]),t("span",[e._v(e._s(e.selectedDevice.ip_addr||"-"))])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"info-item"},[t("span",{staticClass:"info-label"},[e._v(e._s(e.$t("equipmentCenter.table.updated_at")||"最后上线时间")+":")]),t("span",{staticClass:"info-value"},[e._v(e._s(e.formatTime(e.selectedDevice.updated_at)||"-"))])])])],1)],1)]),t("el-card",{staticClass:"info-card material-card",attrs:{shadow:"never"}},[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("span",{staticClass:"card-title"},[e._v(e._s(e.$t("equipmentCenter.material.title")))]),t("div",{staticClass:"header-actions"},[t("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-upload2"},on:{click:function(t){return e.uploadMaterial(e.selectedDevice)}}},[e._v(" "+e._s(e.$t("equipmentCenter.button.upload"))+" ")]),t("el-button",{attrs:{type:"success",size:"small",icon:"el-icon-refresh"},on:{click:function(t){return e.replaceMaterial(e.selectedDevice)}}},[e._v(" "+e._s(e.$t("equipmentCenter.button.replace"))+" ")]),t("el-button",{attrs:{type:"warning",size:"small",icon:"el-icon-view"},on:{click:function(t){return e.previewDevice(e.selectedDevice)}}},[e._v(" "+e._s(e.$t("equipmentCenter.button.preview"))+" ")])],1)]),t("div",{staticClass:"material-info"},[e.selectedDevice.material?t("div",{staticClass:"material-details"},[t("img",{staticClass:"material-thumbnail",attrs:{src:e.selectedDevice.material.thumbnail,alt:"素材缩略图"}}),t("div",{staticClass:"material-text"},[t("p",{staticClass:"material-name"},[e._v(e._s(e.selectedDevice.material.name))]),t("p",{staticClass:"material-meta"},[e._v("类型: "+e._s(e.selectedDevice.material.type)+" | 大小: "+e._s(e.selectedDevice.material.size))])]),t("div",{staticClass:"material-actions"},[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.editMaterial(e.selectedDevice.material)}}},[e._v("编辑当前素材")]),t("el-button",{staticClass:"danger-text",attrs:{type:"text",size:"small"},on:{click:function(t){return e.unbindMaterial(e.selectedDevice)}}},[e._v("解除关联")])],1)]):t("div",{staticClass:"no-material"},[t("el-empty",{attrs:{description:e.$t("equipmentCenter.material.noMaterial"),"image-size":80}})],1)])])],1):t("div",{staticClass:"group-detail"},[t("el-card",{staticClass:"group-card",attrs:{shadow:"never"}},[t("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[t("span",{staticClass:"card-title"},[e._v("分组详情: "+e._s(e.selectedDevice.label))]),t("div",{staticClass:"header-actions"},[t("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-edit"},on:{click:function(t){return e.editGroupDialog(e.selectedDevice)}}},[e._v(" "+e._s(e.$t("equipmentCenter.button.editGroup")||"编辑分组")+" ")]),t("el-button",{attrs:{type:"danger",size:"small",icon:"el-icon-delete"},on:{click:function(t){return e.deleteGroupDialog(e.selectedDevice)}}},[e._v(" "+e._s(e.$t("equipmentCenter.button.deleteGroup")||"删除分组")+" ")]),t("el-button",{attrs:{type:"success",size:"small",icon:"el-icon-upload"},on:{click:e.handleBatchUpload}},[e._v(" 批量上传 ")]),t("el-button",{attrs:{type:"success",size:"small",icon:"el-icon-s-promotion",disabled:0===e.multipleSelection.length},on:{click:e.batchPush}},[e._v(" 批量推送 ")])],1)]),t("div",{staticClass:"stats-info"},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("div",{staticClass:"stat-item"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.selectedDevice.total||0))]),t("div",{staticClass:"stat-label"},[e._v(e._s(e.$t("equipmentCenter.tree.total")||"设备总数"))])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"stat-item online"},[t("div",{staticClass:"stat-number"},[e._v(e._s(e.selectedDevice.online||0))]),t("div",{staticClass:"stat-label"},[e._v(e._s(e.$t("equipmentCenter.tree.online")||"在线设备"))])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"stat-item offline"},[t("div",{staticClass:"stat-number"},[e._v(e._s((e.selectedDevice.total||0)-(e.selectedDevice.online||0)))]),t("div",{staticClass:"stat-label"},[e._v(e._s(e.$t("equipmentCenter.tree.offline")||"离线设备"))])])])],1)],1),t("div",{staticStyle:{height:"calc(100vh - 400px)","overflow-y":"auto"}},[t("el-table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{data:e.selectedDevice.children},on:{"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{type:"selection",width:"55"}}),t("el-table-column",{attrs:{prop:"alias_name",label:"设备别名",width:"150px","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"mac_address",label:"MAC地址",width:"150px","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{label:"连接状态",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("el-tag",{attrs:{type:i.row.isOnline?"success":"danger",size:"small"}},[e._v(" "+e._s(i.row.isOnline?"在线":"离线")+" ")])]}}])}),t("el-table-column",{attrs:{prop:"status",label:"下发状态","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{label:"操作",width:"150",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("el-button",{attrs:{size:"mini"},on:{click:function(t){return e.editDevice(i.row)}}},[e._v("编辑")]),t("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteDevice(i.row)}}},[e._v("删除")])]}}])})],1)],1)])],1)]):t("div",{staticClass:"empty-state"},[t("el-empty",{attrs:{description:e.$t("equipmentCenter.tree.selectTip")||"请选择左侧设备或分组查看详情","image-size":120},scopedSlots:e._u([{key:"image",fn:function(){return[t("i",{staticClass:"el-icon-s-platform empty-icon"})]},proxy:!0}],null,!1,295858064)})],1)])]),t("el-dialog",{attrs:{title:e.$t("equipmentCenter.dialog.title.edit"),visible:e.isShow,width:"40%","close-on-click-modal":!1},on:{"update:visible":function(t){e.isShow=t},close:e.closeDialog}},[t("el-form",{ref:"deviceInfoForm",attrs:{model:e.deviceInfo,rules:e.rules,"label-width":"120px","label-position":"left"}},[t("el-form-item",{attrs:{label:e.$t("equipmentCenter.table.group_name"),prop:"group_name"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:e.$t("equipmentCenter.form.groupPlaceholder"),filterable:"","allow-create":"","default-first-option":""},model:{value:e.deviceInfo.group_name,callback:function(t){e.$set(e.deviceInfo,"group_name",t)},expression:"deviceInfo.group_name"}},e._l(e.groupNameList,(function(e){return t("el-option",{key:e.id,attrs:{label:"null"==e.name?"无分组":e.name,value:e.name}})})),1)],1),t("el-form-item",{attrs:{label:e.$t("equipmentCenter.table.alias_name"),prop:"alias_name",required:""}},[t("el-input",{attrs:{placeholder:e.$t("equipmentCenter.form.namePlaceholder")},model:{value:e.deviceInfo.alias_name,callback:function(t){e.$set(e.deviceInfo,"alias_name",t)},expression:"deviceInfo.alias_name"}})],1),t("el-form-item",{attrs:{label:e.$t("equipmentCenter.form.mac"),prop:"mac_address"}},[t("el-input",{attrs:{disabled:!0},model:{value:e.deviceInfo.mac_address,callback:function(t){e.$set(e.deviceInfo,"mac_address",t)},expression:"deviceInfo.mac_address"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.isShow=!1}}},[e._v(e._s(e.$t("public.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveDevice()}}},[e._v(e._s(e.$t("public.confirm")))])],1)],1),t("el-dialog",{attrs:{title:"更换素材",visible:e.isMaterialDialogVisible,width:"60%","append-to-body":"","close-on-click-modal":!1},on:{"update:visible":function(t){e.isMaterialDialogVisible=t}}},[t("div",{staticClass:"material-select-dialog"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.isMaterialLoading,expression:"isMaterialLoading"}],staticStyle:{width:"100%"},attrs:{data:e.materials,height:"400px",border:""}},[t("el-table-column",{attrs:{width:"55",align:"center"},scopedSlots:e._u([{key:"default",fn:function(i){return[t("el-radio",{attrs:{label:i.row.id},nativeOn:{change:function(e){return(()=>{}).apply(null,arguments)}},model:{value:e.selectedMaterialId,callback:function(t){e.selectedMaterialId=t},expression:"selectedMaterialId"}},[e._v(" ")])]}}])}),t("el-table-column",{attrs:{label:"缩略图",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[t("el-image",{staticStyle:{width:"80px",height:"80px","border-radius":"4px"},attrs:{src:e.row.thumbnail,"preview-src-list":[e.row.thumbnail],fit:"cover"}},[t("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[t("i",{staticClass:"el-icon-picture-outline"})])])]}}])}),t("el-table-column",{attrs:{prop:"label",label:"素材名称","show-overflow-tooltip":""}}),t("el-table-column",{attrs:{prop:"type",label:"类型",width:"100",align:"center"}}),t("el-table-column",{attrs:{prop:"size",label:"大小",width:"120",align:"center"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.isMaterialDialogVisible=!1}}},[e._v(e._s(e.$t("public.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:e.confirmReplaceMaterial}},[e._v(e._s(e.$t("public.confirm")))])],1)]),t("el-dialog",{attrs:{title:"上传新素材",visible:e.isUploadDialogVisible,width:"50%","append-to-body":"","close-on-click-modal":!1},on:{"update:visible":function(t){e.isUploadDialogVisible=t}}},[e.selectedDevice?t("div",{staticClass:"upload-dialog-content"},[t("p",[e._v("上传的素材将自动与当前设备 "),t("el-tag",{attrs:{size:"small"}},[e._v(e._s(e.selectedDevice.label))]),e._v(" 关联。")],1),t("file-upload",{attrs:{"file-list":e.uploadFileList,uploadNum:1,fileType:"image/video"},on:{editUrl:e.handleUploadSuccess,uploadStatus:e.handleUploadStatus}})],1):e._e(),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.isUploadDialogVisible=!1}}},[e._v("关闭")])],1)]),t("el-dialog",{attrs:{title:"实时预览",visible:e.isPreviewDialogVisible,width:"800px","append-to-body":"","close-on-click-modal":!1},on:{"update:visible":function(t){e.isPreviewDialogVisible=t},close:e.closePreviewDialog}},[t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isRtcConnecting,expression:"isRtcConnecting"}],staticClass:"preview-dialog-content",attrs:{"element-loading-text":"正在建立视频连接..."}},[t("video",{ref:"previewVideo",staticStyle:{width:"100%",height:"450px",background:"#000"},attrs:{autoplay:"",playsinline:""}}),e.isRtcConnecting||e.previewStream?e._e():t("div",{staticClass:"preview-placeholder"},[t("i",{staticClass:"el-icon-video-camera-solid"}),t("p",[e._v("无法加载视频流")])])])]),t("el-dialog",{attrs:{title:"编辑素材信息",visible:e.isEditMaterialDialogVisible,width:"40%","append-to-body":"","close-on-click-modal":!1},on:{"update:visible":function(t){e.isEditMaterialDialogVisible=t}}},[e.editingMaterial?t("el-form",{ref:"materialForm",attrs:{model:e.editingMaterial,rules:e.materialRules,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"素材名称",prop:"name"}},[t("el-input",{attrs:{placeholder:"请输入素材名称"},model:{value:e.editingMaterial.name,callback:function(t){e.$set(e.editingMaterial,"name",t)},expression:"editingMaterial.name"}})],1),t("el-form-item",{attrs:{label:"素材类型",prop:"type"}},[t("el-input",{attrs:{disabled:!0},model:{value:e.editingMaterial.type,callback:function(t){e.$set(e.editingMaterial,"type",t)},expression:"editingMaterial.type"}})],1),t("el-form-item",{attrs:{label:"素材大小",prop:"size"}},[t("el-input",{attrs:{disabled:!0},model:{value:e.editingMaterial.size,callback:function(t){e.$set(e.editingMaterial,"size",t)},expression:"editingMaterial.size"}})],1)],1):e._e(),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.isEditMaterialDialogVisible=!1}}},[e._v("取消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.saveMaterial}},[e._v("保存")])],1)],1),t("el-dialog",{attrs:{title:e.isEditGroup?e.$t("equipmentCenter.dialog.title.editGroup"):e.$t("equipmentCenter.dialog.title.addGroup"),visible:e.isGroupDialogVisible,width:"40%","close-on-click-modal":!1},on:{"update:visible":function(t){e.isGroupDialogVisible=t},close:e.closeGroupDialog}},[t("el-form",{ref:"groupForm",attrs:{model:e.groupForm,rules:e.groupRules,"label-width":"120px","label-position":"left"}},[t("el-form-item",{attrs:{label:e.$t("equipmentCenter.dialog.form.groupName"),prop:"name"}},[t("el-input",{attrs:{placeholder:e.$t("equipmentCenter.dialog.form.groupNamePlaceholder")},model:{value:e.groupForm.name,callback:function(t){e.$set(e.groupForm,"name",t)},expression:"groupForm.name"}})],1),t("el-form-item",{attrs:{label:e.$t("equipmentCenter.dialog.form.groupDescription"),prop:"description"}},[t("el-input",{attrs:{type:"textarea",rows:3,placeholder:e.$t("equipmentCenter.dialog.form.groupDescriptionPlaceholder")},model:{value:e.groupForm.description,callback:function(t){e.$set(e.groupForm,"description",t)},expression:"groupForm.description"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.isGroupDialogVisible=!1}}},[e._v(e._s(e.$t("public.cancel")))]),t("el-button",{attrs:{type:"primary"},on:{click:e.saveGroup}},[e._v(e._s(e.$t("public.confirm")))])],1)],1)],1)},a=[],l=(i(4114),i(7120));function n(e){return(0,l.Ay)({url:"/admin/equipment/getTreeList",method:"get",params:e})}function r(e){return(0,l.Ay)({url:"/admin/equipment/editEquipment",method:"put",data:e})}function o(e){return(0,l.Ay)({url:"/admin/equipment/delete/"+e,method:"delete"})}function c(e){return(0,l.Ay)({url:"/admin/equipment/groupNameList",method:"get",params:e})}function d(e){return(0,l.Ay)({url:"/admin/equipment/addGroup",method:"post",data:e})}function u(e){return(0,l.Ay)({url:"/admin/equipment/editGroup",method:"put",data:e})}function p(e){return(0,l.Ay)({url:`/admin/equipment/deleteGroup/${e}`,method:"delete"})}function m(e){return(0,l.Ay)({url:"/admin/sourcematerial/batchUpload",method:"post",data:e,headers:{"Content-Type":"multipart/form-data"}})}var h=i(4759),g=i(3043),f={name:"EquipmentCenter",components:{FileUpload:g.A},props:["groupName","macAddress"],watch:{filterText(e){this.$refs.tree.filter(e)},"$route.params":{handler:"handleRouteChange",deep:!0}},data(){return{equipmentData:[],defaultProps:{children:"children",label:"label",isClient:"isClient",mac_address:"mac_address"},filterText:"",selectedDevice:null,treeWidth:250,isResizing:!1,deviceInfo:{mac_address:"",alias_name:"",group_name:""},groupNameList:[],rules:{alias_name:[{required:!0,message:this.$t("equipmentCenter.form.namePlaceholder"),trigger:"blur"}]},isShow:!1,isLoading:!1,multipleSelection:[],isMaterialDialogVisible:!1,materials:[],isMaterialLoading:!1,selectedMaterialId:null,isUploadDialogVisible:!1,uploadFileList:[],uploading:!1,isPreviewDialogVisible:!1,rtcPeerConnection:null,previewStream:null,isRtcConnecting:!1,isEditMaterialDialogVisible:!1,editingMaterial:null,materialRules:{name:[{required:!0,message:"请输入素材名称",trigger:"blur"}]},isGroupDialogVisible:!1,isEditGroup:!1,groupForm:{id:null,name:"",description:""},groupRules:{name:[{required:!0,message:this.$t("equipmentCenter.dialog.message.groupNameRequired"),trigger:"blur"}]}}},created(){this.getList()},methods:{getList(){this.isLoading=!0,n().then((e=>{0==e.code&&(this.equipmentData=e.data,this.handleRouteChange())})).finally((()=>{this.isLoading=!1}))},handleRouteChange(){this.$nextTick((()=>{if(!this.$refs.tree||!this.equipmentData||0===this.equipmentData.length)return;const{groupName:e,macAddress:t}=this.$route.params;if(this.selectedDevice&&(this.$refs.tree.setCurrentKey(null),this.selectedDevice=null),t){const e=this.findNodeByMac(this.equipmentData,t);if(e){this.selectedDevice=e,this.$refs.tree.setCurrentKey(e.id);const t=this.findNode(this.equipmentData,(t=>t.children&&t.children.some((t=>t.id===e.id))));t&&this.$refs.tree.store.nodesMap[t.id]&&this.$refs.tree.store.nodesMap[t.id].expand()}}else if(e){const t=this.findNodeByLabel(this.equipmentData,e);t&&(this.selectedDevice=t,this.$refs.tree.setCurrentKey(t.id),this.$refs.tree.store.nodesMap[t.id]&&this.$refs.tree.store.nodesMap[t.id].expand())}}))},findNodeById(e,t){for(const i of e){if(i.id==t)return i;if(i.children){const e=this.findNodeById(i.children,t);if(e)return e}}return null},findNode(e,t){for(const i of e){if(t(i))return i;if(i.children){const e=this.findNode(i.children,t);if(e)return e}}return null},findNodeByLabel(e,t){return this.findNode(e,(e=>!e.isClient&&e.label===t))},findNodeByMac(e,t){return this.findNode(e,(e=>e.isClient&&e.mac_address===t))},handleNodeClick(e,t){let i=null;if(e.isClient){let e=t.parent;while(e&&e.data&&e.data.isClient)e=e.parent;e&&e.data&&(i=e.data.label)}else i=e.label;const s=e.isClient?e.mac_address:void 0,a={};i&&(a.groupName=i),s&&(a.macAddress=s),this.$route.params.groupName===a.groupName&&this.$route.params.macAddress===a.macAddress||this.$router.push({name:"EquipmentCenter",params:a}),this.selectedDevice=e,this.selectedDevice.isClient&&!this.selectedDevice.material&&(this.selectedDevice={...this.selectedDevice,material:{id:"mat-001",name:"默认宣传视频.mp4",thumbnail:"https://shadow.elemecdn.com/app/element/hamburger.9cf7b091-55e9-11e9-a976-7f4d0b07eef6.png",type:"视频",size:"15.8MB"}}),e.isClient?this.$refs.tree.setCurrentKey(e.id):t.expanded?this.$refs.tree.store?.nodesMap[e.id]?.collapse():this.$refs.tree.store?.nodesMap[e.id]?.expand()},filterNode(e,t){if(!e)return!0;const i=e.toLowerCase();return t.label&&-1!==t.label.toLowerCase().indexOf(i)||t.alias_name&&-1!==t.alias_name.toLowerCase().indexOf(i)||t.mac_address&&-1!==t.mac_address.toLowerCase().indexOf(i)},renderContent(e,{node:t,data:i}){const s=i.isClient;return e("span",{class:"custom-tree-node"},[e("span",{class:"node-content"},[e("i",{class:[s?"el-icon-monitor":t.expanded?"el-icon-folder-opened":"el-icon-folder","node-icon",{"online-icon":s&&i.isOnline,"offline-icon":s&&!i.isOnline}]}),e("span",{class:"node-label"},"null"==t.label?this.$t("equipmentCenter.tree.null"):t.label),s?e("el-tag",{props:{size:"mini",type:i.isOnline?"success":"danger"},class:"status-tag"},i.isOnline?this.$t("equipmentCenter.tree.online"):this.$t("equipmentCenter.tree.offline")):null]),s?null:e("span",{class:"node-extra"},[e("span",{class:"device-count"},[e("span",{class:"online-count"},i.online||0)," / ",e("span",{class:"total-count"},i.total||0)])])])},uploadMaterial(e){this.uploadFileList=[],this.isUploadDialogVisible=!0},handleUploadSuccess(e){console.log(`上传文件 ${e.name} 成功`);const t={id:`mat-${(new Date).getTime()}`,name:e.name,thumbnail:e.url.startsWith("http")?e.url:"https://via.placeholder.com/100",url:e.url,type:this.getFileType(e.name),size:"N/A"};console.log(`将设备 ${this.selectedDevice.id} 与新上传的素材 ${t.id} 关联`),this.$message.success("上传成功并已关联到当前设备"),this.selectedDevice.material=t,this.isUploadDialogVisible=!1,this.fetchMaterials()},getFileType(e){const t=e.split(".").pop().toLowerCase();return["jpg","jpeg","png","gif"].includes(t)?"图片":["mp4","mov","avi"].includes(t)?"视频":"文件"},handleUploadStatus(e){this.uploading=e},replaceMaterial(e){this.selectedMaterialId=e.material?e.material.id:null,this.isMaterialDialogVisible=!0,this.fetchMaterials()},fetchMaterials(){this.isMaterialLoading=!0,(0,h.vS)({page:1,limit:1e3}).then((e=>{0===e.code?this.materials=(e.data.list||e.data).map((e=>({...e,id:e.id,label:e.label||e.name,thumbnail:e.thumbnail||"https://via.placeholder.com/100"}))):this.$message.error("获取素材列表失败")})).catch((()=>{this.$message.error("获取素材列表失败")})).finally((()=>{this.isMaterialLoading=!1}))},confirmReplaceMaterial(){if(!this.selectedMaterialId)return void this.$message.warning("请选择一个素材");const e=this.materials.find((e=>e.id===this.selectedMaterialId));console.log(`将设备 ${this.selectedDevice.id} 与素材 ${this.selectedMaterialId} 关联`),this.$message.success(`操作成功，设备已关联素材: ${e.label}`),this.selectedDevice.material||(this.selectedDevice.material={}),this.selectedDevice.material.name=e.label,this.selectedDevice.material.id=e.id,this.selectedDevice.material.thumbnail=e.thumbnail,this.selectedDevice.material.type=e.type,this.selectedDevice.material.size=e.size,this.isMaterialDialogVisible=!1},previewDevice(e){this.isPreviewDialogVisible=!0,this.isRtcConnecting=!0,this.$nextTick((()=>{this.initWebRTC(e)}))},initWebRTC(e){console.log(`[WebRTC] 开始为设备 [${e.label}] 初始化连接...`);const t={iceServers:[{urls:"stun:stun.l.google.com:19302"}]};this.rtcPeerConnection=new RTCPeerConnection(t),this.rtcPeerConnection.onicecandidate=e=>{e.candidate&&console.log("[WebRTC] 发现新的 ICE 候选:",e.candidate)},this.rtcPeerConnection.ontrack=e=>{console.log("[WebRTC] 接收到远程视频流"),this.isRtcConnecting=!1,this.$refs.previewVideo&&(this.$refs.previewVideo.srcObject=e.streams[0],this.previewStream=e.streams[0])},this.createMockOfferAndAnswer()},createMockOfferAndAnswer(){console.log("[WebRTC] 正在创建模拟 Offer..."),this.rtcPeerConnection.createOffer({offerToReceiveVideo:!0}).then((e=>(console.log("[WebRTC] 模拟 Offer 创建成功:",e),this.rtcPeerConnection.setLocalDescription(e)))).then((()=>{console.log("[WebRTC] 模拟: LocalDescription 设置成功，等待远端 Answer..."),setTimeout((()=>{const e={type:"answer",sdp:"..."};console.log("[WebRTC] 模拟: 收到远端 Answer:",e),console.warn("[WebRTC] 模拟: setRemoteDescription 已被注释，因为没有真实的Answer。在真实场景中需要取消注释。"),setTimeout((()=>{this.isRtcConnecting&&(this.$message.error("实时预览连接超时（模拟）"),this.isRtcConnecting=!1)}),5e3)}),2e3)})).catch((e=>{console.error("[WebRTC] Offer 创建失败:",e),this.$message.error("WebRTC Offer 创建失败"),this.isRtcConnecting=!1}))},closePreviewDialog(){console.log("[WebRTC] 关闭预览窗口，清理资源..."),this.rtcPeerConnection&&(this.rtcPeerConnection.close(),this.rtcPeerConnection=null),this.previewStream&&(this.previewStream.getTracks().forEach((e=>e.stop())),this.previewStream=null),this.isPreviewDialogVisible=!1,this.isRtcConnecting=!1},editMaterial(e){this.editingMaterial=JSON.parse(JSON.stringify(e)),this.isEditMaterialDialogVisible=!0},saveMaterial(){this.$refs.materialForm.validate((e=>{e&&(0,h.hc)(this.editingMaterial,this.editingMaterial.id).then((()=>{this.$message.success("素材信息更新成功"),this.selectedDevice.material={...this.editingMaterial},this.isEditMaterialDialogVisible=!1,this.fetchMaterials()})).catch((()=>{this.$message.error("素材信息更新失败")}))}))},unbindMaterial(e){this.$confirm(`确定要解除设备 [${e.label}] 与其素材的关联吗?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{console.log(`调用API解除设备 ${e.id} 的素材关联`),this.$message.success("解除关联成功"),this.selectedDevice.material=null})).catch((()=>{}))},addGroupDialog(){this.isEditGroup=!1,this.groupForm={id:null,name:"",description:""},this.isGroupDialogVisible=!0},editGroupDialog(e){this.isEditGroup=!0,this.groupForm={id:e.id,name:e.label,description:e.description||""},this.isGroupDialogVisible=!0},saveGroup(){this.$refs.groupForm.validate((e=>{if(e){const e=this.isEditGroup?u:d,t={name:this.groupForm.name,description:this.groupForm.description};this.isEditGroup&&(t.id=this.groupForm.id),e(t).then((()=>{this.$message.success(this.isEditGroup?this.$t("equipmentCenter.dialog.message.groupEditSuccess"):this.$t("equipmentCenter.dialog.message.groupAddSuccess")),this.isGroupDialogVisible=!1,this.getList()})).catch((()=>{this.$message.error("操作失败，请稍后重试")}))}}))},deleteGroupDialog(e){1!=e.group_id?e.children?.length>0?this.$message.error("请先删除分组下的所有设备"):this.$confirm(this.$t("equipmentCenter.dialog.message.deleteGroupWarning"),this.$t("equipmentCenter.dialog.message.deleteGroupConfirm"),{confirmButtonText:this.$t("public.confirm"),cancelButtonText:this.$t("public.cancel"),type:"warning"}).then((()=>{p(e.group_id).then((()=>{this.$message.success(this.$t("equipmentCenter.dialog.message.groupDeleteSuccess")),this.selectedDevice=null,this.getList()})).catch((()=>{this.$message.error("删除失败，请稍后重试")}))})).catch((()=>{})):this.$message.error(this.$t("equipmentCenter.dialog.message.defaultGroupDelete"))},closeGroupDialog(){this.$refs.groupForm.resetFields(),this.groupForm={id:null,name:"",description:""}},editDevice(e){e&&e.isClient?(this.deviceInfo={...e},this.isShow=!0,this.fetchGroupNameList()):this.$message.warning("无效的设备")},fetchGroupNameList(){c().then((e=>{0==e.code&&(this.groupNameList=e.data)}))},deleteDevice(e){e&&e.isClient?this.$confirm(`确定删除设备: ${e.label}?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{o(e.id).then((()=>{this.$message({type:"success",message:"删除成功"}),this.selectedDevice&&this.selectedDevice.id===e.id&&(this.selectedDevice=null),this.getList()})).catch((()=>{this.$message({type:"error",message:"删除失败"})}))})).catch((()=>{})):this.$message.warning("无效的设备")},saveDevice(){this.$refs.deviceInfoForm.validate((e=>{e&&r(this.deviceInfo).then((()=>{this.$message({type:"success",message:"保存成功"}),this.isShow=!1,this.getList()})).catch((()=>{this.$message({type:"error",message:"保存失败"})}))}))},closeDialog(){this.$refs.deviceInfoForm.resetFields(),this.deviceInfo={mac_address:"",alias_name:"",group_name:""}},handleSelectionChange(e){this.multipleSelection=e},batchPush(){if(0===this.multipleSelection.length)return void this.$message.warning("请至少选择一个设备");const e=this.multipleSelection.map((e=>e.label)).join(", ");this.$message.success(`准备向以下设备进行批量推送: ${e}`),console.log("批量推送的设备:",this.multipleSelection)},formatTime(e){return e?this.$formatTimeStamp?this.$formatTimeStamp(e):new Date(e).toLocaleString():"-"},handleNodeExpand(e,t){const i=this.$refs.tree.store.nodesMap,s=[];for(const a in i)i[a].expanded&&s.push(i[a].data.label);this.$route.params.groupName!==e.label&&this.$router.push({name:"EquipmentCenter",params:{groupName:e.label}})},startResize(e){this.isResizing=!0;const t=e.clientX,i=this.treeWidth,s=e=>{if(!this.isResizing)return;const s=e.clientX-t,a=i+s;a>=150&&a<=500&&(this.treeWidth=a)},a=()=>{this.isResizing=!1,document.removeEventListener("mousemove",s),document.removeEventListener("mouseup",a),document.body.style.cursor="",document.body.style.userSelect=""};document.addEventListener("mousemove",s),document.addEventListener("mouseup",a),document.body.style.cursor="col-resize",document.body.style.userSelect="none"},handleBatchUpload(){if(!this.selectedDevice||this.selectedDevice.isClient)return void this.$message.warning("请先选择一个设备分组");const e=document.createElement("input");e.type="file",e.webkitdirectory=!0,e.onchange=e=>{const t=e.target.files;0!==t.length&&this.uploadFolder(t)},e.click()},async uploadFolder(e){const t=new FormData;t.append("groupId",this.selectedDevice.group_id);for(let s=0;s<e.length;s++)t.append("files",e[s]);this.isLoading=!0;try{const e=await m(t);0===e.code?(this.$message.success("文件上传成功，正在处理分配..."),this.getList()):this.$message.error(e.msg||"上传失败")}catch(i){this.$message.error("上传过程中发生错误")}finally{this.isLoading=!1}}}},v=f,b=i(1656),C=(0,b.A)(v,s,a,!1,null,"11baf1ba",null),_=C.exports},4759:function(e,t,i){i.d(t,{WQ:function(){return l},hc:function(){return n},vS:function(){return a},yH:function(){return r}});var s=i(7120);function a(e){return(0,s.Ay)({url:"/admin/sourcematerial/getList",method:"get",params:e})}function l(e){return(0,s.Ay)({url:"/admin/sourcematerial/addSourceMaterial",method:"post",data:e})}function n(e,t){return(0,s.Ay)({url:"/admin/sourcematerial/editSourceMaterial/"+t,method:"put",data:e})}function r(e){return(0,s.Ay)({url:"/admin/sourcematerial/delete/"+e,method:"delete"})}}}]);
//# sourceMappingURL=254.94d14f03.js.map