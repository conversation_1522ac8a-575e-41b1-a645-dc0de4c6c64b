{"version": 3, "file": "static/js/726.777044ff.js", "mappings": "2JAAA,IAAIA,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACA,EAAG,YAAY,CAACE,IAAI,YAAYC,YAAY,mBAAmBC,MAAMN,EAAIO,KAAKC,MAAM,CAAC,OAASR,EAAIS,YAAY,KAAO,aAAa,QAAUT,EAAIU,QAAQ,SAAWV,EAAIW,UAAY,EAAiB,kBAAiB,EAAM,gBAAgBX,EAAIY,aAAa,YAAYZ,EAAIa,aAAa,cAAcb,EAAIc,qBAAqB,aAAad,EAAIe,oBAAoB,UAAYf,EAAIgB,WAAW,OAAShB,EAAIiB,kBAAkBf,EAAG,MAAM,CAACG,YAAY,UAAU,CAACL,EAAIkB,GAAIlB,EAAImB,UAAU,SAASC,EAAKC,GAAO,OAAOnB,EAAG,MAAM,CAACoB,IAAID,EAAMhB,YAAY,QAAQ,CAACH,EAAG,MAAM,CAACG,YAAY,MAAMG,MAAM,CAAC,GAAK,gBAAgBe,GAAG,CAAC,UAAY,SAASC,GAAQJ,EAAKK,OAAQ,CAAI,EAAE,SAAW,SAASD,GAAQJ,EAAKK,OAAQ,CAAK,IAAI,CAAEzB,EAAI0B,WAAWN,EAAKO,KAAMzB,EAAG,WAAW,CAACE,IAAI,MAAMwB,UAAS,EAAKC,YAAY,CAAC,MAAQ,QAAQ,OAAS,SAASrB,MAAM,CAAC,IAAMR,EAAI8B,SAAWV,EAAKO,IAAI,mBAAmB3B,EAAI+B,gBAAgB,IAAM,SAASR,GAAG,CAAC,MAAQ,SAASC,GAAiC,OAAzBA,EAAOQ,kBAAyBhC,EAAIiC,gBAAgBC,MAAM,KAAMC,UAAU,KAAMnC,EAAIoC,WAAWhB,EAAKO,KAAMzB,EAAG,QAAQ,CAAC2B,YAAY,CAAC,MAAQ,QAAQ,OAAS,QAAQ,QAAU,SAASrB,MAAM,CAAC,SAAW,KAAK,CAACN,EAAG,SAAS,CAACM,MAAM,CAAC,IAAMR,EAAI8B,SAAWV,EAAKO,IAAI,KAAO,iBAAiBzB,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,IAAI,CAACI,MAAMN,EAAIqC,iBAAiBjB,EAAKO,OAAOzB,EAAG,MAAM,CAACG,YAAY,aAAa,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGnB,EAAKb,MAAQP,EAAIwC,mBAAmBpB,EAAKO,WAAWzB,EAAG,MAAM,CAACuC,WAAW,CAAC,CAAClC,KAAK,OAAOmC,QAAQ,SAASC,MAAOvB,EAAKK,MAAOmB,WAAW,eAAevC,YAAY,QAAQ,CAAEL,EAAI6C,WAAWzB,EAAKO,KAAMzB,EAAG,IAAI,CAACG,YAAY,kBAAkBkB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAI8C,YAAY1B,EAAK,KAAKlB,EAAG,IAAI,CAACG,YAAY,mBAAmBkB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAI+C,aAAa3B,EAAKO,IAAI,KAAO3B,EAAIgB,WAAoHhB,EAAIgD,KAA5G9C,EAAG,IAAI,CAACG,YAAY,kBAAkBkB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAIiD,YAAY7B,EAAKO,IAAI,KAAgB3B,EAAIgB,WAAmHhB,EAAIgD,KAA3G9C,EAAG,IAAI,CAACG,YAAY,iBAAiBkB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAIkD,YAAY9B,EAAKO,IAAI,QAAiB,IAAI,IAAI3B,EAAImB,SAASgC,OAASnD,EAAIW,UAAWT,EAAG,MAAM,CAACG,YAAY,MAAMkB,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAIiD,aAAa,IAAI,CAAC/C,EAAG,IAAI,CAACG,YAAY,mBAAmBL,EAAIgD,MAAM,IAAI,EACpuE,EACII,EAAkB,G,8DCqCtB,GACAC,MAAA,CACArC,WAAAsC,QACAC,SAAAC,OACAjD,KAAAiD,OACAvC,eAAA,CACAwC,KAAAD,OACAE,QAAA,IAEAvC,SAAAwC,MACAhD,UAAA,CACA8C,KAAAG,OACAF,QAAA,GAEArC,MAAA,CACAoC,KAAAG,OACAF,QAAA,IAGAG,IAAAA,GACA,OAIApD,YAAAqD,+BACAhC,SAAAgC,iBACApD,QAAA,CACAqD,cAAAC,EAAAA,EAAAC,QAAAC,OAGAC,UAAA,GACAC,eAAA,EACAC,cAAA,GACAC,KAAA,GAEA,EACAC,SAAA,CACAC,aAAA,CACAC,IAAA,WACA,YAAAtD,UAAA,KAAAA,SAAAgC,OAAA,CACA,EACAuB,IAAA,SAAAC,GAAA,IAGAC,QAAA,CACAC,SAAAA,CAAAC,GACA,KAAAC,MAAA,QAAAD,EACA,EACA7C,eAAAA,GACA+C,YAAA,KACA,IAAAC,EAAAC,SAAAC,cAAA,6BACAF,GAEAA,EAAAG,iBAAA,SAAAC,IACA,mCAAAA,EAAAC,OAAAC,WAAAC,WAGAN,SAAAC,cAAA,2BAAAM,OAAA,GACA,GACA,IACA,EACA5E,YAAAA,CAAA6E,EAAAvE,GACA,IAAAA,EAAAgC,OACA,KAAAhC,SAAA,GAEA,KAAAA,SAAAA,CAEA,EACAwE,OAAAA,GACA,6CAAAC,QAAA,SAAAC,IACA,MAAAA,EAAA,GAAAC,KAAAC,SAAA,KAAAC,SAAA,KAEA,EACApF,YAAAA,CAAA8E,GAEA,MAAAO,EAAA,KAAAC,cAAAR,GAEA,IAAAO,EAEA,OADA,KAAAE,kBAAAT,IACA,EAIA,GADA,KAAApB,KAAA8B,KAAAV,EAAAnF,MACA,KAAA+D,KAAAnB,OAAA,KAAAxC,UAGA,OAFA,KAAA0F,SAAAC,QAAA,KAAAC,MAAAC,EAAA,uBAAAC,MAAA,KAAA9F,aACA,KAAA+F,oBACA,EAEA,GAAAhB,EAAAjC,KAAAkD,SAAA,UAEA,IAAAC,EAAA,IAAAC,WACAD,EAAAE,OAAAzB,IACA,IAAA0B,EAAA1B,EAAAC,OAAA0B,OACAC,EAAA/B,SAAAgC,cAAA,OACAD,EAAAE,IAAAJ,EACAE,EAAAH,OAAA,KACA,KAAA/B,MACA,WACA,CACAqC,MAAAH,EAAAG,MACAC,OAAAJ,EAAAI,SAKAC,QAAAC,IAAA,MAAAN,EAAAG,OACAE,QAAAC,IAAA,MAAAN,EAAAI,OAAA,CACA,EAEAT,EAAAY,cAAA9B,EAEA,MAEA,GAAAA,EAAAjC,KAAAkD,SAAA,UACAW,QAAAC,IAAA7B,EAAA,QACA,MAAA+B,EAAAvC,SAAAgC,cAAA,SACAO,EAAAN,IAAAO,IAAAC,gBAAAjC,GACA+B,EAAArC,iBAAA,uBACA,KAAAL,MACA,WACA,CACAqC,MAAAK,EAAAG,WACAP,OAAAI,EAAAI,cAIAP,QAAAC,IAAA,iBAAAE,EAAAG,WACAH,EAAAI,YAAA,GAGA,CAGA,QACA,EACA3B,aAAAA,CAAAR,GAEA,GADA4B,QAAAC,IAAA,KAAAhE,UACA,eAAAA,SACA,OAAAmC,EAAAjC,KAAAkD,SAAA,SACA,kBAAApD,SACA,OAAAmC,EAAAjC,KAAAkD,SAAA,SACA,wBAAApD,SACA,OAAAmC,EAAAjC,KAAAkD,SAAA,UAAAjB,EAAAjC,KAAAkD,SAAA,SACA,iBAAApD,SAAA,CAEA,MAAAuE,EAAA,KAAA7G,eAAA8G,MAAA,KAAAC,KAAA5G,GAAAA,EAAA6G,SACA,OAAAH,EAAAnB,SAAAjB,EAAAjC,KACA,CACA,QACA,EACA0C,iBAAAA,CAAAT,GACA,MAAAwC,EAAA,KAAAC,oBACA,KAAA9B,SAAAC,QAAA4B,GACA,KAAAxB,kBACA,EACAyB,iBAAAA,GACA,YAAA5E,UACA,YACA,YAAAgD,MAAAC,EAAA,oBACA,YACA,YAAAD,MAAAC,EAAA,oBACA,kBACA,YAAAD,MAAAC,EAAA,gCACA,UACA,YAAAD,MAAAC,EAAA,gCACA,WACA,YAAAD,MAAAC,EAAA,iCACA,QACA,SAEA,EACAE,gBAAAA,GACA,KAAA0B,MAAAC,UAAAC,aACA,KAAAhE,KAAA,GACA,KAAAnD,SAAA,SAAAA,SACA,EACAL,oBAAAA,GACA,KAAAiE,MAAA,kBACA,EACAhE,mBAAAA,CAAAwH,EAAA7C,EAAAvE,GACA,KAAA4D,MAAA,mBACA,KAAA5D,SAAAiF,KAAA,CACA7F,KAAAmF,EAAAnF,KACAkB,OAAA,EACAE,IAAA+D,EAAA8C,SAAA3E,KAAA4E,YAGA,KAAA1D,MACA,UACA,CACAxE,KAAAmF,EAAAnF,KACAkB,OAAA,EACAE,IAAA+D,EAAA8C,SAAA3E,KAAA4E,WAEA,KAAAlI,KACA,KAAAc,MAEA,EACAU,aAAAA,GACA,IAAA2G,EAAA,GASA,OARA,KAAAvH,UAAA,KAAAA,SAAAgC,OAAA,GACA,KAAAhC,SAAAwH,SAAAvH,IACA,QAAAM,WAAAN,EAAAO,KAAA,CACA,MAAAiH,EAAA,KAAA9G,SAAAV,EAAAO,IACA+G,EAAAtC,KAAAwC,EACA,KAGAF,CACA,EACAG,YAAAA,CAAAlH,GACA,IAAA+G,EAAA,GACA,KAAAvH,SAAAwH,SAAAvH,IACA,KAAAM,WAAAN,EAAAO,MACA+G,EAAAtC,KAAAhF,EACA,IAEA,IAAAC,GAAAyH,EAAAA,EAAAA,IAAAJ,EAAA,MAAA/G,IACA,IAAAN,GAAA,KAAA+G,MAAAnB,KAAA,KAAAmB,MAAAnB,IAAA5F,KACA,KAAA+G,MAAAnB,IAAA5F,GAAA0H,YAAA,EAEA,EACAC,YAAAA,CAAArH,GACA,EAEAmB,WAAAA,CAAA1B,GACA,KAAAM,WAAAN,EAAAO,KACA,KAAAkH,aAAAzH,EAAAO,KACA,KAAAS,WAAAhB,EAAAO,KACA,KAAAqH,aAAA5H,EAAAO,KAGAsH,OAAAC,KAAA,KAAApH,SAAAV,EAAAO,IAAA,SAEA,EACAD,UAAAA,CAAAC,GACA,IAAAA,EAAA,SACA,MAAAwH,EAAAxH,EAAAyH,UAAAzH,EAAA0H,YAAA,QAAAC,cACAC,EAAA,kCACA,OAAAA,EAAA5C,SAAAwC,EACA,EACA/G,UAAAA,CAAAT,GACA,IAAAA,EAAA,SACA,MAAA6H,EAAA,2BACAC,EAAA9H,EAAAyH,UAAAzH,EAAA0H,YAAA,QAAAC,cACA,OAAAE,EAAA7C,SAAA8C,EACA,EACApH,gBAAAA,CAAAV,GACA,IAAAA,EAAA,yBACA,MAAA+H,EAAA/H,EAAAyH,UAAAzH,EAAA0H,YAAA,QAAAC,cACA,OAAAI,GACA,UACA,6BACA,UACA,WACA,yBACA,UACA,WACA,yBACA,UACA,WACA,yBACA,QACA,yBAEA,EACAlH,kBAAAA,CAAAb,GACA,OAAAA,EACAA,EAAAoG,MAAA,KAAA4B,MADA,EAEA,EACA9G,UAAAA,CAAAlB,GACA,YAAAD,WAAAC,IAAA,KAAAS,WAAAT,EACA,EACAoB,YAAAA,CAAApB,GACAsH,OAAAC,KAAA,KAAApH,SAAAH,EAAA,SACA,EACAsB,WAAAA,CAAA2G,GACA,KAAAtF,KAAA,GACA,KAAAD,cAAAuF,EAEA,KAAAxB,MAAAC,WACA,KAAAD,MAAAC,UAAAwB,IAAA1E,cAAA,SAAAM,OAEA,EACAvC,WAAAA,CAAAvB,GACA,MAAAN,GAAAyH,EAAAA,EAAAA,IAAA,KAAA3H,SAAA,MAAAQ,IACA,IAAAN,GACA,KAAAF,SAAA2I,OAAAzI,EAAA,EAEA,EACA0I,YAAAA,GACA,KAAA3B,MAAAC,WACA,KAAAD,MAAAC,UAAA2B,QAEA,IC7U0P,I,UCQtPC,GAAY,OACd,EACAlK,EACAqD,GACA,EACA,KACA,WACA,MAIF,EAAe6G,EAAiB,O,oECnBhC,IAAIlK,EAAS,WAAkB,IAAIC,EAAIC,KAAKC,EAAGF,EAAIG,MAAMD,GAAG,OAAOA,EAAG,MAAM,CAACG,YAAY,sBAAsB,CAACH,EAAG,UAAU,CAACE,IAAI,OAAOC,YAAY,gBAAgBG,MAAM,CAAC,MAAQR,EAAIkK,KAAK,cAAc,OAAO,iBAAiB,SAAS,CAAChK,EAAG,SAAS,CAACM,MAAM,CAAC,OAAS,KAAK,CAACN,EAAG,SAAS,CAACM,MAAM,CAAC,KAAO,IAAI,CAACN,EAAG,eAAe,CAACM,MAAM,CAAC,MAAQR,EAAImK,GAAG,sBAAsB,KAAO,SAAS,CAACjK,EAAG,WAAW,CAACM,MAAM,CAAC,YAAcR,EAAImK,GAAG,yCAAyCC,MAAM,CAACzH,MAAO3C,EAAIkK,KAAK3J,KAAM8J,SAAS,SAAUC,GAAMtK,EAAIuK,KAAKvK,EAAIkK,KAAM,OAAQI,EAAI,EAAE1H,WAAW,gBAAgB,IAAI,GAAG1C,EAAG,SAAS,CAACM,MAAM,CAAC,KAAO,IAAI,CAACN,EAAG,eAAe,CAACM,MAAM,CAAC,MAAQR,EAAImK,GAAG,6BAA6B,KAAO,SAAS,CAACjK,EAAG,iBAAiB,CAACM,MAAM,CAAC,eAAe,YAAY,KAAO,YAAY,kBAAkBR,EAAImK,GAAG,aAAa,oBAAoBnK,EAAImK,GAAG,oBAAoB,kBAAkBnK,EAAImK,GAAG,mBAAmBC,MAAM,CAACzH,MAAO3C,EAAIkK,KAAKM,KAAMH,SAAS,SAAUC,GAAMtK,EAAIuK,KAAKvK,EAAIkK,KAAM,OAAQI,EAAI,EAAE1H,WAAW,gBAAgB,IAAI,IAAI,GAAG1C,EAAG,SAAS,CAACM,MAAM,CAAC,KAAO,OAAO,QAAU,kBAAkB,CAACN,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,UAAU,KAAO,eAAe,KAAO,QAAQe,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAIyK,KAAK,IAAI,CAACzK,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAImK,GAAG,2BAA2BjK,EAAG,SAAS,CAACM,MAAM,CAAC,KAAO,IAAI,CAACN,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,QAAQe,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAI0K,UAAU,OAAQ,UAAU,IAAI,CAAC1K,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAImK,GAAG,oBAAoBjK,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,UAAU,KAAO,QAAQe,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAI2K,YAAY,IAAI,CAAC3K,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAImK,GAAG,sBAAsB,IAAI,IAAI,GAAGjK,EAAG,MAAM,CAAC2B,YAAY,CAAC,OAAS,OAAO,mBAAmB,OAAO,OAAS,WAAW,CAAC3B,EAAG,WAAW,CAACuC,WAAW,CAAC,CAAClC,KAAK,UAAUmC,QAAQ,YAAYC,MAAO3C,EAAI4K,UAAWhI,WAAW,cAAcf,YAAY,CAAC,MAAQ,QAAQrB,MAAM,CAAC,KAAOR,EAAI6K,SAAS,OAAS,GAAG,OAAS,SAAS,CAAC3K,EAAG,kBAAkB,CAACM,MAAM,CAAC,KAAO,MAAM,MAAQR,EAAImK,GAAG,sBAAsB,MAAQ,MAAM,MAAQ,UAAUW,YAAY9K,EAAI+K,GAAG,CAAC,CAACzJ,IAAI,UAAU0J,GAAG,SAASC,GAAO,MAAO,CAACjL,EAAIsC,GAAG,IAAItC,EAAIuC,GAAG0I,EAAMC,OAAS,GAAG,KAAK,OAAOhL,EAAG,kBAAkB,CAACM,MAAM,CAAC,KAAO,OAAO,MAAQR,EAAImK,GAAG,uBAAuB,MAAQ,MAAM,MAAQ,YAAYjK,EAAG,kBAAkB,CAACM,MAAM,CAAC,KAAO,OAAO,MAAQR,EAAImK,GAAG,uBAAuB,MAAQ,UAAUW,YAAY9K,EAAI+K,GAAG,CAAC,CAACzJ,IAAI,UAAU0J,GAAG,SAASC,GAAO,MAAO,CAACjL,EAAIsC,GAAG,IAAItC,EAAIuC,GAAGvC,EAAImL,YAAYF,EAAMG,IAAI3H,OAAO,KAAK,OAAOvD,EAAG,kBAAkB,CAACM,MAAM,CAAC,KAAO,OAAO,MAAQR,EAAImK,GAAG,0BAA0B,MAAQ,UAAUW,YAAY9K,EAAI+K,GAAG,CAAC,CAACzJ,IAAI,UAAU0J,GAAG,SAASC,GAAO,MAAO,CAAoB,GAAlBA,EAAMG,IAAI3H,MAAawH,EAAMG,IAAIC,KAAMnL,EAAG,WAAW,CAACG,YAAY,MAAMG,MAAM,CAAC,IAAMR,EAAI8B,SAAWmJ,EAAMG,IAAIC,KAAK,mBAAmB,CAACrL,EAAI8B,SAAWmJ,EAAMG,IAAIC,MAAM,IAAM,WAA8B,GAAlBJ,EAAMG,IAAI3H,MAAawH,EAAMG,IAAIC,KAAMnL,EAAG,QAAQ,CAACG,YAAY,MAAMG,MAAM,CAAC,IAAMR,EAAI8B,SAAWmJ,EAAMG,IAAIC,KAAK,SAAW,MAAyB,GAAlBJ,EAAMG,IAAI3H,MAAawH,EAAMG,IAAIC,KAAMnL,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACH,EAAG,IAAI,CAACI,MAAMN,EAAIqC,iBAAiB4I,EAAMG,IAAIC,QAAQnL,EAAG,MAAM,CAACG,YAAY,aAAa,CAACL,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAIsL,YAAYL,EAAMG,IAAIC,YAAYnL,EAAG,MAAM,CAACF,EAAIsC,GAAG,QAAQ,OAAOpC,EAAG,kBAAkB,CAACM,MAAM,CAAC,KAAO,aAAa,MAAQR,EAAImK,GAAG,6BAA6B,MAAQ,UAAUW,YAAY9K,EAAI+K,GAAG,CAAC,CAACzJ,IAAI,UAAU0J,GAAG,SAASC,GAAO,MAAO,CAACjL,EAAIsC,GAAG,IAAItC,EAAIuC,GAAGvC,EAAIuL,iBAAiBN,EAAMG,IAAII,aAAa,KAAK,OAAOtL,EAAG,kBAAkB,CAACM,MAAM,CAAC,MAAQR,EAAImK,GAAG,4BAA4B,MAAQ,QAAQ,MAAQ,UAAUW,YAAY9K,EAAI+K,GAAG,CAAC,CAACzJ,IAAI,UAAU0J,GAAG,SAASC,GAAO,MAAO,CAAC/K,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,OAAO,KAAO,SAASe,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAIyL,KAAKR,EAAMG,IAAI,IAAI,CAACpL,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAImK,GAAG,mBAAmBjK,EAAG,gBAAgB,CAAC2B,YAAY,CAAC,cAAc,QAAQrB,MAAM,CAAC,MAAQR,EAAImK,GAAG,gCAAgC5I,GAAG,CAAC,QAAU,SAASC,GAAQ,OAAOxB,EAAI0L,IAAIT,EAAMG,IAAIxB,GAAG,IAAI,CAAC1J,EAAG,YAAY,CAAC2B,YAAY,CAAC,MAAQ,WAAWrB,MAAM,CAAC,KAAO,YAAY,KAAO,OAAO,KAAO,SAASmL,KAAK,aAAa,CAAC3L,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAImK,GAAG,sBAAsB,GAAG,QAAQ,IAAI,GAAGjK,EAAG,SAAS,CAACM,MAAM,CAAC,OAAS,GAAG,KAAO,OAAO,QAAU,QAAQ,CAACN,EAAG,gBAAgB,CAACM,MAAM,CAAC,WAAa,GAAG,eAAeR,EAAI4L,QAAQ,aAAa,CAAC,GAAI,GAAI,IAAI,YAAY5L,EAAI6L,SAAS,OAAS,2BAA2B,MAAQ7L,EAAI8L,OAAOvK,GAAG,CAAC,cAAcvB,EAAI+L,iBAAiB,iBAAiB/L,EAAIgM,oBAAoB,qBAAqB,SAASxK,GAAQxB,EAAI4L,QAAQpK,CAAM,EAAE,sBAAsB,SAASA,GAAQxB,EAAI4L,QAAQpK,CAAM,MAAM,GAAGtB,EAAG,YAAY,CAACM,MAAM,CAAC,MAAQR,EAAIiM,OACp/IjM,EAAImK,GAAG,sCACPnK,EAAImK,GAAG,qCAAqC,QAAUnK,EAAIkM,OAAO,MAAQ,QAAQ,wBAAuB,EAAM,yBAAwB,EAAM,cAAa,GAAO3K,GAAG,CAAC,iBAAiB,SAASC,GAAQxB,EAAIkM,OAAO1K,CAAM,EAAE,MAAQxB,EAAImM,QAAQ,CAACjM,EAAG,UAAU,CAACE,IAAI,UAAUC,YAAY,gBAAgBG,MAAM,CAAC,MAAQR,EAAIoM,QAAQ,MAAQpM,EAAIqM,MAAM,cAAc,OAAO,iBAAiB,SAAS,CAACnM,EAAG,SAAS,CAACM,MAAM,CAAC,OAAS,KAAK,CAACN,EAAG,SAAS,CAACM,MAAM,CAAC,KAAO,KAAK,CAACN,EAAG,eAAe,CAACM,MAAM,CAAC,MAAQR,EAAImK,GAAG,6BAA6B,KAAO,SAAS,CAACjK,EAAG,WAAW,CAACM,MAAM,CAAC,YAAcR,EAAImK,GAAG,yCAAyCC,MAAM,CAACzH,MAAO3C,EAAIoM,QAAQ7L,KAAM8J,SAAS,SAAUC,GAAMtK,EAAIuK,KAAKvK,EAAIoM,QAAS,OAAQ9B,EAAI,EAAE1H,WAAW,mBAAmB,IAAI,GAAG1C,EAAG,SAAS,CAACM,MAAM,CAAC,KAAO,KAAK,CAACN,EAAG,eAAe,CAACM,MAAM,CAAC,MAAQR,EAAImK,GAAG,6BAA6B,KAAO,OAAO,SAAW,KAAK,CAACjK,EAAG,iBAAiB,CAACM,MAAM,CAAC,UAAY,YAAYe,GAAG,CAAC,OAASvB,EAAIsM,YAAYlC,MAAM,CAACzH,MAAO3C,EAAIoM,QAAQ3I,KAAM4G,SAAS,SAAUC,GAAMtK,EAAIuK,KAAKvK,EAAIoM,QAAS,OAAQ9B,EAAI,EAAE1H,WAAW,iBAAiB,CAAC1C,EAAG,WAAW,CAACM,MAAM,CAAC,MAAQ,IAAI,CAACR,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAImK,GAAG,4BAA4BjK,EAAG,WAAW,CAACM,MAAM,CAAC,MAAQ,IAAI,CAACR,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAImK,GAAG,4BAA4BjK,EAAG,WAAW,CAACM,MAAM,CAAC,MAAQ,IAAI,CAACR,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAImK,GAAG,4BAA4B,IAAI,IAAI,GAAGjK,EAAG,SAAS,CAACM,MAAM,CAAC,KAAO,KAAK,CAACN,EAAG,eAAe,CAACM,MAAM,CAAC,MAAQR,EAAImK,GAAG,+BAA+B,KAAO,SAAS,CAACjK,EAAG,aAAa,CAACM,MAAM,CAAC,UAAY,EAAE,KAAO,OAAO,SAAWR,EAAIuM,YAAYvM,EAAIoM,QAAQ3I,MAAM,eAAiBzD,EAAIiB,eAAe,SAAWjB,EAAImB,UAAUI,GAAG,CAAC,aAAevB,EAAIwM,aAAa,QAAUxM,EAAIyM,QAAQ,SAAWzM,EAAI0M,aAAa,IAAI,IAAI,IAAI,GAAGxM,EAAG,OAAO,CAACG,YAAY,gBAAgBG,MAAM,CAAC,KAAO,UAAUmL,KAAK,UAAU,CAACzL,EAAG,YAAY,CAACM,MAAM,CAAC,QAAUR,EAAI2M,WAAWpL,GAAG,CAAC,MAAQ,SAASC,GAAQxB,EAAIkM,QAAS,CAAK,IAAI,CAAClM,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAImK,GAAG,qBAAqBjK,EAAG,YAAY,CAACM,MAAM,CAAC,KAAO,UAAU,QAAUR,EAAI2M,WAAWpL,GAAG,CAAC,MAAQ,SAASC,GAAQ,OAAOxB,EAAI4M,MAAM,IAAI,CAAC5M,EAAIsC,GAAGtC,EAAIuC,GAAGvC,EAAImK,GAAG,uBAAuB,IAAI,IAAI,EACplE,EACI/G,EAAkB,G,oBC8HtB,GACAyJ,WAAA,CAAAC,WAAAA,EAAAA,GACAjJ,IAAAA,GACA,OACA8I,WAAA,EACA1L,eAAA,UACA4J,SAAA,GACAe,QAAA,EACAC,SAAA,GACAC,MAAA,EACA5B,KAAA,CACA3J,KAAA,GACAiK,KAAA,GACA/G,KAAA,IAEA2I,QAAA,CACA7L,KAAA,GACAkD,KAAA,EACA4H,KAAA,IAEAlK,SAAA,GACA4L,WAAA,CACA3F,MAAA,EACAC,OAAA,GAEAuC,GAAA,GACA9H,SAAAgC,iBACAuI,MAAA,CACA9L,KAAA,CACA,CACAyM,UAAA,EACAC,QAAA,KAAA1G,MAAAC,EAAA,wCACA0G,QAAA,SAGA7B,KAAA,CACA,CACA2B,UAAA,EACAC,QAAA,KAAA1G,MAAAC,EAAA,wCACA0G,QAAA,YAIAhB,QAAA,EACAD,QAAA,EACArB,WAAA,EAEA,EACAuC,OAAAA,GACA,KAAAC,SACA,EACAxI,QAAA,CACA8H,QAAAA,CAAAW,GACA/F,QAAAC,IAAA8F,EAAA,cACA,KAAAN,WAAAM,CAGA,EACAb,YAAAA,CAAAc,GACA,KAAAX,UAAAW,CACA,EACAhB,UAAAA,CAAA7I,GACA,OAAAA,GACA,OACA,KAAAxC,eAAA,UACA,MACA,OACA,KAAAA,eAAA,2CACA,MACA,OACA,KAAAA,eAAA,gTACA,MACA,QACA,MAGA,EACAmM,OAAAA,GACA,KAAAxC,WAAA,GACAwC,EAAAA,EAAAA,IAAA,CACAG,KAAA,KAAA3B,QACAC,SAAA,KAAAA,SACAtL,KAAA,KAAA2J,KAAA3J,KACAkD,KAAA,KAAAyG,KAAAzG,KACA+J,iBACA,KAAAtD,KAAAM,KAAArH,OAAA,OAAA+G,KAAAM,KAAA,UACAiD,eACA,KAAAvD,KAAAM,KAAArH,OAAA,OAAA+G,KAAAM,KAAA,YACAkD,MAAAnF,IACA,GAAAA,EAAAoF,OACA,KAAA9C,SAAAtC,EAAA1E,KAAAA,KACA,KAAAiI,MAAAvD,EAAA1E,KAAAiI,MACA,KAAAlB,WAAA,EACA,GAEA,EAEAH,GAAAA,GACA,KAAAyB,QAAA,EACA,KAAAD,QAAA,EACA,KAAAG,QAAA,CACA7L,KAAA,GACAkD,KAAA,EACA4H,KAAA,IAEA,KAAAlK,SAAA,EACA,EACAsK,IAAAA,CAAAL,GACA,KAAAjK,SAAA,GACA,KAAAyI,GAAAwB,EAAAxB,GACA,KAAAwC,QAAA7L,KAAA6K,EAAA7K,KACA,KAAA6L,QAAA3I,KAAA2H,EAAA3H,KACA,KAAA2I,QAAAf,KAAAD,EAAAC,KACA,KAAAY,QAAA,EACA,KAAAC,QAAA,EACA,KAAA/K,SAAA,CACA,CACAQ,IAAAyJ,EAAAC,KACA5J,OAAA,EACAlB,KAAA,KAAA+K,YAAAF,EAAAC,OAGA,EACAuB,IAAAA,GACA,KAAAxE,MAAAgE,QAAAwB,UAAAC,IACA,GAAAA,EAAA,CAEA,YAAA1M,SAAAgC,OAEA,YADA,KAAAkD,SAAAyH,MAAA,KAAAvH,MAAAC,EAAA,oCAIA,MAAAjD,EAAA,KAAAgJ,YAAA,KAAAH,QAAA3I,MACAsK,EAAA,KAAA5M,SAAA,GAAAQ,IAEA,aAAA4B,IAAA,KAAA7B,WAAAqM,GAEA,YADA,KAAA1H,SAAAyH,MAAA,KAAAvH,MAAAC,EAAA,mCAIA,aAAAjD,IAAA,KAAAnB,WAAA2L,GAEA,YADA,KAAA1H,SAAAyH,MAAA,KAAAvH,MAAAC,EAAA,mCAIA,YAAAjD,IAAA,KAAAyK,iBAAAD,GAEA,YADA,KAAA1H,SAAAyH,MAAA,KAAAvH,MAAAC,EAAA,kCAIA,MAAAyH,EAAA,CACAxK,KAAA,KAAA2I,QAAA3I,KACA4H,KAAA,KAAAe,QAAAf,KACA9K,KAAA,KAAA6L,QAAA7L,KACA2N,aAAA,KAAAnB,WAAA3F,MACA+G,cAAA,KAAApB,WAAA1F,QAGA,KAAA4E,QACAR,EAAAA,EAAAA,IAAAwC,EAAA,KAAArE,IAAA8D,MAAAnF,IACA,KAAAlC,SAAA,CACA5C,KAAA,UACAwJ,QAAA,KAAA1G,MAAAC,EAAA,wBAEA,KAAA4B,MAAAgE,QAAAgC,cACA,KAAAlC,QAAA,EACA,KAAAkB,SAAA,KAGA3C,EAAAA,EAAAA,IAAAwD,GAAAP,MAAAnF,IACA,KAAAlC,SAAA,CACA5C,KAAA,UACAwJ,QAAA,KAAA1G,MAAAC,EAAA,uBAEA,KAAA4B,MAAAgE,QAAAgC,cACA,KAAAlC,QAAA,EACA,KAAAkB,SAAA,GAGA,IAEA,EACA1L,UAAAA,CAAAC,GACA,IAAAA,EAAA,SACA,MAAAwH,EAAAxH,EAAAoG,MAAA,KAAA4B,MAAAL,cACA,uCAAA3C,SAAAwC,EACA,EACA/G,UAAAA,CAAAT,GACA,IAAAA,EAAA,SACA,MAAAwH,EAAAxH,EAAAoG,MAAA,KAAA4B,MAAAL,cACA,iCAAA3C,SAAAwC,EACA,EACA6E,gBAAAA,CAAArM,GACA,IAAAA,EAAA,SACA,MAAAwH,EAAAxH,EAAAoG,MAAA,KAAA4B,MAAAL,cACA,qDAAA3C,SAAAwC,EACA,EAEAuC,GAAAA,CAAA9B,IACA8B,EAAAA,EAAAA,IAAA9B,GAAA8D,MAAAnF,IACA,KAAAlC,SAAA,CACA5C,KAAA,UACAwJ,QAAA,KAAA1G,MAAAC,EAAA,0BAEA,KAAA4G,SAAA,GAEA,EACAX,OAAAA,CAAA5I,GACA,KAAA1C,SAAA,CAAA0C,GACA,KAAAuI,QAAAf,KAAAxH,EAAAlC,GACA,EACAwK,KAAAA,GACA,KAAAQ,YACA,KAAAP,QAAA,CACA7L,KAAA,GACAkD,KAAA,EACA4H,KAAA,IAEA,KAAAlK,SAAA,GACA,EAEAwJ,UAAAA,GACA,KAAAyC,SACA,EAEA1C,SAAAA,GACA,KAAAkB,QAAA,EACA,KAAAxD,MAAA8B,KAAAkE,cACA,KAAAhB,SACA,EACArB,gBAAAA,CAAAjH,GACA,KAAA8G,QAAA,EACA,KAAAC,SAAA/G,EACA,KAAAsI,SACA,EACApB,mBAAAA,CAAAlH,GACA,KAAA8G,QAAA9G,EACA,KAAAsI,SACA,EACAjC,WAAAA,CAAA1H,GACA,MAAA4K,EAAA,CACA,OAAAlE,GAAA,wBACA,OAAAA,GAAA,wBACA,OAAAA,GAAA,wBAEA,OAAAkE,EAAA5K,IAAA,GACA,EACApB,gBAAAA,CAAAV,GACA,IAAAA,EAAA,yBACA,MAAA+H,EAAA/H,EAAAoG,MAAA,KAAA4B,MAAAL,cACA,OAAAI,GACA,UACA,6BACA,UACA,WACA,UACA,WACA,UACA,WACA,yBACA,UACA,WACA,UACA,UACA,UACA,wBACA,UACA,UACA,UACA,WACA,6BACA,QACA,yBAEA,EACA4B,WAAAA,CAAA3J,GACA,OAAAA,EACAA,EAAAoG,MAAA,KAAA4B,MADA,EAEA,EACA4C,WAAAA,CAAA9I,GACA,OAAAA,GACA,qBACA,qBACA,oBACA,sBAEA,IChauP,I,UCQnPwG,GAAY,OACd,EACAlK,EACAqD,GACA,EACA,KACA,WACA,MAIF,EAAe6G,EAAiB,O,8ICjBzB,SAASmD,EAAQkB,GACtB,OAAOC,EAAAA,EAAAA,IAAQ,CACb5M,IAAK,gCACL6M,OAAQ,MACRF,UAEJ,CAEO,SAAS7D,EAAI5G,GAClB,OAAO0K,EAAAA,EAAAA,IAAQ,CACb5M,IAAK,0CACL6M,OAAQ,OACR3K,QAEJ,CAEO,SAAS4H,EAAK5H,EAAK+F,GACxB,OAAO2E,EAAAA,EAAAA,IAAQ,CACb5M,IAAK,4CAA8CiI,EACnD4E,OAAQ,MACR3K,QAEJ,CAEO,SAAS6H,EAAI9B,GAClB,OAAO2E,EAAAA,EAAAA,IAAQ,CACb5M,IAAK,gCAAkCiI,EACvC4E,OAAQ,UAEZ,C", "sources": ["webpack://esop-dashboard/./src/components/file-upload.vue", "webpack://esop-dashboard/src/components/file-upload.vue", "webpack://esop-dashboard/./src/components/file-upload.vue?6b69", "webpack://esop-dashboard/./src/components/file-upload.vue?49cc", "webpack://esop-dashboard/./src/views/Material.vue", "webpack://esop-dashboard/src/views/Material.vue", "webpack://esop-dashboard/./src/views/Material.vue?7175", "webpack://esop-dashboard/./src/views/Material.vue?4646", "webpack://esop-dashboard/./src/api/material.js"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('el-upload',{ref:\"uploadRef\",staticClass:\"avatar-uploader2\",class:_vm.name,attrs:{\"action\":_vm.upload_host,\"name\":\"uploadFile\",\"headers\":_vm.headers,\"multiple\":_vm.uploadNum > 1 ? true : false,\"show-file-list\":false,\"before-upload\":_vm.beforeUpload,\"on-remove\":_vm.handleRemove,\"on-progress\":_vm.handleUploadProgress,\"on-success\":_vm.handleUploadSuccess,\"disabled\":!_vm.isDisabled,\"accept\":_vm.acceptFileType}}),_c('div',{staticClass:\"images\"},[_vm._l((_vm.fileList),function(item,index){return _c('div',{key:index,staticClass:\"item\"},[_c('div',{staticClass:\"img\",attrs:{\"id\":\"preview-item\"},on:{\"mouseover\":function($event){item.hover = true},\"mouseout\":function($event){item.hover = false}}},[(_vm.isImageUrl(item.url))?_c('el-image',{ref:\"img\",refInFor:true,staticStyle:{\"width\":\"120px\",\"height\":\"120px\"},attrs:{\"src\":_vm.imageUrl + item.url,\"preview-src-list\":_vm.previewImages(),\"fit\":\"cover\"},on:{\"click\":function($event){$event.stopPropagation();return _vm.handleClickItem.apply(null, arguments)}}}):(_vm.isVideoUrl(item.url))?_c('video',{staticStyle:{\"width\":\"120px\",\"height\":\"120px\",\"display\":\"block\"},attrs:{\"controls\":\"\"}},[_c('source',{attrs:{\"src\":_vm.imageUrl + item.url,\"type\":\"video/avi\"}})]):_c('div',{staticClass:\"file-preview\"},[_c('i',{class:_vm.getFileIconClass(item.url)}),_c('div',{staticClass:\"file-name\"},[_vm._v(_vm._s(item.name || _vm.getFileNameFromUrl(item.url)))])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(item.hover),expression:\"item.hover\"}],staticClass:\"mask\"},[(_vm.canPreview(item.url))?_c('i',{staticClass:\"el-icon-zoom-in\",on:{\"click\":function($event){return _vm.previewFile(item)}}}):_c('i',{staticClass:\"el-icon-download\",on:{\"click\":function($event){return _vm.downloadFile(item.url)}}}),(!_vm.isDisabled)?_c('i',{staticClass:\"el-icon-upload2\",on:{\"click\":function($event){return _vm.uploadImage(item.url)}}}):_vm._e(),(!_vm.isDisabled)?_c('i',{staticClass:\"el-icon-delete\",on:{\"click\":function($event){return _vm.deleteImage(item.url)}}}):_vm._e()])],1)])}),(_vm.fileList.length < _vm.uploadNum)?_c('div',{staticClass:\"add\",on:{\"click\":function($event){return _vm.uploadImage()}}},[_c('i',{staticClass:\"el-icon-plus\"})]):_vm._e()],2)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div>\n    <el-upload ref=\"uploadRef\" :action=\"upload_host\" name=\"uploadFile\" :headers=\"headers\"\n      :multiple=\"uploadNum > 1 ? true : false\" :show-file-list=\"false\" :before-upload=\"beforeUpload\"\n      :on-remove=\"handleRemove\" :on-progress=\"handleUploadProgress\" :on-success=\"handleUploadSuccess\"\n      :disabled=\"!isDisabled\" :class=\"name\" :accept=\"acceptFileType\" class=\"avatar-uploader2\">\n    </el-upload>\n    <div class=\"images\">\n      <div class=\"item\" v-for=\"(item, index) in fileList\" :key=\"index\">\n        <div class=\"img\" @mouseover=\"item.hover = true\" @mouseout=\"item.hover = false\" id=\"preview-item\">\n          <el-image @click.stop=\"handleClickItem\" ref=\"img\" v-if=\"isImageUrl(item.url)\" :src=\"imageUrl + item.url\"\n            :preview-src-list=\"previewImages()\" style=\"width: 120px; height: 120px\" fit=\"cover\"></el-image>\n          <video v-else-if=\"isVideoUrl(item.url)\" controls style=\"width: 120px; height: 120px; display: block\">\n            <source :src=\"imageUrl + item.url\" type=\"video/avi\">\n          </video>\n          <div v-else class=\"file-preview\">\n            <i :class=\"getFileIconClass(item.url)\"></i>\n            <div class=\"file-name\">{{ item.name || getFileNameFromUrl(item.url) }}</div>\n          </div>\n          <div class=\"mask\" v-show=\"item.hover\">\n            <i class=\"el-icon-zoom-in\" @click=\"previewFile(item)\" v-if=\"canPreview(item.url)\"></i>\n            <i class=\"el-icon-download\" @click=\"downloadFile(item.url)\" v-else></i>\n            <i class=\"el-icon-upload2\" @click=\"uploadImage(item.url)\" v-if=\"!isDisabled\"></i>\n            <i class=\"el-icon-delete\" @click=\"deleteImage(item.url)\" v-if=\"!isDisabled\"></i>\n          </div>\n        </div>\n      </div>\n      <div class=\"add\" v-if=\"fileList.length < uploadNum\" @click=\"uploadImage()\">\n        <i class=\"el-icon-plus\"></i>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport request from \"@/utils/request\";\nimport { findElem } from \"@/utils/util.js\";\nimport store from \"@/store\";\n\nexport default {\n  props: {\n    isDisabled: Boolean,\n    fileType: String,\n    name: String,\n    acceptFileType: {\n      type: String,\n      default: \"\",\n    },\n    fileList: Array,\n    uploadNum: {\n      type: Number,\n      default: 1,\n    },\n    index: {\n      type: Number,\n      default: 0,\n    },\n  },\n  data () {\n    return {\n\n\n\n      upload_host: process.env.VUE_APP_BASE_API + `admin/sourcematerial/upload`,\n      imageUrl: process.env.VUE_APP_BASE_API + \"assets/media/\",\n      headers: {\n        Authorization: store.getters.token,\n      },\n\n      showImage: \"\",\n      dialogVisible: false,\n      uploadImageId: \"\",\n      list: [],\n    };\n  },\n  computed: {\n    showFileList: {\n      get: function () {\n        return this.fileList && this.fileList.length > 0;\n      },\n      set: function (newValue) { },\n    },\n  },\n  methods: {\n    emitInput (val) {\n      this.$emit(\"input\", val);\n    },\n    handleClickItem () {\n      setTimeout(() => {\n        let domImageMask = document.querySelector(\".el-image-viewer__wrapper\");\n        if (!domImageMask) return;\n\n        domImageMask.addEventListener(\"click\", (e) => {\n          if (e.target.parentNode.className == \"el-image-viewer__actions__inner\") {\n            return;\n          }\n          document.querySelector(\".el-image-viewer__close\").click();\n        });\n      }, 300);\n    },\n    handleRemove (file, fileList) {\n      if (fileList.length === 0) {\n        this.fileList = [];\n      } else {\n        this.fileList = fileList;\n      }\n    },\n    getUUID () {\n      return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, (c) => {\n        return (c === \"x\" ? (Math.random() * 16) | 0 : \"r&0x3\" | \"0x8\").toString(16);\n      });\n    },\n    beforeUpload (file) {\n\n      const isValidType = this.checkFileType(file);\n\n      if (!isValidType) {\n        this.handleInvalidType(file);\n        return false;\n      }\n\n      this.list.push(file.name);\n      if (this.list.length > this.uploadNum) {\n        this.$message.warning(this.$i18n.t(\"upload.maxFileCount\", { count: this.uploadNum }));\n        this.resetUploadState();\n        return false;\n      }\n      if (file.type.includes(\"image\")) {\n\n        let reader = new FileReader();\n        reader.onload = (e) => {\n          let txt = e.target.result\n          let img = document.createElement(\"img\")\n          img.src = txt\n          img.onload = () => {\n            this.$emit(\n              \"fileData\",\n              {\n                width: img.width,\n                height: img.height,\n\n              }\n            );\n\n            console.log(\"宽度：\", img.width);\n            console.log(\"高度：\", img.height);\n          }\n        };\n        reader.readAsDataURL(file);\n\n      }\n\n      else if (file.type.includes(\"video\")) {\n        console.log(file, \"ffff\");\n        const videoElement = document.createElement(\"video\");\n        videoElement.src = URL.createObjectURL(file);\n        videoElement.addEventListener(\"loadedmetadata\", () => {\n          this.$emit(\n            \"fileData\",\n            {\n              width: videoElement.videoWidth,\n              height: videoElement.videoHeight,\n\n            }\n          );\n          console.log('width, height:', videoElement.videoWidth\n            , videoElement.videoHeight)\n        });\n\n      }\n\n\n      return true;\n    },\n    checkFileType (file) {\n      console.log(this.fileType)\n      if (this.fileType === \"image\") {\n        return file.type.includes(\"image\");\n      } else if (this.fileType === \"video\") {\n        return file.type.includes(\"video\");\n      } else if (this.fileType === \"image/video\") {\n        return file.type.includes(\"image\") || file.type.includes(\"video\");\n      } else if (this.fileType === \"file\") {\n        // 使用 acceptFileType prop 来进行校验，使其更具通用性\n        const allowedTypes = this.acceptFileType.split(',').map(item => item.trim());\n        return allowedTypes.includes(file.type);\n      }\n      return true;\n    },\n    handleInvalidType (file) {\n      const warningMsg = this.getWarningMessage();\n      this.$message.warning(warningMsg);\n      this.resetUploadState();\n    },\n    getWarningMessage () {\n      switch (this.fileType) {\n        case \"image\":\n          return this.$i18n.t(\"upload.onlyImage\");\n        case \"video\":\n          return this.$i18n.t(\"upload.onlyVideo\");\n        case \"image/video\":\n          return this.$i18n.t(\"upload.onlyVideoOrImageAgain\");\n        case \"pdf\":\n          return this.$i18n.t(\"material.dialog.form.onlyPDF\");\n        case \"file\":\n          return this.$i18n.t(\"material.dialog.form.onlyFile\");\n        default:\n          return \"\";\n      }\n    },\n    resetUploadState () {\n      this.$refs.uploadRef.clearFiles();\n      this.list = [];\n      this.fileList = [...this.fileList];\n    },\n    handleUploadProgress () {\n      this.$emit(\"uploadStatus\", true);\n    },\n    handleUploadSuccess (res, file, fileList) {\n      this.$emit(\"uploadStatus\", false);\n      this.fileList.push({\n        name: file.name,\n        hover: false,\n        url: file.response.data.file_name,\n      });\n\n      this.$emit(\n        \"editUrl\",\n        {\n          name: file.name,\n          hover: false,\n          url: file.response.data.file_name,\n        },\n        this.name,\n        this.index\n      );\n    },\n    previewImages () {\n      let images = [];\n      if (this.fileList && this.fileList.length > 0) {\n        this.fileList.forEach((item) => {\n          if (this.isImageUrl(item.url)) {\n            const fullUrl = this.imageUrl + item.url;\n            images.push(fullUrl);\n          }\n        });\n      }\n      return images;\n    },\n    previewImage (url) {\n      let images = [];\n      this.fileList.forEach((item) => {\n        if (this.isImageUrl(item.url)) {\n          images.push(item);\n        }\n      });\n      let index = findElem(images, \"url\", url);\n      if (index !== -1 && this.$refs.img && this.$refs.img[index]) {\n        this.$refs.img[index].showViewer = true;\n      }\n    },\n    previewVideo (url) {\n      // 保留原视频预览逻辑\n    },\n    previewFile (item) {\n      if (this.isImageUrl(item.url)) {\n        this.previewImage(item.url);\n      } else if (this.isVideoUrl(item.url)) {\n        this.previewVideo(item.url);\n      } else {\n        // 对于 PDF/PPT，直接在新窗口打开\n        window.open(this.imageUrl + item.url, \"_blank\");\n      }\n    },\n    isImageUrl (url) {\n      if (!url) return false;\n      const fileSuffix = url.substring(url.lastIndexOf(\".\") + 1).toLowerCase();\n      const whiteList = [\"jpg\", \"jpeg\", \"png\", \"gif\", \"webp\"];\n      return whiteList.includes(fileSuffix);\n    },\n    isVideoUrl (url) {\n      if (!url) return false;\n      const videoSuffix = [\"mp4\", \"ogg\", \"mov\", \"webm\"];\n      const suffix = url.substring(url.lastIndexOf(\".\") + 1).toLowerCase();\n      return videoSuffix.includes(suffix);\n    },\n    getFileIconClass (url) {\n      if (!url) return \"el-icon-document\";\n      const ext = url.substring(url.lastIndexOf(\".\") + 1).toLowerCase();\n      switch (ext) {\n        case \"pdf\":\n          return \"el-icon-document-pdf\";\n        case \"doc\":\n        case \"docx\":\n          return \"el-icon-document\";\n        case \"xls\":\n        case \"xlsx\":\n          return \"el-icon-document\";\n        case \"ppt\":\n        case \"pptx\":\n          return \"el-icon-document\";\n        default:\n          return \"el-icon-document\";\n      }\n    },\n    getFileNameFromUrl (url) {\n      if (!url) return \"\";\n      return url.split(\"/\").pop();\n    },\n    canPreview (url) {\n      return this.isImageUrl(url) || this.isVideoUrl(url);\n    },\n    downloadFile (url) {\n      window.open(this.imageUrl + url, \"_blank\");\n    },\n    uploadImage (id) {\n      this.list = [];\n      this.uploadImageId = id;\n      // 使用 $refs 替代 document.querySelector，确保组件内部元素被正确访问\n      if (this.$refs.uploadRef) {\n        this.$refs.uploadRef.$el.querySelector('input').click();\n      }\n    },\n    deleteImage (url) {\n      const index = findElem(this.fileList, \"url\", url);\n      if (index !== -1) {\n        this.fileList.splice(index, 1);\n      }\n    },\n    submitUpload () {\n      if (this.$refs.uploadRef) {\n        this.$refs.uploadRef.submit();\n      }\n    },\n  },\n};\n</script>\n\n<style type=\"text/css\" lang=\"scss\" scoped>\n.images {\n  display: flex;\n  flex-wrap: wrap;\n\n  .item {\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n    width: 120px;\n    margin-right: 20px;\n    margin-bottom: 15px;\n\n    .img {\n      border: 1px dashed #eaeaea;\n      border-radius: 5px;\n      overflow: hidden;\n      position: relative;\n\n      .el-image {\n        display: block;\n      }\n\n      .mask {\n        position: absolute;\n        left: 0;\n        top: 0;\n        width: 120px;\n        height: 120px;\n        background: rgba($color: #000000, $alpha: 0.3);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n\n        i {\n          font-size: 20px;\n          color: #ffffff;\n          cursor: pointer;\n          margin: 0 8px;\n        }\n      }\n    }\n\n    .add {\n      border: 1px solid #dddddd;\n      border-radius: 5px;\n      cursor: pointer;\n    }\n\n    .text {\n      font-size: 14px;\n      color: #666666;\n    }\n  }\n\n  .add {\n    width: 120px;\n    height: 120px;\n    border: 1px solid #dddddd;\n    border-radius: 5px;\n    cursor: pointer;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    i {\n      font-size: 30px;\n      color: #999;\n    }\n  }\n}\n\n.avatar-uploader2 {\n  height: 0;\n}\n\n.file-preview {\n  width: 120px;\n  height: 120px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  border: 1px dashed #eaeaea;\n  border-radius: 5px;\n\n  i {\n    font-size: 40px;\n    color: #409EFF;\n    margin-bottom: 10px;\n  }\n\n  .file-name {\n    max-width: 100px;\n    text-align: center;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n    font-size: 12px;\n    color: #606266;\n  }\n}\n</style>", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./file-upload.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./file-upload.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./file-upload.vue?vue&type=template&id=84f8d436&scoped=true\"\nimport script from \"./file-upload.vue?vue&type=script&lang=js\"\nexport * from \"./file-upload.vue?vue&type=script&lang=js\"\nimport style0 from \"./file-upload.vue?vue&type=style&index=0&id=84f8d436&prod&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"84f8d436\",\n  null\n  \n)\n\nexport default component.exports", "var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',{staticClass:\"material-container\"},[_c('el-form',{ref:\"form\",staticClass:\"demo-ruleForm\",attrs:{\"model\":_vm.form,\"label-width\":\"80px\",\"label-position\":\"left\"}},[_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":4}},[_c('el-form-item',{attrs:{\"label\":_vm.$t('material.form.name'),\"prop\":\"name\"}},[_c('el-input',{attrs:{\"placeholder\":_vm.$t('material.dialog.form.namePlaceholder')},model:{value:(_vm.form.name),callback:function ($$v) {_vm.$set(_vm.form, \"name\", $$v)},expression:\"form.name\"}})],1)],1),_c('el-col',{attrs:{\"span\":4}},[_c('el-form-item',{attrs:{\"label\":_vm.$t('material.table.created_at'),\"prop\":\"date\"}},[_c('el-date-picker',{attrs:{\"value-format\":\"timestamp\",\"type\":\"daterange\",\"range-separator\":_vm.$t('public.to'),\"start-placeholder\":_vm.$t('public.startDate'),\"end-placeholder\":_vm.$t('public.endDate')},model:{value:(_vm.form.date),callback:function ($$v) {_vm.$set(_vm.form, \"date\", $$v)},expression:\"form.date\"}})],1)],1)],1),_c('el-row',{attrs:{\"type\":\"flex\",\"justify\":\"space-between\"}},[_c('el-button',{attrs:{\"type\":\"primary\",\"icon\":\"el-icon-plus\",\"size\":\"mini\"},on:{\"click\":function($event){return _vm.add()}}},[_vm._v(_vm._s(_vm.$t(\"material.button.add\")))]),_c('el-col',{attrs:{\"span\":4}},[_c('el-button',{attrs:{\"size\":\"mini\"},on:{\"click\":function($event){return _vm.resetForm('form', 'getList')}}},[_vm._v(_vm._s(_vm.$t(\"public.reset\")))]),_c('el-button',{attrs:{\"type\":\"primary\",\"size\":\"mini\"},on:{\"click\":function($event){return _vm.searchForm()}}},[_vm._v(_vm._s(_vm.$t(\"public.search\")))])],1)],1)],1),_c('div',{staticStyle:{\"height\":\"60vh\",\"background-color\":\"#ccc\",\"margin\":\"10px 0\"}},[_c('el-table',{directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.isLoading),expression:\"isLoading\"}],staticStyle:{\"width\":\"100%\"},attrs:{\"data\":_vm.dataList,\"border\":\"\",\"height\":\"100%\"}},[_c('el-table-column',{attrs:{\"prop\":\"num\",\"label\":_vm.$t('material.table.num'),\"width\":\"120\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(\" \"+_vm._s(scope.$index + 1)+\" \")]}}])}),_c('el-table-column',{attrs:{\"prop\":\"name\",\"label\":_vm.$t('material.table.name'),\"width\":\"180\",\"align\":\"center\"}}),_c('el-table-column',{attrs:{\"prop\":\"type\",\"label\":_vm.$t('material.table.type'),\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(\" \"+_vm._s(_vm.getTypeName(scope.row.type))+\" \")]}}])}),_c('el-table-column',{attrs:{\"prop\":\"path\",\"label\":_vm.$t('material.table.preview'),\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [(scope.row.type == 1 && scope.row.path)?_c('el-image',{staticClass:\"img\",attrs:{\"src\":_vm.imageUrl + scope.row.path,\"preview-src-list\":[_vm.imageUrl + scope.row.path],\"fit\":\"cover\"}}):(scope.row.type == 2 && scope.row.path)?_c('video',{staticClass:\"img\",attrs:{\"src\":_vm.imageUrl + scope.row.path,\"controls\":\"\"}}):(scope.row.type == 3 && scope.row.path)?_c('div',{staticClass:\"file-preview\"},[_c('i',{class:_vm.getFileIconClass(scope.row.path)}),_c('div',{staticClass:\"file-name\"},[_vm._v(_vm._s(_vm.getFileName(scope.row.path)))])]):_c('div',[_vm._v(\"--\")])]}}])}),_c('el-table-column',{attrs:{\"prop\":\"created_at\",\"label\":_vm.$t('material.table.created_at'),\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_vm._v(\" \"+_vm._s(_vm.$formatTimeStamp(scope.row.created_at))+\" \")]}}])}),_c('el-table-column',{attrs:{\"label\":_vm.$t('material.table.operation'),\"fixed\":\"right\",\"align\":\"center\"},scopedSlots:_vm._u([{key:\"default\",fn:function(scope){return [_c('el-button',{attrs:{\"type\":\"text\",\"size\":\"small\"},on:{\"click\":function($event){return _vm.edit(scope.row)}}},[_vm._v(_vm._s(_vm.$t(\"public.edit\")))]),_c('el-popconfirm',{staticStyle:{\"margin-left\":\"10px\"},attrs:{\"title\":_vm.$t('material.table.sureToDelete')},on:{\"confirm\":function($event){return _vm.del(scope.row.id)}}},[_c('el-button',{staticStyle:{\"color\":\"#ff0000\"},attrs:{\"slot\":\"reference\",\"type\":\"text\",\"size\":\"small\"},slot:\"reference\"},[_vm._v(_vm._s(_vm.$t(\"public.delete\")))])],1)]}}])})],1)],1),_c('el-row',{attrs:{\"gutter\":20,\"type\":\"flex\",\"justify\":\"end\"}},[_c('el-pagination',{attrs:{\"background\":\"\",\"current-page\":_vm.pageNum,\"page-sizes\":[10, 20, 50],\"page-size\":_vm.pageSize,\"layout\":\"total, prev, pager, next\",\"total\":_vm.total},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange,\"update:currentPage\":function($event){_vm.pageNum=$event},\"update:current-page\":function($event){_vm.pageNum=$event}}})],1),_c('el-dialog',{attrs:{\"title\":_vm.isEdit\n    ? _vm.$t('material.dialog.title.editMaterial')\n    : _vm.$t('material.dialog.title.addMaterial'),\"visible\":_vm.isShow,\"width\":\"600px\",\"close-on-click-modal\":false,\"close-on-press-escape\":false,\"show-close\":false},on:{\"update:visible\":function($event){_vm.isShow=$event},\"close\":_vm.close}},[_c('el-form',{ref:\"addForm\",staticClass:\"demo-ruleForm\",attrs:{\"model\":_vm.addForm,\"rules\":_vm.rules,\"label-width\":\"80px\",\"label-position\":\"left\"}},[_c('el-row',{attrs:{\"gutter\":20}},[_c('el-col',{attrs:{\"span\":15}},[_c('el-form-item',{attrs:{\"label\":_vm.$t('material.dialog.form.name'),\"prop\":\"name\"}},[_c('el-input',{attrs:{\"placeholder\":_vm.$t('material.dialog.form.namePlaceholder')},model:{value:(_vm.addForm.name),callback:function ($$v) {_vm.$set(_vm.addForm, \"name\", $$v)},expression:\"addForm.name\"}})],1)],1),_c('el-col',{attrs:{\"span\":15}},[_c('el-form-item',{attrs:{\"label\":_vm.$t('material.dialog.form.type'),\"prop\":\"type\",\"required\":\"\"}},[_c('el-radio-group',{attrs:{\"direction\":\"vertical\"},on:{\"change\":_vm.changeType},model:{value:(_vm.addForm.type),callback:function ($$v) {_vm.$set(_vm.addForm, \"type\", $$v)},expression:\"addForm.type\"}},[_c('el-radio',{attrs:{\"label\":1}},[_vm._v(_vm._s(_vm.$t(\"material.table.image\")))]),_c('el-radio',{attrs:{\"label\":2}},[_vm._v(_vm._s(_vm.$t(\"material.table.video\")))]),_c('el-radio',{attrs:{\"label\":3}},[_vm._v(_vm._s(_vm.$t(\"material.table.file\")))])],1)],1)],1),_c('el-col',{attrs:{\"span\":24}},[_c('el-form-item',{attrs:{\"label\":_vm.$t('material.dialog.form.upload'),\"prop\":\"path\"}},[_c('fileUpload',{attrs:{\"uploadNum\":1,\"name\":\"path\",\"fileType\":_vm.getFileType(_vm.addForm.type),\"acceptFileType\":_vm.acceptFileType,\"fileList\":_vm.fileList},on:{\"uploadStatus\":_vm.uploadStatus,\"editUrl\":_vm.editUrl,\"fileData\":_vm.fileData}})],1)],1)],1)],1),_c('span',{staticClass:\"dialog-footer\",attrs:{\"slot\":\"footer\"},slot:\"footer\"},[_c('el-button',{attrs:{\"loading\":_vm.uploading},on:{\"click\":function($event){_vm.isShow = false}}},[_vm._v(_vm._s(_vm.$t(\"public.cancel\")))]),_c('el-button',{attrs:{\"type\":\"primary\",\"loading\":_vm.uploading},on:{\"click\":function($event){return _vm.save()}}},[_vm._v(_vm._s(_vm.$t(\"public.confirm\")))])],1)],1)],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"material-container\">\n    <el-form :model=\"form\" ref=\"form\" label-width=\"80px\" label-position=\"left\" class=\"demo-ruleForm\">\n      <el-row :gutter=\"20\">\n        <el-col :span=\"4\">\n          <el-form-item :label=\"$t('material.form.name')\" prop=\"name\">\n            <el-input v-model=\"form.name\" :placeholder=\"$t('material.dialog.form.namePlaceholder')\"></el-input>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"4\">\n          <el-form-item :label=\"$t('material.table.created_at')\" prop=\"date\">\n            <el-date-picker v-model=\"form.date\" value-format=\"timestamp\" type=\"daterange\"\n              :range-separator=\"$t('public.to')\" :start-placeholder=\"$t('public.startDate')\"\n              :end-placeholder=\"$t('public.endDate')\">\n            </el-date-picker>\n          </el-form-item>\n        </el-col>\n\n      </el-row>\n      <el-row type=\"flex\" justify=\"space-between\">\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"add()\" size=\"mini\">{{ $t(\"material.button.add\")\n        }}</el-button>\n        <el-col :span=\"4\">\n          <el-button @click=\"resetForm('form', 'getList')\" size=\"mini\">{{\n            $t(\"public.reset\")\n          }}</el-button>\n          <el-button type=\"primary\" @click=\"searchForm()\" size=\"mini\">{{\n            $t(\"public.search\")\n          }}</el-button>\n        </el-col>\n      </el-row>\n    </el-form>\n\n    <div style=\"height: 60vh; background-color: #ccc; margin: 10px 0\">\n      <el-table v-loading=\"isLoading\" :data=\"dataList\" style=\"width: 100%\" border height=\"100%\">\n        <el-table-column prop=\"num\" :label=\"$t('material.table.num')\" width=\"120\" align=\"center\">\n          <template slot-scope=\"scope\">\n            {{ scope.$index + 1 }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"name\" :label=\"$t('material.table.name')\" width=\"180\" align=\"center\"></el-table-column>\n        <el-table-column prop=\"type\" :label=\"$t('material.table.type')\" align=\"center\">\n          <template slot-scope=\"scope\">\n            {{ getTypeName(scope.row.type) }}\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"path\" :label=\"$t('material.table.preview')\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-image v-if=\"scope.row.type == 1 && scope.row.path\" :src=\"imageUrl + scope.row.path\" class=\"img\"\n              :preview-src-list=\"[imageUrl + scope.row.path]\" fit=\"cover\"></el-image>\n            <video class=\"img\" v-else-if=\"scope.row.type == 2 && scope.row.path\" :src=\"imageUrl + scope.row.path\"\n              controls></video>\n            <div v-else-if=\"scope.row.type == 3 && scope.row.path\" class=\"file-preview\">\n              <i :class=\"getFileIconClass(scope.row.path)\"></i>\n              <div class=\"file-name\">{{ getFileName(scope.row.path) }}</div>\n            </div>\n            <div v-else>--</div>\n          </template>\n        </el-table-column>\n        <el-table-column prop=\"created_at\" :label=\"$t('material.table.created_at')\" align=\"center\">\n          <template slot-scope=\"scope\">\n            {{ $formatTimeStamp(scope.row.created_at) }}\n          </template>\n        </el-table-column>\n        <el-table-column :label=\"$t('material.table.operation')\" fixed=\"right\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" size=\"small\" @click=\"edit(scope.row)\">{{\n              $t(\"public.edit\")\n            }}</el-button>\n            <el-popconfirm :title=\"$t('material.table.sureToDelete')\" style=\"margin-left: 10px\"\n              @confirm=\"del(scope.row.id)\">\n              <el-button type=\"text\" style=\"color: #ff0000\" size=\"small\" slot=\"reference\">{{ $t(\"public.delete\")\n              }}</el-button>\n            </el-popconfirm>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n\n    <el-row :gutter=\"20\" type=\"flex\" justify=\"end\">\n      <el-pagination background @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\"\n        :current-page.sync=\"pageNum\" :page-sizes=\"[10, 20, 50]\" :page-size=\"pageSize\" layout=\"total, prev, pager, next\"\n        :total=\"total\">\n      </el-pagination>\n    </el-row>\n\n    <el-dialog :title=\"isEdit\n      ? $t('material.dialog.title.editMaterial')\n      : $t('material.dialog.title.addMaterial')\n      \" :visible.sync=\"isShow\" width=\"600px\" @close=\"close\" :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\" :show-close=\"false\">\n      <el-form :model=\"addForm\" :rules=\"rules\" ref=\"addForm\" label-width=\"80px\" label-position=\"left\"\n        class=\"demo-ruleForm\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"15\">\n            <el-form-item :label=\"$t('material.dialog.form.name')\" prop=\"name\">\n              <el-input v-model=\"addForm.name\" :placeholder=\"$t('material.dialog.form.namePlaceholder')\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"15\">\n            <el-form-item :label=\"$t('material.dialog.form.type')\" prop=\"type\" required>\n              <el-radio-group v-model=\"addForm.type\" direction=\"vertical\" @change=\"changeType\">\n                <el-radio :label=\"1\">{{ $t(\"material.table.image\") }}</el-radio>\n                <el-radio :label=\"2\">{{ $t(\"material.table.video\") }}</el-radio>\n                <el-radio :label=\"3\">{{ $t(\"material.table.file\") }}</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item :label=\"$t('material.dialog.form.upload')\" prop=\"path\">\n              <fileUpload :uploadNum=\"1\" name=\"path\" :fileType=\"getFileType(addForm.type)\" @uploadStatus=\"uploadStatus\"\n                :acceptFileType=\"acceptFileType\" :fileList=\"fileList\" @editUrl=\"editUrl\" @fileData=\"fileData\">\n              </fileUpload>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"isShow = false\" :loading=\"uploading\">{{ $t(\"public.cancel\") }}</el-button>\n        <el-button type=\"primary\" @click=\"save()\" :loading=\"uploading\">{{\n          $t(\"public.confirm\")\n        }}</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getList, add, edit, del } from \"@/api/material.js\";\nimport fileUpload from \"@/components/file-upload.vue\";\nexport default {\n  components: { fileUpload },\n  data () {\n    return {\n      uploading: false,\n      acceptFileType: \"image/*\",\n      dataList: [],\n      pageNum: 1,\n      pageSize: 10,\n      total: 0,\n      form: {\n        name: \"\",\n        date: [],\n        type: \"\"\n      },\n      addForm: {\n        name: \"\",\n        type: 1,\n        path: \"\",\n      },\n      fileList: [],\n      upfileData: {\n        width: 0,\n        height: 0,\n      },\n      id: \"\",\n      imageUrl: process.env.VUE_APP_BASE_API + \"assets/media/\",\n      rules: {\n        name: [\n          {\n            required: true,\n            message: this.$i18n.t(\"material.dialog.form.namePlaceholder\"),\n            trigger: \"blur\",\n          },\n        ],\n        path: [\n          {\n            required: true,\n            message: this.$i18n.t(\"material.dialog.form.pathPlaceholder\"),\n            trigger: \"change\",\n          },\n        ],\n      },\n      isShow: false,\n      isEdit: false,\n      isLoading: false,\n    };\n  },\n  created () {\n    this.getList();\n  },\n  methods: {\n    fileData (obj) {\n      console.log(obj, \"upfileData\");\n      this.upfileData = obj;\n\n\n    },\n    uploadStatus (status) {\n      this.uploading = status;\n    },\n    changeType (type) {\n      switch (type) {\n        case 1:\n          this.acceptFileType = \"image/*\";\n          break;\n        case 2:\n          this.acceptFileType = \"video/mp4,video/ogg,video/mov,video/webm\";\n          break;\n        case 3:\n          this.acceptFileType = \"application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation\";\n          break;\n        default:\n          break;\n      }\n\n    },\n    getList () {\n      this.isLoading = true;\n      getList({\n        page: this.pageNum,\n        pageSize: this.pageSize,\n        name: this.form.name,\n        type: this.form.type,\n        created_at_start:\n          this.form.date.length > 0 ? this.form.date[0] / 1000 : \"\",\n        created_at_end:\n          this.form.date.length > 0 ? this.form.date[1] / 1000 : \"\",\n      }).then((res) => {\n        if (res.code == 0) {\n          this.dataList = res.data.data;\n          this.total = res.data.total;\n          this.isLoading = false;\n        }\n      });\n    },\n    //新建模板\n    add () {\n      this.isShow = true;\n      this.isEdit = false;\n      this.addForm = {\n        name: \"\",\n        type: 1,\n        path: \"\"\n      };\n      this.fileList = [];\n    },\n    edit (row) {\n      this.fileList = [];\n      this.id = row.id;\n      this.addForm.name = row.name;\n      this.addForm.type = row.type;\n      this.addForm.path = row.path;\n      this.isEdit = true;\n      this.isShow = true;\n      this.fileList = [\n        {\n          url: row.path,\n          hover: false,\n          name: this.getFileName(row.path)\n        },\n      ];\n    },\n    save () {\n      this.$refs.addForm.validate((valid) => {\n        if (valid) {\n          // 验证文件类型是否匹配\n          if (this.fileList.length === 0) {\n            this.$message.error(this.$i18n.t(\"material.dialog.form.selectFile\"));\n            return;\n          }\n\n          const fileType = this.getFileType(this.addForm.type);\n          const fileUrl = this.fileList[0].url;\n\n          if (fileType === 'image' && !this.isImageUrl(fileUrl)) {\n            this.$message.error(this.$i18n.t(\"material.dialog.form.onlyImage\"));\n            return;\n          }\n\n          if (fileType === 'video' && !this.isVideoUrl(fileUrl)) {\n            this.$message.error(this.$i18n.t(\"material.dialog.form.onlyVideo\"));\n            return;\n          }\n\n          if (fileType === 'file' && !this.isAllowedFileUrl(fileUrl)) {\n            this.$message.error(this.$i18n.t(\"material.dialog.form.onlyFile\"));\n            return;\n          }\n\n          const formData = {\n            type: this.addForm.type,\n            path: this.addForm.path,\n            name: this.addForm.name,\n            source_width: this.upfileData.width,\n            source_height: this.upfileData.height,\n          };\n\n          if (this.isEdit) {\n            edit(formData, this.id).then((res) => {\n              this.$message({\n                type: \"success\",\n                message: this.$i18n.t(\"public.editSuccess\"),\n              });\n              this.$refs.addForm.resetFields();\n              this.isShow = false;\n              this.getList();\n            });\n          } else {\n            add(formData).then((res) => {\n              this.$message({\n                type: \"success\",\n                message: this.$i18n.t(\"public.addSuccess\"),\n              });\n              this.$refs.addForm.resetFields();\n              this.isShow = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    isImageUrl (url) {\n      if (!url) return false;\n      const fileSuffix = url.split(\".\").pop().toLowerCase();\n      return [\"jpg\", \"jpeg\", \"png\", \"gif\", \"bmp\"].includes(fileSuffix);\n    },\n    isVideoUrl (url) {\n      if (!url) return false;\n      const fileSuffix = url.split(\".\").pop().toLowerCase();\n      return [\"mp4\", \"avi\", \"mov\", \"webm\"].includes(fileSuffix);\n    },\n    isAllowedFileUrl (url) {\n      if (!url) return false;\n      const fileSuffix = url.split(\".\").pop().toLowerCase();\n      return [\"pdf\", \"doc\", \"docx\", \"xls\", \"xlsx\", \"ppt\", \"pptx\"].includes(fileSuffix);\n    },\n\n    del (id) {\n      del(id).then((res) => {\n        this.$message({\n          type: \"success\",\n          message: this.$i18n.t(\"public.deleteSuccess\"),\n        });\n        this.getList();\n      });\n    },\n    editUrl (data) {\n      this.fileList = [data];\n      this.addForm.path = data.url;\n    },\n    close () {\n      if (this.uploading) return;\n      this.addForm = {\n        name: \"\",\n        type: 1,\n        path: \"\",\n      };\n      this.fileList = [];\n    },\n    //搜索\n    searchForm () {\n      this.getList();\n    },\n    //重置\n    resetForm () {\n      this.pageNum = 1;\n      this.$refs.form.resetFields();\n      this.getList();\n    },\n    handleSizeChange (val) {\n      this.pageNum = 1;\n      this.pageSize = val;\n      this.getList();\n    },\n    handleCurrentChange (val) {\n      this.pageNum = val;\n      this.getList();\n    },\n    getTypeName (type) {\n      const types = {\n        1: this.$t(\"material.table.image\"),\n        2: this.$t(\"material.table.video\"),\n        3: this.$t(\"material.table.file\")\n      };\n      return types[type] || \"-\";\n    },\n    getFileIconClass (url) {\n      if (!url) return \"el-icon-document\";\n      const ext = url.split(\".\").pop().toLowerCase();\n      switch (ext) {\n        case \"pdf\":\n          return \"el-icon-document-pdf\";\n        case \"doc\":\n        case \"docx\":\n        case \"xls\":\n        case \"xlsx\":\n        case \"ppt\":\n        case \"pptx\":\n          return \"el-icon-document\";\n        case \"jpg\":\n        case \"jpeg\":\n        case \"png\":\n        case \"gif\":\n        case \"bmp\":\n          return \"el-icon-picture\";\n        case \"mp4\":\n        case \"avi\":\n        case \"mov\":\n        case \"webm\":\n          return \"el-icon-video-camera\";\n        default:\n          return \"el-icon-document\";\n      }\n    },\n    getFileName (url) {\n      if (!url) return \"\";\n      return url.split(\"/\").pop();\n    },\n    getFileType (type) {\n      switch (type) {\n        case 1: return \"image\";\n        case 2: return \"video\";\n        case 3: return \"file\";\n        default: return \"image\";\n      }\n    }\n  },\n};\n</script>\n\n<style scoped>\n.flex {\n  display: flex;\n}\n\n.img {\n  width: 60px;\n  height: 60px;\n}\n\n.file-preview {\n  width: 60px;\n  height: 60px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  border: 1px dashed #eaeaea;\n  border-radius: 5px;\n\n  i {\n    font-size: 24px;\n    color: #409EFF;\n    margin-bottom: 5px;\n  }\n\n  .file-name {\n    max-width: 50px;\n    text-align: center;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n    font-size: 10px;\n    color: #606266;\n  }\n}\n</style>", "import mod from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Material.vue?vue&type=script&lang=js\"; export default mod; export * from \"-!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js??clonedRuleSet-40.use[1]!../../node_modules/@vue/vue-loader-v15/lib/index.js??vue-loader-options!./Material.vue?vue&type=script&lang=js\"", "import { render, staticRenderFns } from \"./Material.vue?vue&type=template&id=72891219&scoped=true\"\nimport script from \"./Material.vue?vue&type=script&lang=js\"\nexport * from \"./Material.vue?vue&type=script&lang=js\"\nimport style0 from \"./Material.vue?vue&type=style&index=0&id=72891219&prod&scoped=true&lang=css\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/@vue/vue-loader-v15/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"72891219\",\n  null\n  \n)\n\nexport default component.exports", "import request, { postBlob, getBlob, handleImport } from '@/utils/request'\n\nexport function getList(params) {\n  return request({\n    url: `/admin/sourcematerial/getList`,\n    method: 'get',\n    params\n  })\n}\n\nexport function add(data) {\n  return request({\n    url: `/admin/sourcematerial/addSourceMaterial`,\n    method: 'post',\n    data\n  })\n}\n\nexport function edit(data,id) {\n  return request({\n    url: `/admin/sourcematerial/editSourceMaterial/` + id,\n    method: 'put',\n    data\n  })\n}\n\nexport function del(id) {\n  return request({\n    url: `/admin/sourcematerial/delete/` + id,\n    method: 'delete'\n  })\n}"], "names": ["render", "_vm", "this", "_c", "_self", "ref", "staticClass", "class", "name", "attrs", "upload_host", "headers", "uploadNum", "beforeUpload", "handleRemove", "handleUploadProgress", "handleUploadSuccess", "isDisabled", "acceptFileType", "_l", "fileList", "item", "index", "key", "on", "$event", "hover", "isImageUrl", "url", "refInFor", "staticStyle", "imageUrl", "previewImages", "stopPropagation", "handleClickItem", "apply", "arguments", "isVideoUrl", "getFileIconClass", "_v", "_s", "getFileNameFromUrl", "directives", "rawName", "value", "expression", "canPreview", "previewFile", "downloadFile", "_e", "uploadImage", "deleteImage", "length", "staticRenderFns", "props", "Boolean", "fileType", "String", "type", "default", "Array", "Number", "data", "process", "Authorization", "store", "getters", "token", "showImage", "dialogVisible", "uploadImageId", "list", "computed", "showFileList", "get", "set", "newValue", "methods", "emitInput", "val", "$emit", "setTimeout", "domImageMask", "document", "querySelector", "addEventListener", "e", "target", "parentNode", "className", "click", "file", "getUUID", "replace", "c", "Math", "random", "toString", "isValidType", "checkFileType", "handleInvalidType", "push", "$message", "warning", "$i18n", "t", "count", "resetUploadState", "includes", "reader", "FileReader", "onload", "txt", "result", "img", "createElement", "src", "width", "height", "console", "log", "readAsDataURL", "videoElement", "URL", "createObjectURL", "videoWidth", "videoHeight", "allowedTypes", "split", "map", "trim", "warningMsg", "getWarningMessage", "$refs", "uploadRef", "clearFiles", "res", "response", "file_name", "images", "for<PERSON>ach", "fullUrl", "previewImage", "findElem", "showViewer", "previewVideo", "window", "open", "fileSuffix", "substring", "lastIndexOf", "toLowerCase", "whiteList", "videoSuffix", "suffix", "ext", "pop", "id", "$el", "splice", "submitUpload", "submit", "component", "form", "$t", "model", "callback", "$$v", "$set", "date", "add", "resetForm", "searchForm", "isLoading", "dataList", "scopedSlots", "_u", "fn", "scope", "$index", "getTypeName", "row", "path", "getFileName", "$formatTimeStamp", "created_at", "edit", "del", "slot", "pageNum", "pageSize", "total", "handleSizeChange", "handleCurrentChange", "isEdit", "isShow", "close", "addForm", "rules", "changeType", "getFileType", "uploadStatus", "editUrl", "fileData", "uploading", "save", "components", "fileUpload", "upfileData", "required", "message", "trigger", "created", "getList", "obj", "status", "page", "created_at_start", "created_at_end", "then", "code", "validate", "valid", "error", "fileUrl", "isAllowedFileUrl", "formData", "source_width", "source_height", "resetFields", "types", "params", "request", "method"], "sourceRoot": ""}